<?xml version="1.0" encoding="utf-8"?><!--
 * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
-->

<permissions>
    <privapp-permissions package="com.thundercomm.eventprovider">
        <permission name="android.permission.INSTALL_PACKAGE_UPDATES" />
        <permission name="android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS" />
        <permission name="android.permission.INTERACT_ACROSS_USERS" />
        <permission name="android.permission.INTERACT_ACROSS_USERS_FULL" />
        <permission name="android.permission.NFC" />
        <permission name="android.permission.BIND_NFC_SERVICE" />
        <permission name="android.permission.READ_PHONE_STATE" />
        <permission name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
        <permission name="android.permission.INSTALL_PACKAGES" />
        <permission name="android.permission.DELETE_PACKAGES" />
        <permission name="android.permission.REQUEST_DELETE_PACKAGES" />
        <permission name="android.permission.QUERY_ALL_PACKAGES" />
        <permission name="android.permission.REBOOT" />
        <permission name="android.permission.SHUTDOWN" />
        <permission name="android.permission.BLUETOOTH" />
        <permission name="android.permission.BLUETOOTH_ADMIN" />
        <permission name="android.permission.BLUETOOTH_CONNECT" />
        <permission name="android.permission.WRITE_APN_SETTINGS" />
        <permission name="android.permission.WRITE_SECURE_SETTINGS" />
        <permission name="android.permission.DEVICE_POWER" />
    </privapp-permissions>
</permissions>
