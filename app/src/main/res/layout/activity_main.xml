<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="EventProvider Test Interface"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="20dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Device Detection Test"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="10dp" />

        <Button
            android:id="@+id/btn_start_detection_test"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Start Detection Test"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_simulate_normal_response"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Simulate Normal Response (IMU)"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_simulate_abnormal_response"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Simulate Abnormal Response (Camera Error)"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_simulate_response_with_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Simulate Abnormal Response (With Data Field)"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_test_timeout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Timeout Mechanism"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_test_field_validation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Field Validation"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_complete_flow_test"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Complete Flow Test"
            android:layout_marginBottom="20dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Other Tests"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="10dp" />

        <Button
            android:id="@+id/btn_test_sdcard_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test SD Card Error"
            android:layout_marginBottom="8dp" />

    </LinearLayout>

</ScrollView>