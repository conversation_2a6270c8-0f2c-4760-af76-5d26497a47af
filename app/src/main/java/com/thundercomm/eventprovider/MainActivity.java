/*
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 *  Not a contribution.
 */
package com.thundercomm.eventprovider;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;
import android.view.KeyEvent;

import java.io.UnsupportedEncodingException;

import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.PayloadType;

import org.json.JSONException;
import org.json.JSONObject;

import vendor.thundercomm.eventbroker.EventBody;
import vendor.thundercomm.eventbroker.EventEntry;
import vendor.thundercomm.eventbroker.EventHeader;
import vendor.thundercomm.eventbroker.IEventBroker;
import vendor.thundercomm.eventbroker.IEventObserver;

public class MainActivity extends Activity {

    IEventBrokerConnection mEventBrokerConnection = new IEventBrokerConnection();

    // Test related variables
    private IEventBroker mEventBroker;

    // Constants
    private static final int EVENT_LIFETIME_ONE_HOUR = 36000; // 1 hour in seconds
    private static final int TEST_DELAY_2_SECONDS = 2000; // 2 seconds
    private static final int TEST_DELAY_5_SECONDS = 5000; // 5 seconds
    private static final int TEST_DELAY_8_SECONDS = 8000; // 8 seconds
    private static final String TEST_TEMPERATURE_VALUE = "85.5";
    private static final int TEST_ERROR_CODE_92 = 92;
    private static final int TEST_ERROR_CODE_90 = 90;
    private static final int TEST_ERROR_CODE_13 = 13;
    private static final int PAYLOAD_TYPE_NATIVE = 1;
    private static final byte CATEGORY_ID_TRANSITION = 1;
    private static final int TYPE_ID_ACCELEROMETER = 2;
    private static final byte CATEGORY_ID_LED_CONTROL = 12;
    private static final int INTERFACE_VERSION_DEFAULT = 0;
    private static final int DEVICE_STATE_NORMAL = 0;
    private static final int DEVICE_STATE_ABNORMAL = 1;
    private static final int TEST_ERROR_CODE_2 = 2;
    private static final int TEST_ERROR_CODE_3 = 3;
    private static final int TEST_ERROR_CODE_5 = 5;
    private static final int TEST_ERROR_CODE_15 = 15;
    private static final int TEST_HW_ID_3 = 3;
    private static final int[] TEST_HW_IDS = {
        EventType.HardwareRuntimeError.HwId.G_SENSOR,      // 3
        EventType.HardwareRuntimeError.HwId.FRONT_CAMERA,  // 5
        EventType.HardwareRuntimeError.HwId.SD_CARD,       // 9
        EventType.HardwareRuntimeError.HwId.BT,            // 12
        EventType.HardwareRuntimeError.HwId.TEMPERATURE    // 15
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Setup test buttons
        setupTestButtons();

        bindEventBroker();
    }

    private void bindEventBroker() {
        Intent intent = new Intent("com.thundercomm.brokeragent.SERVICE");
        intent.setClassName("vendor.thundercomm.brokeragent", "vendor.thundercomm.brokeragent.BrokerAgentService");
        bindService(intent, mEventBrokerConnection, BIND_AUTO_CREATE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unbindService(mEventBrokerConnection);
    }

    private class IEventBrokerConnection implements ServiceConnection {
        /**
         * Called when a connection to the Service has been established, with
         * the {@link IBinder} of the communication channel to the
         * Service.
         *
         * <p class="note"><b>Note:</b> If the system has started to bind your
         * client app to a service, it's possible that your app will never receive
         * this callback. Your app won't receive a callback if there's an issue with
         * the service, such as the service crashing while being created.
         *
         * @param name    The concrete component name of the service that has
         *                been connected.
         * @param service The IBinder of the Service's communication channel,
         *                which you can now make calls on.
         */
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mEventBroker = IEventBroker.Stub.asInterface(service);
            if (mEventBroker == null) {
                EventProviderApp.LOG.e("onServiceConnected: IEventBroker is null, cannot publish event");
                return;
            }
            EventProviderApp.LOG.d("onServiceConnected: IEventBroker is connected");
            try {
                EventObserver eventObserver = new EventObserver();
                mEventBroker.registerEventObserver(eventObserver, CATEGORY_ID_TRANSITION, TYPE_ID_ACCELEROMETER);
                mEventBroker.registerEventObserver(eventObserver, CATEGORY_ID_LED_CONTROL, PAYLOAD_TYPE_NATIVE);
                EventProviderApp.LOG.d("onServiceConnected: Event observer registered successfully");
            } catch (RemoteException ex) {
                EventProviderApp.LOG.e("event broker call failed", ex);
            }

            EventBody eventBody = new EventBody();
            eventBody.name = "accelerometer";
            eventBody.payloadType = PAYLOAD_TYPE_NATIVE;
            try {
                byte[] bytes = "accelerometer".getBytes("utf-8");
                eventBody.payloadSize = bytes.length;
                eventBody.payloadData = bytes;
            } catch (UnsupportedEncodingException ex) {
                EventProviderApp.LOG.e("Failed to encode payload data", ex);
            }

            EventHeader eventHeader = new EventHeader();
            eventHeader.categoryId = CATEGORY_ID_TRANSITION;
            eventHeader.typeId = TYPE_ID_ACCELEROMETER;
            eventHeader.timestamp = System.currentTimeMillis();
            eventHeader.lifetime = EVENT_LIFETIME_ONE_HOUR;

            EventEntry eventEntry = new EventEntry();
            eventEntry.body = eventBody;
            eventEntry.header = eventHeader;
            try {
                mEventBroker.publishEvent(eventEntry);
            } catch (RemoteException ex) {
                EventProviderApp.LOG.e("Failed to publish event", ex);
            }

            // Test SD card error (optional, for testing SD card detection functionality)
            // sendTestSdCardError(mEventBroker);
        }

        /**
         * Called when a connection to the Service has been lost.  This typically
         * happens when the process hosting the service has crashed or been killed.
         * This does <em>not</em> remove the ServiceConnection itself -- this
         * binding to the service will remain active, and you will receive a call
         * to {@link #onServiceConnected} when the Service is next running.
         *
         * @param name The concrete component name of the service whose
         *             connection has been lost.
         */
        @Override
        public void onServiceDisconnected(ComponentName name) {

        }

        /**
         * Called when the binding to this connection is dead.  This means the
         * interface will never receive another connection.  The application will
         * need to unbind and rebind the connection to activate it again.  This may
         * happen, for example, if the application hosting the service it is bound to
         * has been updated.
         *
         * <p class="note"><b>Note:</b> The app that requested the binding must call
         *
         * @param name The concrete component name of the service whose connection is dead.
         */
        @Override
        public void onBindingDied(ComponentName name) {
            ServiceConnection.super.onBindingDied(name);
        }
    }

    /**
     * Create event entry with JSON payload.
     * @param eventName Event name
     * @param jsonPayload JSON payload object
     * @param categoryId Event category ID
     * @param typeId Event type ID
     * @return EventEntry or null if creation failed
     */
    private EventEntry createJsonEventEntry(String eventName, JSONObject jsonPayload,
                                          byte categoryId, int typeId) {
        try {
            // Create event body
            EventBody eventBody = new EventBody();
            eventBody.name = eventName;
            eventBody.payloadType = PayloadType.JSON.getType();

            try {
                byte[] bytes = jsonPayload.toString().getBytes("utf-8");
                eventBody.payloadSize = bytes.length;
                eventBody.payloadData = bytes;
            } catch (UnsupportedEncodingException ex) {
                EventProviderApp.LOG.e("createJsonEventEntry: Failed to encode payload data", ex);
                return null;
            }

            EventEntry eventEntry = new EventEntry();
            eventEntry.body = eventBody;

            // Create event header
            EventHeader eventHeader = new EventHeader();
            eventHeader.categoryId = categoryId;
            eventHeader.typeId = typeId;
            eventHeader.timestamp = System.currentTimeMillis();
            eventHeader.lifetime = EVENT_LIFETIME_ONE_HOUR;
            eventEntry.header = eventHeader;

            return eventEntry;
        } catch (Exception ex) {
            EventProviderApp.LOG.e("createJsonEventEntry: Failed to create event entry", ex);
            return null;
        }
    }

    /**
     * Create event entry with string payload.
     * @param eventName Event name
     * @param stringPayload String payload
     * @param categoryId Event category ID
     * @param typeId Event type ID
     * @return EventEntry or null if creation failed
     */
    private EventEntry createStringEventEntry(String eventName, String stringPayload,
                                            byte categoryId, int typeId) {
        try {
            // Create event body
            EventBody eventBody = new EventBody();
            eventBody.name = eventName;
            eventBody.payloadType = PayloadType.NATIVE.getType();

            try {
                byte[] bytes = stringPayload.getBytes("utf-8");
                eventBody.payloadSize = bytes.length;
                eventBody.payloadData = bytes;
            } catch (UnsupportedEncodingException ex) {
                EventProviderApp.LOG.e("createStringEventEntry: Failed to encode payload data", ex);
                return null;
            }

            EventEntry eventEntry = new EventEntry();
            eventEntry.body = eventBody;

            // Create event header
            EventHeader eventHeader = new EventHeader();
            eventHeader.categoryId = categoryId;
            eventHeader.typeId = typeId;
            eventHeader.timestamp = System.currentTimeMillis();
            eventHeader.lifetime = EVENT_LIFETIME_ONE_HOUR;
            eventEntry.header = eventHeader;

            return eventEntry;
        } catch (Exception ex) {
            EventProviderApp.LOG.e("createStringEventEntry: Failed to create event entry", ex);
            return null;
        }
    }

    /**
     * Publish event entry.
     * @param eventBroker Event broker
     * @param eventEntry Event entry to publish
     * @param methodName Method name for logging
     * @param successMessage Success message for logging
     * @return true if published successfully, false otherwise
     */
    private boolean publishEventEntry(IEventBroker eventBroker, EventEntry eventEntry,
                                    String methodName, String successMessage) {
        if (eventBroker == null) {
            EventProviderApp.LOG.e(methodName + ": IEventBroker is null, cannot publish event");
            return false;
        }

        if (eventEntry == null) {
            EventProviderApp.LOG.e(methodName + ": EventEntry is null, cannot publish event");
            return false;
        }

        try {
            eventBroker.publishEvent(eventEntry);
            EventProviderApp.LOG.d(methodName + ": " + successMessage);
            return true;
        } catch (RemoteException ex) {
            EventProviderApp.LOG.e(methodName + ": Failed to publish event", ex);
            return false;
        }
    }

    /**
     * Test sending SD card read/write failure event.
     * Used to trigger SD card health detection logic.
     */
    private void sendTestSdCardError(IEventBroker eventBroker) {
        try {
            // Create data object
            JSONObject dataObject = new JSONObject();
            dataObject.put("error_type", "read_write_fail");
            dataObject.put("test_trigger", true);
            dataObject.put("timestamp", System.currentTimeMillis());

            // Create main JSON object (consistent with EventMonitorService format)
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", EventType.HardwareRuntimeError.ErrorCode.READ_WRITE_FAIL);
            jsonObject.put("hwErrorCode", EventType.HardwareRuntimeError.HwErrorCode.READ_WRITE_FAIL);
            jsonObject.put("data", dataObject);

            EventEntry eventEntry = createJsonEventEntry(
                "sdcard_read_write_error",
                jsonObject,
                EventCategory.RUNTIME_ERROR,
                EventType.HardwareRuntimeError.HwId.SD_CARD
            );

            publishEventEntry(eventBroker, eventEntry, "sendTestSdCardError",
                            "SD card error test event published successfully");

        } catch (JSONException ex) {
            EventProviderApp.LOG.e("sendTestSdCardError: Failed to create test data JSON", ex);
        }
    }

    private static class EventObserver extends IEventObserver.Stub {
        @Override
        public void onEventReceived(EventEntry eventEntry) throws RemoteException {
            Log.d("EventProvider", "onEventReceived: Event received - " + eventEntry.body.name);
        }

        @Override
        public int getInterfaceVersion() throws RemoteException {
            return INTERFACE_VERSION_DEFAULT;
        }

        @Override
        public String getInterfaceHash() throws RemoteException {
            return "";
        }
    }

    /**
     * Setup test buttons.
     */
    private void setupTestButtons() {
        findViewById(R.id.btn_start_detection_test).setOnClickListener(v -> {
            if (mEventBroker != null) {
                sendTestDetectionCommand(mEventBroker);
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });

        findViewById(R.id.btn_simulate_normal_response).setOnClickListener(v -> {
            if (mEventBroker != null) {
                sendTestNormalResponse(mEventBroker, EventType.HardwareRuntimeError.HwId.G_SENSOR);
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });

        findViewById(R.id.btn_simulate_abnormal_response).setOnClickListener(v -> {
            if (mEventBroker != null) {
                sendTestAbnormalResponse(mEventBroker,
                        EventType.HardwareRuntimeError.HwId.SD_CARD, TEST_ERROR_CODE_2, TEST_ERROR_CODE_92);
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });

        findViewById(R.id.btn_simulate_response_with_data).setOnClickListener(v -> {
            if (mEventBroker != null) {
                sendTestAbnormalResponseWithData(mEventBroker);
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });

        findViewById(R.id.btn_test_timeout).setOnClickListener(v -> {
            if (mEventBroker != null) {
                sendTestDetectionCommand(mEventBroker);
                EventProviderApp.LOG.d("MainActivity: Timeout test started - wait 1 minute for timeout");
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });

        findViewById(R.id.btn_test_field_validation).setOnClickListener(v -> {
            if (mEventBroker != null) {
                sendTestInvalidResponse(mEventBroker);
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });

        findViewById(R.id.btn_complete_flow_test).setOnClickListener(v -> {
            if (mEventBroker != null) {
                runCompleteFlowTest(mEventBroker);
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });

        findViewById(R.id.btn_test_sdcard_error).setOnClickListener(v -> {
            if (mEventBroker != null) {
                sendTestSdCardError(mEventBroker);
            } else {
                EventProviderApp.LOG.w("MainActivity: EventBroker not connected");
            }
        });
    }

    /**
     * Test sending device detection command.
     */
    private void sendTestDetectionCommand(IEventBroker eventBroker) {
        EventEntry eventEntry = createStringEventEntry(
            "checkdevice",
            "checkdevice",
            EventCategory.CATEGORY_TOOL_EVENT,
            EventType.CategoryTool.START_DETECTION
        );

        publishEventEntry(eventBroker, eventEntry, "sendTestDetectionCommand",
                        "Detection command sent successfully");
    }

    /**
     * Simulate normal device response.
     */
    private void sendTestNormalResponse(IEventBroker eventBroker, int hwId) {
        try {
            // Build normal response JSON
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("state", DEVICE_STATE_NORMAL);     // Normal state
            jsonObject.put("hwid", hwId);   // Hardware ID

            EventEntry eventEntry = createJsonEventEntry(
                "device_response_normal",
                jsonObject,
                EventCategory.CATEGORY_TOOL_EVENT,
                EventType.CategoryTool.RECEIVED_TEST_RESULTS
            );

            publishEventEntry(eventBroker, eventEntry, "sendTestNormalResponse",
                            "Normal response sent for HW ID " + hwId);

        } catch (JSONException ex) {
            EventProviderApp.LOG.e("sendTestNormalResponse: Failed to create normal response JSON", ex);
        }
    }

    /**
     * Simulate abnormal device response.
     */
    private void sendTestAbnormalResponse(IEventBroker eventBroker, int hwId, int errorCode, int hwErrorCode) {
        try {
            // Build abnormal response JSON
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("state", DEVICE_STATE_ABNORMAL);                 // Abnormal state
            jsonObject.put("hwid", hwId);               // Hardware ID
            jsonObject.put("errorCode", errorCode);     // Error code
            jsonObject.put("hwErrorCode", hwErrorCode); // Hardware error code

            EventEntry eventEntry = createJsonEventEntry(
                "device_response_abnormal",
                jsonObject,
                EventCategory.CATEGORY_TOOL_EVENT,
                EventType.CategoryTool.RECEIVED_TEST_RESULTS
            );

            publishEventEntry(eventBroker, eventEntry, "sendTestAbnormalResponse",
                            "Abnormal response sent for HW ID " + hwId
                                    + " (errorCode: " + errorCode + ", hwErrorCode: " + hwErrorCode + ")");

        } catch (JSONException ex) {
            EventProviderApp.LOG.e("sendTestAbnormalResponse: Failed to create abnormal response JSON", ex);
        }
    }

    /**
     * Simulate abnormal response with data field.
     */
    private void sendTestAbnormalResponseWithData(IEventBroker eventBroker) {
        try {
            // Build data object
            JSONObject dataObject = new JSONObject();
            dataObject.put("state", DEVICE_STATE_ABNORMAL);
            dataObject.put("type", TYPE_ID_ACCELEROMETER);
            dataObject.put("temp", TEST_TEMPERATURE_VALUE);

            // Build abnormal response JSON
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("state", DEVICE_STATE_ABNORMAL);                 // Abnormal state
            jsonObject.put("hwid", EventType.HardwareRuntimeError.HwId.TEMPERATURE); // Temperature sensor
            jsonObject.put("errorCode", TEST_ERROR_CODE_5);             // Error code
            jsonObject.put("hwErrorCode", TEST_ERROR_CODE_90);       // Hardware error code
            jsonObject.put("data", dataObject);         // Include data field

            EventEntry eventEntry = createJsonEventEntry(
                "device_response_with_data",
                jsonObject,
                EventCategory.CATEGORY_TOOL_EVENT,
                EventType.CategoryTool.RECEIVED_TEST_RESULTS
            );

            publishEventEntry(eventBroker, eventEntry, "sendTestAbnormalResponseWithData",
                            "Abnormal response with data sent");

        } catch (JSONException ex) {
            EventProviderApp.LOG.e("sendTestAbnormalResponseWithData: Failed to create response with data JSON", ex);
        }
    }

    /**
     * Test field validation - send malformed responses.
     */
    private void sendTestInvalidResponse(IEventBroker eventBroker) {
        try {
            // Test 1: Missing required field state
            JSONObject invalidJson1 = new JSONObject();
            invalidJson1.put("hwid", TEST_HW_ID_3);
            sendInvalidResponseHelper(eventBroker, invalidJson1, "missing_state");

            // Test 2: Missing required field hwid
            JSONObject invalidJson2 = new JSONObject();
            invalidJson2.put("state", DEVICE_STATE_ABNORMAL);
            sendInvalidResponseHelper(eventBroker, invalidJson2, "missing_hwid");

            // Test 3: state=1 but missing errorCode
            JSONObject invalidJson3 = new JSONObject();
            invalidJson3.put("state", DEVICE_STATE_ABNORMAL);
            invalidJson3.put("hwid", TEST_HW_ID_3);
            invalidJson3.put("hwErrorCode", TEST_ERROR_CODE_13);
            sendInvalidResponseHelper(eventBroker, invalidJson3, "missing_errorCode");

            // Test 4: state=1 but missing hwErrorCode
            JSONObject invalidJson4 = new JSONObject();
            invalidJson4.put("state", DEVICE_STATE_ABNORMAL);
            invalidJson4.put("hwid", TEST_HW_ID_3);
            invalidJson4.put("errorCode", TEST_ERROR_CODE_3);
            sendInvalidResponseHelper(eventBroker, invalidJson4, "missing_hwErrorCode");

            EventProviderApp.LOG.d("sendTestInvalidResponse: All invalid response tests sent");

        } catch (JSONException ex) {
            EventProviderApp.LOG.e("sendTestInvalidResponse: Failed to create invalid response", ex);
        }
    }

    /**
     * Helper method for sending invalid responses.
     */
    private void sendInvalidResponseHelper(IEventBroker eventBroker, JSONObject jsonObject, String testName) {
        EventEntry eventEntry = createJsonEventEntry(
            "device_response_invalid_" + testName,
            jsonObject,
            EventCategory.CATEGORY_TOOL_EVENT,
            EventType.CategoryTool.RECEIVED_TEST_RESULTS
        );

        publishEventEntry(eventBroker, eventEntry, "sendInvalidResponseHelper",
                        "Invalid response sent for test: " + testName);
    }

    /**
     * Run complete flow test.
     */
    private void runCompleteFlowTest(IEventBroker eventBroker) {
        EventProviderApp.LOG.d("runCompleteFlowTest: Starting complete flow test");

        // Step 1: Send detection command
        sendTestDetectionCommand(eventBroker);

        // Step 2: Send some normal responses with delay
        new android.os.Handler().postDelayed(() -> {
            sendTestNormalResponse(eventBroker, EventType.HardwareRuntimeError.HwId.G_SENSOR);
            sendTestNormalResponse(eventBroker, EventType.HardwareRuntimeError.HwId.BT);
        }, TEST_DELAY_2_SECONDS);

        // Step 3: Send some abnormal responses with delay
        new android.os.Handler().postDelayed(() -> {
            sendTestAbnormalResponse(eventBroker, EventType.HardwareRuntimeError.HwId.FRONT_CAMERA,
                    TEST_ERROR_CODE_5, TEST_ERROR_CODE_15);
            sendTestAbnormalResponseWithData(eventBroker);
        }, TEST_DELAY_5_SECONDS);

        // Step 4: Send some invalid responses
        new android.os.Handler().postDelayed(() -> {
            sendTestInvalidResponse(eventBroker);
        }, TEST_DELAY_8_SECONDS);

        EventProviderApp.LOG.d("runCompleteFlowTest: Complete flow test scheduled. "
                + "Check logs for results. Remaining devices will timeout after 1 minute.");
    }
}