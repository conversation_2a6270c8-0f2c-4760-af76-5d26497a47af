/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.anno;

public class ActionConstants {
    /**
     * Action for BLE API.
     * Enable or Disable Bluetooth from an app.
     */
    public static final String ACTION_BLE = "yellowstone.system.BLUETOOTH_SET";
    /**
     * Action for Location API.
     * Enable or Disable Location Services from an app.
     */
    public static final String ACTION_LOCATION = "yellowstone.system.LOCATION_SET";
    /**
     * Change the volume from an app.
     */
    public static final String ACTION_SOUND_VOLUME = "yellowstone.system.SOUND_VOLUME";
    /**
     * ACTION for Reboot req.
     * Reset a specified target from an app.
     */
    public static final String ACTION_REBOOT_REQ = "yellowstone.system.REQ_REBOOT";
    /**
     * ACTION for Reboot res.
     * Notify the result after the target has restarted
     */
    public static final String ACTION_REBOOT_RES = "yellowstone.system.RES_REBOOT";

    /**
     * ACTION for FOTA update.
     * Send a command from an app to get the update file(FOTA) version.
     */
    public static final String ACTION_FOTA_CHECK_VERSION_REQ = "yellowstone.system.REQ_FOTA_CHECK_VERSION";
    /**
     * Notify the app of the latest version(FOTA).
     */
    public static final String ACTION_FOTA_CHECK_VERSION_RES = "yellowstone.system.RES_FOTA_CHECK_VERSION";
    /**
     * Send a command from an app to download an update file(FOTA).
     */
    public static final String ACTION_FOTA_DOWNLOAD_REQ = "yellowstone.system.REQ_FOTA_DOWNLOAD";
    /**
     * Notify an app about the download result(FOTA).
     */
    public static final String ACTION_FOTA_DOWNLOAD_RES = "yellowstone.system.RES_FOTA_DOWNLOAD";
    /**
     * Send a command from an app to install an update file(FOTA).
     */
    public static final String ACTION_FOTA_UPDATE_REQ = "yellowstone.system.REQ_FOTA_UPDATE";
    /**
     * Notify an app about the install result(FOTA).
     */
    public static final String ACTION_FOTA_UPDATE_RES = "yellowstone.system.RES_FOTA_UPDATE";
    /**
     * Send a command from an app to FW download and install.(FOTA)
     */
    public static final String ACTION_FOTA_YS_UPDATE_REQ = "yellowstone.system.REQ_FOTA_YS_UPDATE";
    /**
     * Notify an app about the FW install result(FOTA).
     */
    public static final String ACTION_FOTA_YS_UPDATE_RES = "yellowstone.system.RES_FOTA_YS_UPDATE";
    /**
     * Change APN setting from an app.
     * And apply the settings.
     */
    public static final String ACTION_APN_SET_REQ = "yellowstone.system.REQ_APN_SET";
    /**
     * Notify that the APN has been set and applied.
     */
    public static final String ACTION_APN_SET_RES = "yellowstone.system.RES_APN_SET";
    /**
     * Notify the occurred error.
     */
    public static final String ACTION_ERROR_INFO = "yellowstone.ssolapp.ERROR_INFO";
    /**
     * Trigger the Event Recorde.
     */
    public static final String ACTION_EVENT_REC_REQ = "yellowstone.system.REQ_EVENT_REC";
    /**
     * Notify when the Event Recorde is triggered.
     */
    public static final String ACTION_EVENT_REC_RES = "yellowstone.system.RES_EVENT_REC";

    /**
     * Enable or Disable Display from an app.(backlight and touch)
     */
    public static final String ACTION_DISPLAY_FUNC = "yellowstone.system.DISPLAY_FUNC";
    /**
     * Request to enter power-saving mode from an app.
     */
    public static final String ACTION_PWR_SAVE = "yellowstone.system.PWR_SAVE";
    /**
     * Request the version information of another app, firmware, etc., from an app.
     */
    public static final String ACTION_VER_REQ = "yellowstone.system.REQ_VER";
    /**
     * Return the target version as an array to the app.
     */
    public static final String ACTION_VER_RES = "yellowstone.system.RES_VER";
    /**
     * Request the hardware information of the dashcam.
     */
    public static final String ACTION_DVR_INFO_REQ = "yellowstone.system.REQ_DVR_INFO";
    /**
     * Return the dashcam hardware information request.
     */
    public static final String ACTION_DVR_INFO_RES = "yellowstone.system.RES_DVR_INFO";

    /**
     * Enable or Disable NFC from an app.
     */
    public static final String ACTION_NFC_SET = "yellowstone.system.NFC_SET";
    /**
     * Notify when ACC changes to ON or OFF.
     */
    public static final String ACTION_ACC_STATE_CHANGE = "yellowstone.system.ACC_STATE_CHANGE";
    /**
     * Request the wireless connection information.
     */
    public static final String ACTION_CONNECT_INFO_REQ = "yellowstone.system.REQ_CONNECT_INFO";
    /**
     * Return the wireless connection information request.
     */
    public static final String ACTION_CONNECT_INFO_RES = "yellowstone.system.RES_CONNECT_INFO";
    /**
     * When this Intent is received, the system will save the logcat output to the SD card.
     */
    public static final String ACTION_SAVE_LOGS = "yellowstone.system.SAVE_LOGS";
    /**
     * Notify when waking up from power-saving mode.
     */
    public static final String ACTION_SYS_WAKEUP = "yellowstone.system.SYS_WAKEUP";
    /**
     * Request information related to the current mobile network connection.
     */
    public static final String ACTION_NETWORK_INFO_REQ = "yellowstone.system.REQ_NETWORK_INFO";
    /**
     * Return information related to the current mobile network connection.
     */
    public static final String ACTION_NETWORK_INFO_RES = "yellowstone.system.RES_NETWORK_INFO";
    /**
     * Request to uninstall an app.
     */
    public static final String ACTION_UNINSTALL_REQ = "yellowstone.system.REQ_UNINSTALL";
    /**
     * Notify the result of the uninstallation.
     */
    public static final String ACTION_UNINSTALL_RES = "yellowstone.system.RES_UNINSTALL";
    /**
     * Request to check whether the app is installed.
     */
    public static final String ACTION_CHECK_INSTALLED_REQ = "yellowstone.system.REQ_CHECK_INSTALLED";
    /**
     * Notify the result of checking  whether the app is installed.
     */
    public static final String ACTION_CHECK_INSTALLED_RES = "yellowstone.system.RES_CHECK_INSTALLED";

    // tc actions
    /**
     * TC Action for start record request.
     */
    public static final String TC_ACTION_EVENT_REC_REQ = "vendor.thundercomm.REQ_EVENT_REC";
    /**
     * TC Action for start record response.
     */
    public static final String TC_ACTION_EVENT_REC_RES = "vendor.thundercomm.RES_EVENT_REC";

    // signature values (SHA-256)
    public static final String SIGNATURE_SSOL = "66bbafcc11e6a7f06d4c1cd2b21e95269d840f3f2b5b710691a758f0ab16df35";

    // action extra keys
    /**
     * The extra key for the target name.
     */
    public static final String EXTRA_KEY_TARGET_NAME = "KEY_TARGET_NAME";
    /**
     * The extra key for the result code.
     */
    public static final String EXTRA_KEY_RESULT_CODE = "KEY_RESULT_CODE";
    /**
     * The extra key for the target version.
     */
    public static final String EXTRA_KEY_TARGET_VER = "KEY_TARGET_VER";
    /**
     * The extra key for the device serial no.
     */
    public static final String EXTRA_KEY_SERIAL_NO = "KEY_SERIAL_NO";
    /**
     * The extra key for the status.
     */
    public static final String EXTRA_KEY_STATUS = "KEY_STATUS";
    /**
     * The extra key for the value.
     */
    public static final String EXTRA_KEY_VALUE = "KEY_VALUE";

    // ErrorInfoSender constants
    /**
     * The extra key for error code in error info broadcast.
     */
    public static final String EXTRA_KEY_ERROR_CODE = "KEY_ERROR_CODE";
    /**
     * The extra key for error time in error info broadcast.
     */
    public static final String EXTRA_KEY_ERROR_TIME = "KEY_ERROR_TIME";
    /**
     * The extra key for error summary in error info broadcast.
     */
    public static final String EXTRA_KEY_ERROR_SUMMARY = "KEY_ERROR_SUMMARY";

    // Broadcast permissions
    /**
     * Permission required to receive broadcasts sent by EventProvider senders.
     * Receiving applications must declare this permission in their AndroidManifest.xml:
     * <uses-permission android:name="com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS" />
     */
    public static final String PERMISSION_RECEIVE_BROADCASTS
            = "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS";
}
