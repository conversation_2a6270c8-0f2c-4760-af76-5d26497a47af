/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.anno;

public class EventCategory {
    public static final byte RECORD = 0x01;  // Record events
    public static final byte TRANSITION = 0x02;  // Transition events
    public static final byte RUNTIME_ERROR = 0x03;  // Runtime error events
    public static final byte LED_CONTROL = 0x04;  // LED control events
    public static final byte CONTROLLER = 0x05;  // Device control events (like power off/reboot)
    public static final byte CATEGORY_TOOL_EVENT = 0x06;
}
