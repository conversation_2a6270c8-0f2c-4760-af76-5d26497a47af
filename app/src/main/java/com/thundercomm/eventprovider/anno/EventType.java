/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.anno;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public class EventType {

    public static class Controller {
        public static final int POWER_OFF = 0x01;
        public static final String POWER_OFF_NAME = "power_off";
        public static final int REBOOT = 0x02;
        public static final String REBOOT_NAME = "reboot";
    }

    public static class Record {
        public static final int ACC = 0x01;
        public static final String ACC_NAME = "acc_recording";
        public static final int COLLISION = 0x02;
        public static final String COLLISION_NAME = "collision_recording";
        public static final int PARKING = 0x03;
        public static final String PARKING_NAME = "parking_recording";

    }

    public static class Transition {
        public static final int ACC = 0x01; // acc_state event
        public static final String ACC_NAME = "acc_state";
        public static final int BT = 0x02; // bt_state event
        public static final String BT_NAME = "bt_state";
        public static final int WIFI = 0x03; // wifi_state event
        public static final String WIFI_NAME = "wifi_state";
        public static final int CALLING = 0x04; // calling_state event
        public static final String CALLING_NAME = "calling_state";
        public static final int GNSS = 0x05; // gnss_state event
        public static final String GNSS_NAME = "gnss_state";
        public static final int AGNSS = 0x06; // agnss_state event
        public static final String AGNSS_NAME = "agnss_state";
        public static final int OS_UPGRADE = 0x07; // os_upgrade_state
        public static final String OS_UPGRADE_NAME = "os_upgrade_state";
        public static final int APP_UPGRADE = 0x08; // app_upgrade_state
        public static final String APP_UPGRADE_NAME = "app_upgrade_state";
    }

    public static class CategoryTool {
        public static final int START_DETECTION = 0x01; // ACC state event
        public static final int RECEIVED_TEST_RESULTS = 0x02; // BT state event
    }

    public enum HardwareRuntimeError {
        // Enumeration value definitions
        // MCU
        MCU_NO_RESPONSE(HwId.MCU, ErrorCode.MCU_NO_RESPONSE, HwErrorCode.MCU_NO_RESPONSE),

        // LED
        LED_CONTROL_UNAVAILABLE(HwId.LED, ErrorCode.LED_CONTROL_UNAVAILABLE, HwErrorCode.LED_CONTROL_UNAVAILABLE),

        // IMU
        G_SENSOR_UNAVAILABLE(HwId.G_SENSOR, ErrorCode.G_SENSOR_UNAVAILABLE, HwErrorCode.G_SENSOR_UNAVAILABLE),
        GYRO_SENSOR_UNAVAILABLE(HwId.GYRO_SENSOR, ErrorCode.GYRO_SENSOR_UNAVAILABLE,
                HwErrorCode.GYRO_SENSOR_UNAVAILABLE),

        // Front Camera
        FRONT_CAMERA_UNAVAILABLE(HwId.FRONT_CAMERA, ErrorCode.CAMERA_UNAVAILABLE, HwErrorCode.FRONT_CAMERA_UNAVAILABLE),
        FRONT_CAMERA_NO_SIGNAL(HwId.FRONT_CAMERA, ErrorCode.CAMERA_NO_SIGNAL, HwErrorCode.FRONT_CAMERA_NO_SIGNAL),

        // Incabin Camera
        INCABIN_CAMERA_UNAVAILABLE(HwId.INCABIN_CAMERA, ErrorCode.CAMERA_UNAVAILABLE,
                HwErrorCode.INCABIN_CAMERA_UNAVAILABLE),
        INCABIN_CAMERA_NO_SIGNAL(HwId.INCABIN_CAMERA, ErrorCode.CAMERA_NO_SIGNAL, HwErrorCode.INCABIN_CAMERA_NO_SIGNAL),

        // Rear Camera
        REAR_CAMERA_NOT_DETECTED(HwId.REAR_CAMERA, ErrorCode.CAMERA_NOT_DETECTED, HwErrorCode.REAR_CAMERA_NOT_DETECTED),
        REAR_CAMERA_UNAVAILABLE(HwId.REAR_CAMERA, ErrorCode.CAMERA_UNAVAILABLE, HwErrorCode.REAR_CAMERA_UNAVAILABLE),
        REAR_CAMERA_NO_SIGNAL(HwId.REAR_CAMERA, ErrorCode.CAMERA_NO_SIGNAL, HwErrorCode.REAR_CAMERA_NO_SIGNAL),

        // Option Camera
        OPTION_CAMERA_NOT_DETECTED(HwId.OPTION_CAMERA, ErrorCode.CAMERA_NOT_DETECTED,
                HwErrorCode.OPTION_CAMERA_NOT_DETECTED),
        OPTION_CAMERA_UNAVAILABLE(HwId.OPTION_CAMERA, ErrorCode.CAMERA_UNAVAILABLE,
                HwErrorCode.OPTION_CAMERA_UNAVAILABLE),
        OPTION_CAMERA_NO_SIGNAL(HwId.OPTION_CAMERA, ErrorCode.CAMERA_NO_SIGNAL, HwErrorCode.OPTION_CAMERA_NO_SIGNAL),

        // SD Card
        NO_SD_CARD(HwId.SD_CARD, ErrorCode.NO_SD_CARD, HwErrorCode.NO_SD_CARD),
        SD_CARD_UNAVAILABLE(HwId.SD_CARD, ErrorCode.SD_CARD_UNAVAILABLE, HwErrorCode.SD_CARD_UNAVAILABLE),
        SD_CARD_READ_WRITE_FAIL(HwId.SD_CARD, ErrorCode.READ_WRITE_FAIL, HwErrorCode.READ_WRITE_FAIL),

        // LTE Module
        NO_SIM_CARD(HwId.LTE_MODULE, ErrorCode.NO_SIM_CARD, HwErrorCode.NO_SIM_CARD),
        LTE_MODULE_UNAVAILABLE(HwId.LTE_MODULE, ErrorCode.LTE_MODULE_UNAVAILABLE, HwErrorCode.LTE_MODULE_UNAVAILABLE),
        LTE_NETWORK_UNAVAILABLE(HwId.LTE_MODULE, ErrorCode.LTE_NETWORK_UNAVAILABLE,
                HwErrorCode.LTE_NETWORK_UNAVAILABLE),

        // GNSS
        GNSS_FAIL_UNAVAILABLE(HwId.GNSS, ErrorCode.GNSS_FAIL_UNAVAILABLE, HwErrorCode.GNSS_FAIL_UNAVAILABLE),
        DR_ALGORITHM_UNAVAILABLE(HwId.GNSS, ErrorCode.DR_ALGORITHM_UNAVAILABLE, HwErrorCode.DR_ALGORITHM_UNAVAILABLE),

        // BT
        BT_UNAVAILABLE(HwId.BT, ErrorCode.BT_UNAVAILABLE, HwErrorCode.BT_UNAVAILABLE),

        // WiFi
        WIFI_UNAVAILABLE(HwId.WIFI, ErrorCode.WIFI_UNAVAILABLE, HwErrorCode.WIFI_UNAVAILABLE),

        // I/F Box Unit
        IF_BOX_UNIT_UNAVAILABLE(HwId.IF_BOX_UNIT, ErrorCode.IF_BOX_UNIT_UNAVAILABLE,
                HwErrorCode.IF_BOX_UNIT_UNAVAILABLE),

        // Temperature
        CPU_HIGH_TEMPERATURE(HwId.TEMPERATURE, ErrorCode.CPU_HIGH_TEMPERATURE, HwErrorCode.CPU_HIGH_TEMPERATURE),

        // Voltage
        BATTERY_LOW_POWER(HwId.VOLTAGE, ErrorCode.BATTERY_LOW_POWER, HwErrorCode.BATTERY_LOW_POWER);

    public static final byte CATEGORY_TOOL_EVENT = 0x06; // Tool event category

        public static class HwId {
            public static final int MCU = 1;
            public static final int LED = 2;
            public static final int G_SENSOR = 3; // G Sensor (IMU)
            public static final int GYRO_SENSOR = 4; // Gyro Sensor (IMU)
            public static final int FRONT_CAMERA = 5;
            public static final int INCABIN_CAMERA = 6;
            public static final int REAR_CAMERA = 7;
            public static final int OPTION_CAMERA = 8;
            public static final int SD_CARD = 9;
            public static final int LTE_MODULE = 10;
            public static final int GNSS = 11;
            public static final int BT = 12;
            public static final int WIFI = 13;
            public static final int IF_BOX_UNIT = 14;
            public static final int TEMPERATURE = 15;
            public static final int VOLTAGE = 16;

            public static final Set<Integer> DETECTABLE_HW_IDS = Collections.unmodifiableSet(
                new HashSet<>(Arrays.asList(
                    MCU, LED, G_SENSOR, GYRO_SENSOR, FRONT_CAMERA, INCABIN_CAMERA, REAR_CAMERA,
                    OPTION_CAMERA, SD_CARD, LTE_MODULE, GNSS, BT, WIFI,
                    IF_BOX_UNIT, TEMPERATURE, VOLTAGE
                ))
            );
        }

        // Error Code constants (named according to Runtime Exception)
        public static class ErrorCode {
            public static final int MCU_NO_RESPONSE = 3;
            public static final int LED_CONTROL_UNAVAILABLE = 1;
            public static final int G_SENSOR_UNAVAILABLE = 1;
            public static final int GYRO_SENSOR_UNAVAILABLE = 1;
            public static final int CAMERA_UNAVAILABLE = 1;
            public static final int CAMERA_NO_SIGNAL = 2;
            public static final int CAMERA_NOT_DETECTED = 0;
            public static final int NO_SD_CARD = 0;
            public static final int SD_CARD_UNAVAILABLE = 1;
            public static final int READ_WRITE_FAIL = 2;
            public static final int LTE_MODULE_UNAVAILABLE = 1;
            public static final int NO_SIM_CARD = 0;
            public static final int LTE_NETWORK_UNAVAILABLE = 2;
            public static final int GNSS_FAIL_UNAVAILABLE = 1;
            public static final int DR_ALGORITHM_UNAVAILABLE = 2;
            public static final int BT_UNAVAILABLE = 1;
            public static final int WIFI_UNAVAILABLE = 1;
            public static final int IF_BOX_UNIT_UNAVAILABLE = 1;
            public static final int CPU_HIGH_TEMPERATURE = 5;
            public static final int BATTERY_LOW_POWER = 4;
        }

        // HW Error Code constants (named according to Runtime Exception)
        public static class HwErrorCode {
            public static final int MCU_NO_RESPONSE = 13;
            public static final int LED_CONTROL_UNAVAILABLE = 21;
            public static final int G_SENSOR_UNAVAILABLE = 31;
            public static final int GYRO_SENSOR_UNAVAILABLE = 41;
            public static final int FRONT_CAMERA_UNAVAILABLE = 51;
            public static final int FRONT_CAMERA_NO_SIGNAL = 52;
            public static final int INCABIN_CAMERA_UNAVAILABLE = 61;
            public static final int INCABIN_CAMERA_NO_SIGNAL = 62;
            public static final int REAR_CAMERA_NOT_DETECTED = 70;
            public static final int REAR_CAMERA_UNAVAILABLE = 71;
            public static final int REAR_CAMERA_NO_SIGNAL = 72;
            public static final int OPTION_CAMERA_NOT_DETECTED = 80;
            public static final int OPTION_CAMERA_UNAVAILABLE = 81;
            public static final int OPTION_CAMERA_NO_SIGNAL = 82;
            public static final int NO_SD_CARD = 90;
            public static final int SD_CARD_UNAVAILABLE = 91;
            public static final int READ_WRITE_FAIL = 92;
            public static final int NO_SIM_CARD = 100;
            public static final int LTE_MODULE_UNAVAILABLE = 101;
            public static final int LTE_NETWORK_UNAVAILABLE = 102;
            public static final int GNSS_FAIL_UNAVAILABLE = 111;
            public static final int DR_ALGORITHM_UNAVAILABLE = 112;
            public static final int BT_UNAVAILABLE = 121;
            public static final int WIFI_UNAVAILABLE = 131;
            public static final int IF_BOX_UNIT_UNAVAILABLE = 141;
            public static final int CPU_HIGH_TEMPERATURE = 155;
            public static final int BATTERY_LOW_POWER = 164;
        }

        private final int hwId;
        private final int errorCode;
        private final int hwErrorCode;

        HardwareRuntimeError(int hwId, int errorCode, int hwErrorCode) {
            this.hwId = hwId;
            this.errorCode = errorCode;
            this.hwErrorCode = hwErrorCode;
        }

        public int getHwId() {
            return hwId;
        }

        public int getErrorCode() {
            return errorCode;
        }

        public int getHwErrorCode() {
            return hwErrorCode;
        }

        /**
         * HardwareRuntimeError.
         */
        public static HardwareRuntimeError findByHwId(int hwId) {
            for (HardwareRuntimeError error : values()) {
                if (error.hwId == hwId) {
                    return error;
                }
            }
            return null;
        }

        /**
         * HardwareRuntimeError.
         */
        public static HardwareRuntimeError findByHwErrorCode(int hwErrorCode) {
            for (HardwareRuntimeError error : values()) {
                if (error.hwErrorCode == hwErrorCode) {
                    return error;
                }
            }
            return null;
        }

        /**
         * HardwareRuntimeError.
         */
        public static HardwareRuntimeError findByErrorCode(int errorCode) {
            for (HardwareRuntimeError error : values()) {
                if (error.errorCode == errorCode) {
                    return error;
                }
            }
            return null;
        }
    }

    public static class LedControl {
        //--------------------------POWER----------------------------//
        public static final int POWER = 0x01;
        public static final String POWER_NAME = "POWER_LED";
        //--------------------------ACC ON----------------------------//
        public static final int RECORDING = 0x02;
        public static final String RECORDING_NAME = "RECORDING_LED";
        public static final int RECORDING_LTE_WEAK = 0x03;
        public static final String RECORDING_LTE_WEAK_NAME = "RECORDING_LTE_WEAK_LED";
        public static final int RECORDING_LTE_OUT = 0x04;
        public static final String RECORDING_LTE_OUT_NAME = "RECORDING_LTE_OUT_LED";
        //--------------------------ACC OFF----------------------------//
        public static final int PROCESSING = 0x05;
        public static final String PROCESSING_NAME = "PROCESSING_LED";
        public static final int PROCESSING_LTE_WEAK = 0x06;
        public static final String PROCESSING_LTE_WEAK_NAME = "PROCESSING_LTE_WEAK_LED";
        public static final int PROCESSING_LTE_OUT = 0x07;
        public static final String PROCESSING_LTE_OUT_NAME = "PROCESSING_LTE_OUT_LED";
        //--------------------------DEVICE----------------------------//
        public static final int DEVICE_WARNING = 0x08;
        public static final String DEVICE_WARNING_NAME = "DEVICE_WARNING_LED";
        public static final int SDCARD_ERROR = 0x09;
        public static final String SDCARD_ERROR_NAME = "SDCARD_ERROR_LED";
        public static final int DEVICE_ERROR = 0x0A;
        public static final String DEVICE_ERROR_NAME = "DEVICE_ERROR_LED";
        //--------------------------SLEEP----------------------------//
        public static final int SLEEP = 0x0B;
        public static final String SLEEP_NAME = "SLEEP_LED";
    }
}
