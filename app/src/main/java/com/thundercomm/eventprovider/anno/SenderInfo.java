/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for broadcast sender classes.
 * Used to automatically register senders with their corresponding actions.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(value = ElementType.TYPE)
public @interface SenderInfo {

    /**
     * The broadcast action of the sender.
     *
     * @return the action of the sender.
     */
    String action();
}
