/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import android.content.Intent;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.core.PayloadType;
import com.thundercomm.eventprovider.utils.EventBrokerManager;
import com.thundercomm.eventprovider.utils.GpioUtils;
import com.thundercomm.eventprovider.utils.ThreadUtils;

import vendor.thundercomm.eventbroker.EventBody;
import vendor.thundercomm.eventbroker.EventEntry;
import vendor.thundercomm.eventbroker.EventHeader;

@ControllerInfo(action = Intent.ACTION_SCREEN_ON)
public class PowerWakeupController extends BaseController {

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        turnOffSleepLed();
        response();
        sendEventBroker();
        writeGpioActive();
    }

    /**
     * Turn off sleep led.
     */
    private void turnOffSleepLed() {
        try {
            EventEntry eventEntry = new EventEntry();
            eventEntry.header = new EventHeader();
            eventEntry.header.categoryId = EventCategory.LED_CONTROL;
            eventEntry.header.typeId = EventType.LedControl.SLEEP;
            eventEntry.header.timestamp = System.currentTimeMillis();
            eventEntry.body = new EventBody();
            eventEntry.body.name = EventType.LedControl.SLEEP_NAME;
            eventEntry.body.payloadType = PayloadType.CUSTOM.getType();
            eventEntry.body.payloadData = new byte[]{0};
            EventProviderApp.getEventListener(EventCategory.LED_CONTROL).onEventReceived(eventEntry);
        } catch (Exception ex) {
            EventProviderApp.LOG.w("onEventReceived: turnOffSleepLed failed", ex);
        }
    }

    /**
     * Response the wakeup broadcast to the client.
     */
    private void response() {
        EventProviderApp.LOG.i("response wakeup");
        Intent responseIntent = new Intent(ActionConstants.ACTION_SYS_WAKEUP);
        getContext().sendBroadcast(responseIntent);
    }

    /**
     * Send event to event broker.
     */
    private void sendEventBroker() {
        try {
            EventBrokerManager.getInstance().publishEvent(EventCategory.TRANSITION,
                    EventType.Transition.POWER_SAVE,
                    EventType.Transition.POWER_SAVE_NAME, "0",
                    PayloadType.CUSTOM.getType());
        } catch (Exception ex) {
            EventProviderApp.LOG.w("onEventReceived: turn off failed", ex);
        }
    }

    /**
     * Write gpio to active status.
     * GPIO_79_PATH: 1
     * GPIO_67_PATH: 1
     */
    private void writeGpioActive() {
        ThreadUtils.postOnBackgroundThread(() -> {
            try {
                EventProviderApp.LOG.i("writeGpioActive: start");
                boolean result79 = GpioUtils.writeGpioFile(GpioUtils.GPIO_79_PATH, "1");
                EventProviderApp.LOG.i("writeGpioActive: result79=" + result79);
            } catch (Exception ex) {
                EventProviderApp.LOG.w("writeGpioActive: failed", ex);
            }
        });
    }
}
