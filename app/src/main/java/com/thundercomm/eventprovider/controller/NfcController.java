/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_NFC_SET;

import android.content.Intent;
import android.nfc.NfcAdapter;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_NFC_SET)
public class NfcController extends BaseController {

    private static final int VALUE_OFF = 0;
    private static final int VALUE_ON = 1;

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        if (!intent.hasExtra(ActionConstants.EXTRA_KEY_VALUE)) {
            EventProviderApp.LOG.w("handleIntent: need correct extra!");
            return;
        }
        int value = intent.getIntExtra(ActionConstants.EXTRA_KEY_VALUE, VALUE_OFF);
        handleNfcValue(value == VALUE_ON);
    }

    /**
     * Handle NFC switch on/off.
     *
     * @param on true if on, false if off
     */
    private void handleNfcValue(boolean on) {
        EventProviderApp.LOG.d("handleNfcValue: on = " + on);
        boolean result = false;
        try {
            NfcAdapter adapter = NfcAdapter.getDefaultAdapter(getContext());
            if (adapter == null) {
                EventProviderApp.LOG.w("handleNfcValue: NfcAdapter is null");
                return;
            }
            if (on) {
                result = adapter.enable();
            } else {
                result = adapter.disable();
            }
        } catch (Exception ex) {
            EventProviderApp.LOG.w("handleNfcValue: Exception occurred while switch nfc", ex);
        }
        EventProviderApp.LOG.d("handleNfcValue: " + on + ", result=" + result);
    }
}
