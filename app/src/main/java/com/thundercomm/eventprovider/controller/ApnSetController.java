/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_APN_SET_REQ;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.telephony.data.ApnSetting;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_APN_SET_REQ)
public class ApnSetController extends BaseController {
    private static final String KEY_APN_NAME = "KEY_APN_NAME";
    private static final String KEY_APN_USER = "KEY_APN_USER";
    private static final String KEY_APN_PASS = "KEY_APN_PASS";
    private static final String KEY_APN_TYPE = "KEY_APN_TYPE";
    private static final String KEY_RESULT_CODE = "KEY_RESULT_CODE";
    private static final int KEY_RESULT_CODE_SUCCESS = 0;
    private static final int KEY_RESULT_CODE_FAILED = 1;
    private static final int KEY_RESULT_CODE_NO_CHANGE = 2;

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: null intent: ");
            return;
        }
        if (!checkIntentExtra(intent, KEY_APN_NAME)
                || !checkIntentExtra(intent, KEY_APN_USER)
                || !checkIntentExtra(intent, KEY_APN_PASS)
                || !checkIntentExtra(intent, KEY_APN_TYPE)) {
            return;
        }

        String apnName = intent.getStringExtra(KEY_APN_NAME);
        String apnUser = intent.getStringExtra(KEY_APN_USER);
        String apnPass = intent.getStringExtra(KEY_APN_PASS);
        String apnType = intent.getStringExtra(KEY_APN_TYPE);
        EventProviderApp.LOG.d("handleIntent: " + intent.getAction()
                + " " + KEY_APN_NAME + " = " + apnName
                + ", " + KEY_APN_TYPE + " = " + apnType);

        try {
            ApnSetting.Builder builder = null;
            TelephonyManager telephonyManager = (TelephonyManager)
                    getContext().getSystemService(Context.TELEPHONY_SERVICE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                builder = new ApnSetting.Builder();
                ApnSetting setting = builder
                        .setUser(apnUser)
                        .setApnName(apnName)
                        .setPassword(apnPass)
                        .setApnTypeBitmask(ApnSetting.getApnTypesBitmaskFromString(apnType))
                        .build();
                boolean success = telephonyManager.modifyDevicePolicyOverrideApn(
                        getContext(), 0, setting);
                response(success ? KEY_RESULT_CODE_SUCCESS : KEY_RESULT_CODE_FAILED);
            }
        } catch (SecurityException ex) {
            response(KEY_RESULT_CODE_FAILED);
            EventProviderApp.LOG.e("handleIntent: SecurityException while setting APN", ex);
        }
    }

    private boolean checkIntentExtra(Intent intent, String key) {
        if (!intent.hasExtra(key)) {
            EventProviderApp.LOG.w("handleIntent: no extra " + key);
            return false;
        }
        return true;
    }

    /**
     * Sends a response indicating whether the APN setting was successful.
     *
     * @param code 0:Success 1:Fail  2:No change
     */
    private void response(int code) {
        Intent responseIntent = new Intent(ACTION_APN_SET_REQ);
        responseIntent.putExtra(KEY_RESULT_CODE, code);
        getContext().sendBroadcast(responseIntent);
        EventProviderApp.LOG.d("APN set response sent: " + code);
    }
}
