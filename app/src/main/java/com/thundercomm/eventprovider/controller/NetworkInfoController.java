/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_CONNECT_INFO_RES;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_NETWORK_INFO_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_NETWORK_INFO_RES;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.database.Cursor;
import android.net.ConnectivityManager;
import android.net.LinkAddress;
import android.net.LinkProperties;
import android.net.Network;
import android.net.Uri;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.utils.NetworkStateUtils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

@ControllerInfo(action = ACTION_NETWORK_INFO_REQ)
public class NetworkInfoController extends BaseController {
    private static final String KEY_APN_NAME = "KEY_APN_NAME";
    private static final String KEY_APN_USER = "KEY_APN_USER";
    private static final String KEY_APN_PASS = "KEY_APN_PASS";
    private static final String KEY_IP = "KEY_IP";
    private static final String KEY_PHONE_NUMBER = "KEY_PHONE_NUMBER";
    private static final String KEY_TIME = "KEY_TIME";
    private static final int HIDDEN_CODE_LENGTH = 4;

    @Override
    public void handleIntent(Intent intent) {
        //TODO  not implemented yet
        EventProviderApp.LOG.d("handleIntent: " + intent.getAction());
        Uri currentUri = Uri.parse("content://telephony/carriers/preferapn");
        Cursor currentCursor = getContext().getContentResolver().query(currentUri, null,
                null, null, null);

        if (currentCursor != null && currentCursor.moveToFirst()) {
            @SuppressLint("Range")
            String currentName = currentCursor.getString(currentCursor.getColumnIndex("name"));
            @SuppressLint("Range")
            String currentUser = currentCursor.getString(currentCursor.getColumnIndex("user"));
            @SuppressLint("Range")
            String currentPass = currentCursor.getString(currentCursor.getColumnIndex("password"));

            EventProviderApp.LOG.d("response Intent:  currentName = " + maskSensitiveInfo(currentName));
            EventProviderApp.LOG.d("response Intent:  currentUser = " + maskSensitiveInfo(currentUser));
            EventProviderApp.LOG.d("response Intent:  currentPass = " + maskSensitiveInfo(currentPass));

            Intent resultIntent = new Intent(ACTION_NETWORK_INFO_RES);
            resultIntent.putExtra(KEY_APN_NAME, currentName);
            resultIntent.putExtra(KEY_APN_USER, currentUser);
            resultIntent.putExtra(KEY_APN_PASS, currentPass);
            resultIntent.putExtra(KEY_IP, NetworkStateUtils.getIpAddress(getContext(), Inet4Address.class));
            resultIntent.putExtra(KEY_PHONE_NUMBER, NetworkStateUtils.getPhoneNumber(getContext()));
            resultIntent.putExtra(KEY_TIME,  getCurrentTime());
            getContext().sendBroadcast(resultIntent);
        }
        if (currentCursor != null) {
            currentCursor.close(); // 确保Cursor对象被正确关闭以释放资源
        }
    }

    private String getCurrentTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault());
        String formattedTime = sdf.format(new Date());
        EventProviderApp.LOG.d("response Intent:  getCurrentTime = " + formattedTime);
        return formattedTime;
    }

    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= HIDDEN_CODE_LENGTH) {
            return "****";
        }
        return "****" + info.substring(info.length() - HIDDEN_CODE_LENGTH);
    }
}
