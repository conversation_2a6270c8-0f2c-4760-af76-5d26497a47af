/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_NETWORK_INFO_REQ;

import android.content.Intent;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_NETWORK_INFO_REQ)
public class NetworkInfoController extends BaseController {
    @Override
    public void handleIntent(Intent intent) {
        //TODO  not implemented yet
        EventProviderApp.LOG.d("handleIntent: " + intent.getAction());
    }
}
