/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_DISPLAY_FUNC;

import android.content.Intent;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.manager.ConfigManager;

@ControllerInfo(action = ACTION_DISPLAY_FUNC)
public class DisplayFuncController extends BaseController {

    private static final int VALUE_ON = 1;

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        if (!intent.hasExtra(ActionConstants.EXTRA_KEY_VALUE)) {
            EventProviderApp.LOG.w("handleIntent: no extra");
            return;
        }
        int lcdUse = intent.getIntExtra(ActionConstants.EXTRA_KEY_VALUE, VALUE_ON);
        EventProviderApp.LOG.i("handleIntent: KEY_VALUE = " + lcdUse);
        ConfigManager manager = ConfigManager.getInstance(getContext());
        boolean success = manager.setLcdEnabled(lcdUse == VALUE_ON);
        EventProviderApp.LOG.i("handleIntent: setLcdEnabled success = " + success);
    }
}
