/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_DVR_INFO_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_DVR_INFO_RES;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.text.TextUtils;

import androidx.core.content.ContextCompat;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_DVR_INFO_REQ)
public class DvrInfoController extends BaseController {

    public static final String PERMISSION = Manifest.permission.READ_PHONE_STATE;

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        handleDvrInfo();
    }

    /**
     * Handle get DVR info.
     */
    private void handleDvrInfo() {
        //Get the serial no for device
        String serialNo;
        try {
            if (ContextCompat.checkSelfPermission(getContext(), PERMISSION)
                    == PackageManager.PERMISSION_GRANTED) {
                serialNo = Build.getSerial();
            } else {
                EventProviderApp.LOG.w("failed to get serial no: no PERMISSION !");
                serialNo = "";
            }
        } catch (Exception ex) {
            EventProviderApp.LOG.w("handleIntent: get serial no failed !");
            serialNo = "";
        }
        EventProviderApp.LOG.i("handleIntent: serialNo = " + serialNo);
        response(serialNo);
    }

    /**
     * Send the result to the client.
     *
     * @param serialNo serial no
     */
    private void response(String serialNo) {
        EventProviderApp.LOG.d("response: serialNo=" + serialNo);
        Intent responseIntent = new Intent(ACTION_DVR_INFO_RES);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_SERIAL_NO, serialNo);
        getContext().sendBroadcast(responseIntent);
    }
}
