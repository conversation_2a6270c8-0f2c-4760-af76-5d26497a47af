/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import android.content.Intent;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = TcFotaUpdateController.ACTION_FOTA_UPDATE_RES_IMPL)
public class TcFotaUpdateController extends BaseController {

    static final String ACTION_FOTA_UPDATE_RES_IMPL = "com.thundercomm.eventprovider.RES_FOTA_UPDATE";

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        Intent newIntent = new Intent(intent);
        newIntent.setAction(ActionConstants.ACTION_FOTA_UPDATE_RES);
        if (getContext() != null) {
            getContext().sendBroadcast(newIntent);
        }
        EventProviderApp.LOG.i("handleIntent: sendBroadcast action = " + newIntent.getAction());
    }
}
