/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_VER_REQ;

import android.content.Intent;
import android.os.SystemProperties;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.bean.FotaTargetInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.manager.ConfigManager;
import com.thundercomm.eventprovider.utils.PackageUtils;

@ControllerInfo(action = ACTION_VER_REQ)
public class VersionController extends BaseController {
    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        String targetName = intent.getStringExtra(ActionConstants.EXTRA_KEY_TARGET_NAME);
        if (TextUtils.isEmpty(targetName)) {
            EventProviderApp.LOG.w("handleIntent: packageName is empty !");
            return;
        }
        handleVersion(targetName);
    }

    /**
     * Handle version broadcast.
     *
     * @param targetName target name
     */
    private void handleVersion(String targetName) {
        try {
            FotaTargetInfo info = ConfigManager.getInstance(getContext())
                    .getFotaTargetInfoByTargetName(targetName);
            if (info == null) {
                EventProviderApp.LOG.w("handleVersion: info is null !");
                response(targetName, "");
                return;
            }
            switch (info.getFotaType()) {
                case ConfigManager.CONFIG_VALUE_FOTA_TYPE_APK:
                    String packageName = info.getTargetName();
                    EventProviderApp.LOG.i("handleVersion: apk: packageName=" + packageName);
                    String appVersionName = PackageUtils.getAppVersionName(getContext(), packageName);
                    response(targetName, appVersionName);
                    break;
                case ConfigManager.CONFIG_VALUE_FOTA_TYPE_CONFIG:
                    EventProviderApp.LOG.i("handleVersion: config");
                    ConfigManager cm = ConfigManager.getInstance(getContext());
                    String configVersion = cm.getVersion();
                    response(targetName, configVersion);
                    break;
                case ConfigManager.CONFIG_VALUE_FOTA_TYPE_FIRMWARE:
                    EventProviderApp.LOG.i("handleVersion: firmware");
                    String firmwareVersion = SystemProperties.get("ro.build.version.firmware");
                    response(targetName, firmwareVersion);
                    break;
                default:
                    EventProviderApp.LOG.w("handleVersion: unknown fotaType: " + info.getFotaType());
                    response(targetName, "");
                    break;
            }
        } catch (Exception ex) {
            EventProviderApp.LOG.w("handleVersion failed: " + ex.getMessage());
        }
    }

    /**
     * Response broadcast to client.
     *
     * @param targetName target name
     * @param version    version
     */
    private void response(String targetName, String version) {
        EventProviderApp.LOG.i("response: targetName=" + targetName + ", version=" + version);
        Intent responseIntent = new Intent(ActionConstants.ACTION_VER_RES);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_TARGET_NAME, targetName);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_TARGET_VER, version);
        getContext().sendBroadcast(responseIntent);
    }
}
