/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_LOCATION;

import android.content.Intent;
import android.provider.Settings;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_LOCATION)
public class LocationController extends BaseController {
    private static final String KEY_VALUE = "KEY_VALUE";
    private static final int VALUE_ON = 1;
    private static final int VALUE_OFF = 0;

    @Override
    public void handleIntent(Intent intent) {
        if (!intent.hasExtra(KEY_VALUE)) {
            EventProviderApp.LOG.w("handleIntent: no extra " + KEY_VALUE);
            return;
        }
        int value = intent.getIntExtra(KEY_VALUE, VALUE_OFF);
        EventProviderApp.LOG.d("handleIntent: " + intent.getAction() + " " + KEY_VALUE + " = " + value);
        switch (value) {
            case VALUE_ON:
                EventProviderApp.LOG.d("Location is ON");
                Settings.Secure.putInt(getContext().getContentResolver(),
                        Settings.Secure.LOCATION_MODE,
                        Settings.Secure.LOCATION_MODE_HIGH_ACCURACY);

                break;
            case VALUE_OFF:
                EventProviderApp.LOG.d("Location is OFF");
                Settings.Secure.putInt(
                        getContext().getContentResolver(),
                        Settings.Secure.LOCATION_MODE,
                        Settings.Secure.LOCATION_MODE_OFF
                );
                break;
            default:
                EventProviderApp.LOG.w("Unknown value for " + KEY_VALUE + ": " + value);
                break;
        }
    }

}
