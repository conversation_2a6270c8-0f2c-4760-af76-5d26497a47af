/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_EVENT_REC_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_EVENT_REC_RES;
import static com.thundercomm.eventprovider.anno.ActionConstants.TC_ACTION_EVENT_REC_RES;

import android.content.Intent;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.core.IController;

@ControllerInfo(action = TC_ACTION_EVENT_REC_RES)
public class TcRecordResController extends BaseController {
    private static final int RESULT_CODE_SUCCESS = 0; // 0:Success
    private static final int RESULT_CODE_FAILED_ALREADY_RECORDING = 1; // 1:Fail(Already recording)
    private static final int RESULT_CODE_FAILED_OTHER = 2; // 2:Fail(Other)
    private static final String KEY_RESULT_CODE = "KEY_RESULT_CODE";
    /**
     * (String)
     * The path/name of the recorded event file.(Absolute Path)
     * It will not be used if the Result Code is Failure.
     */
    private static final String KEY_RECORD_FILE_NAME = "KEY_RECORD_FILE_NAME";

    @Override
    public void handleIntent(Intent intent) {
        if (!intent.hasExtra(KEY_RESULT_CODE)) {
            EventProviderApp.LOG.w("handleIntent: no extra " + KEY_RESULT_CODE);
            return;
        }
        int resultCode = intent.getIntExtra(KEY_RESULT_CODE, RESULT_CODE_FAILED_OTHER);
        Intent resIntent = new Intent(ACTION_EVENT_REC_RES);
        resIntent.putExtra(KEY_RESULT_CODE, resultCode);
        if (resultCode == RESULT_CODE_SUCCESS) {
            if (!intent.hasExtra(KEY_RECORD_FILE_NAME)) {
                EventProviderApp.LOG.w("handleIntent: no extra " + KEY_RECORD_FILE_NAME);
                return;
            }
            String recordFileName = intent.getStringExtra(KEY_RECORD_FILE_NAME);
            EventProviderApp.LOG.d("handleIntent: " + intent.getAction() + " " + KEY_RESULT_CODE + " = " + resultCode
                    + ", " + KEY_RECORD_FILE_NAME + " = " + recordFileName);
            resIntent.putExtra(KEY_RECORD_FILE_NAME, recordFileName);
        } else {
            EventProviderApp.LOG.d("handleIntent: " + intent.getAction() + " " + KEY_RESULT_CODE + " = " + resultCode);
        }
        getContext().sendBroadcast(resIntent);
        IController controller = EventProviderApp.getController(ACTION_EVENT_REC_REQ);
        if (controller instanceof EventRecordController) {
            ((EventRecordController) controller).setRecordResponded(true);
        } else {
            EventProviderApp.LOG.w("handleIntent: controller is not instance of EventRecordController");
        }
    }
}
