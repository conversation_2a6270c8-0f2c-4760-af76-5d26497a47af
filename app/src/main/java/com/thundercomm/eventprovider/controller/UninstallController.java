/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static android.content.pm.PackageManager.DELETE_ALL_USERS;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_UNINSTALL_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_UNINSTALL_RES;

import android.content.Intent;
import android.content.pm.IPackageDeleteObserver;
import android.content.pm.PackageManager;
import android.os.RemoteException;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.utils.PackageUtils;

@ControllerInfo(action = ACTION_UNINSTALL_REQ)
public class UninstallController extends BaseController {

    private static final int RESULT_CODE_SUCCESS = 0;
    private static final int RESULT_CODE_FAILED = 1;
    private static final int RESULT_CODE_PACKAGE_NOT_FOUND = 2;

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        if (!intent.hasExtra(ActionConstants.EXTRA_KEY_TARGET_NAME)) {
            EventProviderApp.LOG.w("handleIntent: no extra " + ActionConstants.EXTRA_KEY_TARGET_NAME);
            return;
        }
        String packageName = intent.getStringExtra(ActionConstants.EXTRA_KEY_TARGET_NAME);
        if (TextUtils.isEmpty(packageName)) {
            EventProviderApp.LOG.w("handleIntent: packageName is empty !");
            response(packageName, RESULT_CODE_PACKAGE_NOT_FOUND);
            return;
        }
        int checkResult = PackageUtils.checkAppSignature(getContext(), packageName);
        EventProviderApp.LOG.i("checkResult: " + checkResult);
        switch (checkResult) {
            case PackageUtils.RESULT_NOT_FOUND:
                response(packageName, RESULT_CODE_PACKAGE_NOT_FOUND);
                break;
            case PackageUtils.RESULT_FAILED:
                response(packageName, RESULT_CODE_FAILED);
                break;
            default:
                silentUninstall(packageName);
                break;
        }
    }

    /**
     * Uninstall app package.
     *
     * @param packageName package name
     */
    private void silentUninstall(String packageName) {
        EventProviderApp.LOG.d("silentUninstall: packageName = " + packageName);
        try {
            PackageManager pm = getContext().getPackageManager();
            pm.deletePackage(packageName, new PackageDeleteObserver(), DELETE_ALL_USERS);
        } catch (Exception ex) {
            EventProviderApp.LOG.w("silentUninstall: failed to uninstall package !", ex);
            response(packageName, RESULT_CODE_FAILED);
        }
    }

    private class PackageDeleteObserver extends IPackageDeleteObserver.Stub {
        @Override
        public void packageDeleted(String packageName, int returnCode) throws RemoteException {
            EventProviderApp.LOG.i("packageDeleted: packageName = " + packageName
                    + ", returnCode = " + returnCode);
            if (returnCode == PackageManager.DELETE_SUCCEEDED) {
                response(packageName, RESULT_CODE_SUCCESS);
            } else {
                response(packageName, RESULT_CODE_FAILED);
            }
        }
    }

    /**
     * Send the result to the client.
     *
     * @param packageName package name
     * @param resultCode  result code
     */
    private void response(String packageName, int resultCode) {
        EventProviderApp.LOG.i("response: packageName = " + packageName + ", resultCode = " + resultCode);
        Intent responseIntent = new Intent(ACTION_UNINSTALL_RES);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_RESULT_CODE, resultCode);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_TARGET_NAME, packageName);
        getContext().sendBroadcast(responseIntent);
    }
}