/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_PWR_SAVE;

import android.content.Intent;
import android.os.PowerManager;
import android.os.SystemClock;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_PWR_SAVE)
public class PowerSaveController extends BaseController {
    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        handlePowerSaveEvent();
    }

    /**
     * Handle power save event.
     */
    private void handlePowerSaveEvent() {
        EventProviderApp.LOG.i("handlePowerSave: ");
        PowerManager manager = getContext().getSystemService(PowerManager.class);
        if (manager == null) {
            EventProviderApp.LOG.w("handlePowerSave: PowerManager is null !");
            return;
        }
        manager.goToSleep(SystemClock.uptimeMillis());
    }
}
