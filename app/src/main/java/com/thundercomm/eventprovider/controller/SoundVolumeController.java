/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_SOUND_VOLUME;

import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_SOUND_VOLUME)
public class SoundVolumeController extends BaseController {
    private static final String KEY_VALUE = "KEY_VALUE";
    private static final int SOUND_INDEX_MIN = 0;
    private static final int SOUND_INDEX_MAX = 15;
    @Override
    public void handleIntent(Intent intent) {
        if (!intent.hasExtra(KEY_VALUE)) {
            EventProviderApp.LOG.w("handleIntent: no extra " + KEY_VALUE);
            return;
        }
        int value = intent.getIntExtra(KEY_VALUE, SOUND_INDEX_MIN);
        EventProviderApp.LOG.d("handleIntent: " + intent.getAction()
                + " " + KEY_VALUE + " = " + value);
        if (value < SOUND_INDEX_MIN || value > SOUND_INDEX_MAX) {
            EventProviderApp.LOG.w("Invalid sound index: " + value
                    + ", must be between " + SOUND_INDEX_MIN
                    + " and " + SOUND_INDEX_MAX);
            return;
        }
        AudioManager audioManager = (AudioManager)
                getContext().getSystemService(Context.AUDIO_SERVICE);
        if (audioManager == null) {
            EventProviderApp.LOG.e("AudioManager is not available");
            return;
        }
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, value, AudioManager.FLAG_SHOW_UI);
    }
}
