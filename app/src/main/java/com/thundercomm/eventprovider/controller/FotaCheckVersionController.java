/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_FOTA_CHECK_VERSION_REQ;

import android.content.Intent;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_FOTA_CHECK_VERSION_REQ)
public class FotaCheckVersionController extends BaseController {

    static final String ACTION_FOTA_CHECK_VERSION_REQ_IMPL = "com.thundercomm.eventprovider.REQ_FOTA_CHECK_VERSION";

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        Intent newIntent = new Intent(intent);
        newIntent.setAction(ACTION_FOTA_CHECK_VERSION_REQ_IMPL);
        if (getContext() != null) {
            getContext().sendBroadcast(newIntent);
        }
        EventProviderApp.LOG.i("handleIntent: sendBroadcast action = " + newIntent.getAction());
    }
}
