/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_LOG_CONTROL;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_SAVE_LOGS;
import static com.thundercomm.eventprovider.anno.ActionConstants.EXTRA_LOG_OPERATION;
import static com.thundercomm.eventprovider.anno.ActionConstants.LOG_OPERATION_START;
import static com.thundercomm.eventprovider.anno.ActionConstants.LOG_OPERATION_STOP;

import android.content.Intent;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_SAVE_LOGS)
public class SaveLogsController extends BaseController {

    private static final String TAG = "SaveLogsController";
    private static final String LOGCLIENT_PACKAGE = "com.tc.logclient";

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w(TAG + ": intent is null");
            return;
        }

        String action = intent.getAction();
        if (TextUtils.isEmpty(action)) {
            EventProviderApp.LOG.w(TAG + ": action is empty");
            return;
        }

        EventProviderApp.LOG.i(TAG + ": Received action: " + action);

        // 解析客户端请求，这里可以根据实际需求添加参数解析
        String operation = intent.getStringExtra("operation");

        if ("start".equals(operation)) {
            sendLogControlBroadcast(LOG_OPERATION_START);
        } else if ("stop".equals(operation)) {
            sendLogControlBroadcast(LOG_OPERATION_STOP);
        } else {
            // 默认行为：先停止，然后保存日志
            EventProviderApp.LOG.i(TAG + ": Default save logs operation");
            sendLogControlBroadcast(LOG_OPERATION_STOP);
            // 这里可以添加延时后触发保存的逻辑
        }
    }

    /**
     * 发送日志控制广播到LogClient
     * @param operation 操作类型：1=开始，0=停止
     */
    private void sendLogControlBroadcast(int operation) {
        try {
            Intent controlIntent = new Intent(ACTION_LOG_CONTROL);
            controlIntent.putExtra(EXTRA_LOG_OPERATION, operation);
            controlIntent.setPackage(LOGCLIENT_PACKAGE);

            getContext().sendBroadcast(controlIntent);

            String operationStr = (operation == LOG_OPERATION_START) ? "START" : "STOP";
            EventProviderApp.LOG.i(TAG + ": Sent log control broadcast - Operation: " + operationStr);

        } catch (Exception e) {
            EventProviderApp.LOG.e(TAG + ": Failed to send log control broadcast", e);
        }
    }
}
