/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_CONNECT_INFO_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_CONNECT_INFO_RES;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_REBOOT_REQ;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.utils.NetworkStateUtils;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

@ControllerInfo(action = ACTION_CONNECT_INFO_REQ)
public class ConnectInfoController extends BaseController {
    private static final String KEY_NET_CONN = "KEY_NET_CONN";
    private static final String KEY_RSRP = "KEY_RSRP";
    private static final String KEY_RSRQ = "KEY_RSRQ";
    private static final String KEY_CELL_ID = "KEY_CELL_ID";
    private static final String KEY_IP = "KEY_IP";
    private static final String KEY_IPV6 = "KEY_IPV6";
    @Override
    public void handleIntent(Intent intent) {
        //TODO  not implemented yet
        EventProviderApp.LOG.d("handleIntent: " + intent.getAction());
        sendConnectInfoBroadcast();
    }

    private void sendConnectInfoBroadcast() {
        Intent resultIntent = new Intent(ACTION_CONNECT_INFO_RES);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            resultIntent.putExtra(KEY_NET_CONN, NetworkStateUtils.getNetWorkConnection(getContext()));
            resultIntent.putExtra(KEY_RSRP, NetworkStateUtils.getLteRsrp(getContext()));
            resultIntent.putExtra(KEY_RSRQ, NetworkStateUtils.getLteRsrq(getContext()));
            resultIntent.putExtra(KEY_CELL_ID, NetworkStateUtils.getCellId(getContext()));
        }
        resultIntent.putExtra(KEY_IP, NetworkStateUtils.getIpAddress(getContext(), Inet4Address.class));
        resultIntent.putExtra(KEY_IPV6,  NetworkStateUtils.getIpAddress(getContext(), Inet6Address.class));
        getContext().sendBroadcast(resultIntent);
    }

}
