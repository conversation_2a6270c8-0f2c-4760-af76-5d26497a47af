/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_EVENT_REC_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_EVENT_REC_RES;
import static com.thundercomm.eventprovider.anno.ActionConstants.TC_ACTION_EVENT_REC_REQ;

import android.content.Intent;
import android.os.Handler;
import android.os.Looper;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;


@ControllerInfo(action = ACTION_EVENT_REC_REQ)
public class EventRecordController extends BaseController {
    private static final int TIMEOUT_RECORD_RESPONSE = 3000; // 3 seconds timeout

    private static final int RESULT_CODE_SUCCESS = 0; // 0:Success
    private static final int RESULT_CODE_FAILED_ALREADY_RECORDING = 1; // 1:Fail(Already recording)
    private static final int RESULT_CODE_FAILED_OTHER = 2; // 2:Fail(Other)

    private volatile boolean isRecordResponded = false;
    private Handler mHandler;

    @Override
    public void handleIntent(Intent intent) {
        isRecordResponded = false;
        // send a inner broadcast to request event recording
        getContext().sendBroadcast(new Intent(TC_ACTION_EVENT_REC_REQ));
        try {
            if (mHandler == null) {
                mHandler = new Handler(Looper.getMainLooper());
            }
            mHandler.postDelayed(() -> {
                if (!isRecordResponded) {
                    EventProviderApp.LOG.d("handleIntent: No response received for record request,"
                            + " sending failure result.");
                    Intent resIntent = new Intent(ACTION_EVENT_REC_RES);
                    resIntent.putExtra("KEY_RESULT_CODE", RESULT_CODE_FAILED_OTHER);
                    getContext().sendBroadcast(resIntent);
                }
            }, TIMEOUT_RECORD_RESPONSE);
        } catch (Exception ex) {
            EventProviderApp.LOG.e("handleIntent: Exception occurred while handling intent: " + ex.getMessage());
        }
    }

    public void setRecordResponded(boolean recordResponded) {
        isRecordResponded = recordResponded;
    }
}
