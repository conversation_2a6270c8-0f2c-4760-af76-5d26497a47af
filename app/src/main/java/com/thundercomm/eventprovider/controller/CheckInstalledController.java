/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_CHECK_INSTALLED_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_CHECK_INSTALLED_RES;

import android.content.Intent;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.utils.PackageUtils;

@ControllerInfo(action = ACTION_CHECK_INSTALLED_REQ)
public class CheckInstalledController extends BaseController {

    private static final int RESULT_CODE_INSTALLED = 0;
    private static final int RESULT_CODE_NOT_INSTALLED = 1;

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        if (!intent.hasExtra(ActionConstants.EXTRA_KEY_TARGET_NAME)) {
            EventProviderApp.LOG.w("handleIntent: need correct extra!");
            return;
        }
        String packageName = intent.getStringExtra(ActionConstants.EXTRA_KEY_TARGET_NAME);
        if (TextUtils.isEmpty(packageName)) {
            EventProviderApp.LOG.w("handleIntent: packageName is empty !");
            response(packageName, RESULT_CODE_NOT_INSTALLED);
            return;
        }
        boolean isInstalled = PackageUtils.isAppInstalled(getContext(), packageName);
        response(packageName, isInstalled ? RESULT_CODE_INSTALLED : RESULT_CODE_NOT_INSTALLED);
    }

    /**
     * Send the result to the client.
     *
     * @param packageName package name
     * @param resultCode  result code
     */
    private void response(String packageName, int resultCode) {
        EventProviderApp.LOG.d("response: package=" + packageName + ", resultCode=" + resultCode);
        Intent responseIntent = new Intent(ACTION_CHECK_INSTALLED_RES);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_RESULT_CODE, resultCode);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_TARGET_NAME, packageName);
        getContext().sendBroadcast(responseIntent);
    }
}
