/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;


import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_REBOOT_REQ;

import android.content.Intent;

import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

@ControllerInfo(action = ACTION_REBOOT_REQ)
public class RebootController extends BaseController {
    private static final String KEY_TARGET = "KEY_TARGET";
    private static final String KEY_RESULT_CODE = "KEY_RESULT_CODE";
    private static final int RESULT_CODE_SUCCESS = 0;
    private static final int RESULT_CODE_FAILED_NOT_FOUND = 1;
    private static final int RESULT_CODE_FAILED_OTHER = 2;

    @Override
    public void handleIntent(Intent intent) {
        //TODO  not implemented yet dependency on config service
    }
}
