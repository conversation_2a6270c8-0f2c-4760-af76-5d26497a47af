/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;


import static android.content.Context.ACTIVITY_SERVICE;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_REBOOT_REQ;
import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_REBOOT_RES;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.PowerManager;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.manager.ConfigManager;
import com.thundercomm.eventprovider.utils.ThreadUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;

@ControllerInfo(action = ACTION_REBOOT_REQ)
public class RebootController extends BaseController {
    private static final String KEY_TARGET = "KEY_TARGET";
    private static final String KEY_RESULT_CODE = "KEY_RESULT_CODE";
    private static final int RESULT_CODE_SUCCESS = 0;
    private static final int RESULT_CODE_FAILED_NOT_FOUND = 1;
    private static final int RESULT_CODE_FAILED_OTHER = 2;
    private static final String REBOOT_DEVICE = "0";
    private static final String REBOOT_MODULE = "2";
    private static final String REBOOT_APP = "3";
    private static final int POST_DELAY = 300;
    private static final String MODULE_EC25_PATH = "/sys/devices/platform/soc/soc:gpio_info/reset_n_ec25_gpio57";
    private static final long MODULE_SWITCH_DELAY_MS = 300;
    private static final int SLOT_INDEX = 1;

    @Override
    public void handleIntent(Intent intent) {
        if (intent != null) {
             String targetNo = intent.getStringExtra(KEY_TARGET);
             String rebootTarget = ConfigManager.getInstance(getContext()).getRebootTarget(targetNo);
             EventProviderApp.LOG.i("RebootTarget: targetNo = " + targetNo);
             EventProviderApp.LOG.i("RebootTarget: rebootTarget = " + rebootTarget);
             if (targetNo != null) {
                 switch (targetNo) {
                     case REBOOT_DEVICE:
                         sendRebootResult(RESULT_CODE_SUCCESS);
                         rebootDevice();
                         break;
                     case REBOOT_MODULE:
                         rebootModule(rebootTarget);
                         break;
                     case REBOOT_APP:
                         restartOtherApp(getContext(), rebootTarget);
                         break;
                     default:
                         break;
                 }
             } else {
                 EventProviderApp.LOG.e("Error: targetNo is null");
             }
        }
    }

    private void rebootModule(String target) {
        EventProviderApp.LOG.i("RebootTarget: rebootModule====" + target);
        ThreadUtils.postOnBackgroundThread(() -> {
            Writer outputStream = null;
            try {
                File nodeFile = new File(MODULE_EC25_PATH);
                if (!nodeFile.exists()) {
                    EventProviderApp.LOG.i("RebootTarget: rebootModule NOT supported.");
                    sendRebootResult(RESULT_CODE_FAILED_NOT_FOUND);
                    return;
                }

                outputStream = new BufferedWriter(new FileWriter(nodeFile));
                outputStream.write("1");
                outputStream.flush();
                Thread.sleep(MODULE_SWITCH_DELAY_MS);
                outputStream.write("0");
                outputStream.flush();

                sendRebootResult(RESULT_CODE_SUCCESS);
            } catch (FileNotFoundException exception) {
                EventProviderApp.LOG.i("RebootTarget: setDeviceModule reboot FileNotFoundException ");
                sendRebootResult(RESULT_CODE_FAILED_NOT_FOUND);
            } catch (Exception ex) {
                EventProviderApp.LOG.i("RebootTarget: setDeviceModule reboot Exception = " + ex);
                sendRebootResult(RESULT_CODE_FAILED_OTHER);
            } finally {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException ioEx) {
                        sendRebootResult(RESULT_CODE_FAILED_OTHER);
                        EventProviderApp.LOG.i("RebootTarget: setDeviceModule reboot Exception = " + ioEx);
                    }
                }
            }
        });
    }

    private void rebootDevice() {
        EventProviderApp.LOG.i("RebootTarget: rebootDevice====");
        PowerManager pm = (PowerManager) getContext().getSystemService(Context.POWER_SERVICE);
        pm.reboot(null);
    }

    private void restartOtherApp(Context context, String packageName) {
        EventProviderApp.LOG.i("RebootTarget: restartOtherApp====");
        PackageManager pm = context.getPackageManager();
        ActivityManager am = (ActivityManager) getContext().getSystemService(ACTIVITY_SERVICE);
        am.killBackgroundProcesses(packageName);

        new Handler().postDelayed(() -> {
            Intent intent = pm.getLaunchIntentForPackage(packageName);
            if (intent != null) {
                sendRebootResult(RESULT_CODE_SUCCESS);
                getContext().startActivity(intent);
            } else {
                sendRebootResult(RESULT_CODE_FAILED_NOT_FOUND);
            }
        }, POST_DELAY);

    }

    private void sendRebootResult(int code) {
        EventProviderApp.LOG.i("RebootTarget: sendRebootResult = " + code);
        Intent resultIntent = new Intent(ACTION_REBOOT_RES);
        resultIntent.putExtra(KEY_RESULT_CODE, code);
        getContext().sendBroadcast(resultIntent);
    }
}
