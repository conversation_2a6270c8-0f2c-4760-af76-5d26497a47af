/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import static com.thundercomm.eventprovider.anno.ActionConstants.ACTION_BLE;

import android.bluetooth.BluetoothAdapter;
import android.content.Intent;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.core.BaseController;

/**
 * TController is a controller class that handles specific actions in the EventProvider application.
 * It is annotated with @ControllerInfo to specify the action it responds to.
 */
@ControllerInfo(action = ACTION_BLE)
public class BleController extends BaseController {

    private static final String KEY_VALUE = "KEY_VALUE";
    private static final int VALUE_ON = 1;
    private static final int VALUE_OFF = 0;

    @Override
    public void handleIntent(Intent intent) {
        if (!intent.hasExtra(KEY_VALUE)) {
            EventProviderApp.LOG.w("handleIntent: no extra " + KEY_VALUE);
            return;
        }
        int value = intent.getIntExtra(KEY_VALUE, VALUE_OFF);
        EventProviderApp.LOG.d("handleIntent: " + intent.getAction() + " " + KEY_VALUE + " = " + value);
        switch (value) {
            case VALUE_OFF:
                handleBluetoothOnOff(false);
                break;
            case VALUE_ON:
                handleBluetoothOnOff(true);
                break;
            default:
                EventProviderApp.LOG.w("handleIntent: unknown value");
                break;
        }
    }

    /**
     * Handle bluetooth on/off.
     *
     * @param on ?
     */
    private void handleBluetoothOnOff(boolean on) {
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        try {
            if (on) {
                boolean bluetoothEnabled = adapter.enable();
                EventProviderApp.LOG.i("handleBluetoothOnOff: enable, bluetoothEnabled=" + bluetoothEnabled);
            } else {
                boolean bluetoothDisabled = adapter.disable();
                EventProviderApp.LOG.i("handleBluetoothOnOff: disable, bluetoothDisabled=" + bluetoothDisabled);
            }
        } catch (Exception ex) {
            EventProviderApp.LOG.w("handleBluetoothOnOff: Exception occurred while turning on bluetooth", ex);
        }
    }
}
