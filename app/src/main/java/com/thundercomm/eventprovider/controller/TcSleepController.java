/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.controller;

import android.content.Intent;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseController;
import com.thundercomm.eventprovider.core.PayloadType;
import com.thundercomm.eventprovider.utils.EventBrokerManager;
import com.thundercomm.eventprovider.utils.GpioUtils;
import com.thundercomm.eventprovider.utils.ThreadUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import vendor.thundercomm.eventbroker.EventBody;
import vendor.thundercomm.eventbroker.EventEntry;
import vendor.thundercomm.eventbroker.EventHeader;

@ControllerInfo(action = Intent.ACTION_SCREEN_OFF)
public class TcSleepController extends BaseController {

    private static final int TIMEOUT = 5;

    @Override
    public void handleIntent(Intent intent) {
        if (intent == null) {
            EventProviderApp.LOG.w("handleIntent: intent is null !");
            return;
        }
        if (!TextUtils.equals(getAction(), intent.getAction())) {
            EventProviderApp.LOG.w("handleIntent: action does not match !");
            return;
        }
        EventProviderApp.LOG.i("handleIntent: action = " + intent.getAction());
        turnOnSleepLed();
        writeGpioSleep();
        sendEventBroker();
    }

    /**
     * Turn on sleep led.
     */
    private void turnOnSleepLed() {
        EventProviderApp.LOG.d("turnOnSleepLed: start");
        try {
            EventEntry eventEntry = new EventEntry();
            eventEntry.header = new EventHeader();
            eventEntry.header.categoryId = EventCategory.LED_CONTROL;
            eventEntry.header.typeId = EventType.LedControl.SLEEP;
            eventEntry.header.timestamp = System.currentTimeMillis();
            eventEntry.body = new EventBody();
            eventEntry.body.name = EventType.LedControl.SLEEP_NAME;
            eventEntry.body.payloadType = PayloadType.CUSTOM.getType();
            eventEntry.body.payloadData = new byte[]{1};
            EventProviderApp.getEventListener(EventCategory.LED_CONTROL).onEventReceived(eventEntry);
        } catch (Exception ex) {
            EventProviderApp.LOG.w("onEventReceived: turnOnSleepLed failed", ex);
        }
    }

    /**
     * Send event to event broker.
     */
    private void sendEventBroker() {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.submit(() -> {
            EventProviderApp.LOG.d("sendEventBroker: start");
            try {
                EventBrokerManager.getInstance().publishEvent(EventCategory.TRANSITION,
                        EventType.Transition.POWER_SAVE,
                        EventType.Transition.POWER_SAVE_NAME, "1",
                        PayloadType.CUSTOM.getType());
            } catch (Exception ex) {
                EventProviderApp.LOG.w("sendEventBroker: turn on failed", ex);
            } finally {
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(TIMEOUT, TimeUnit.SECONDS)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException ex) {
                    EventProviderApp.LOG.e("Interrupted while waiting for executor service to terminate");
                    executorService.shutdownNow();
                }
            }
        });
    }

    /**
     * Write gpio to sleep status.
     * GPIO_79_PATH: 0.
     * GPIO_67_PATH: 1.
     */
    private void writeGpioSleep() {
        ThreadUtils.postOnBackgroundThread(() -> {
            try {
                EventProviderApp.LOG.i("writeGpioSleep: start");
                boolean result79 = GpioUtils.writeGpioFile(GpioUtils.GPIO_79_PATH, "0");
                EventProviderApp.LOG.i("writeGpioSleep: result79=" + result79);
            } catch (Exception ex) {
                EventProviderApp.LOG.w("writeGpioSleep: failed", ex);
            }
        });
    }
}
