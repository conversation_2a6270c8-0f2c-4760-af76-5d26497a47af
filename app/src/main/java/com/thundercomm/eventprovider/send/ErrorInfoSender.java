/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.send;

import android.content.Context;
import android.content.Intent;
import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.SenderInfo;
import com.thundercomm.eventprovider.core.BaseSender;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Notify the occurred error.
 * ErrorInfoSender sends error information broadcasts.
 * Action: "yellowstone.ssolapp.ERROR_INFO".
 */
@SenderInfo(action = ActionConstants.ACTION_ERROR_INFO)
public class ErrorInfoSender extends BaseSender {

    private final SimpleDateFormat mDateFormat;

    public ErrorInfoSender() {
        mDateFormat = new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault());
    }

    // Backward compatibility constructor
    public ErrorInfoSender(Context context) {
        this();
        setContext(context);
    }

    @Override
    public void sendBroadcast(Object... params) {
        if (params.length >= 2) {
            String errorCode = String.valueOf(params[0]);
            String errorSummary = String.valueOf(params[1]);
            sendErrorInfo(errorCode, errorSummary);
        } else if (params.length == 1) {
            String errorCode = String.valueOf(params[0]);
            sendErrorInfo(errorCode, "");
        }
    }

    /**
     * Send error information broadcast with permission control.
     * Receivers must have the RECEIVE_BROADCASTS permission to receive this broadcast.
     * @param hwErrorCode Hardware error code (as String)
     * @param errorSummary Error summary (can be empty or null)
     */
    public void sendErrorInfo(String hwErrorCode, String errorSummary) {
        try {
            Intent intent = new Intent(getAction());
            intent.putExtra(ActionConstants.EXTRA_KEY_ERROR_CODE, hwErrorCode);
            intent.putExtra(ActionConstants.EXTRA_KEY_ERROR_TIME, mDateFormat.format(new Date()));
            intent.putExtra(ActionConstants.EXTRA_KEY_ERROR_SUMMARY, errorSummary != null ? errorSummary : "");

            // Send broadcast with permission control
            sendBroadcastWithPermission(intent);
            EventProviderApp.LOG.i("ErrorInfoSender: Broadcast sent with permission - Action: " + getAction()
                + ", ErrorCode: " + hwErrorCode + ", Permission: " + getRequiredPermission());
        } catch (Exception ex) {
            EventProviderApp.LOG.e("ErrorInfoSender: Failed to send error broadcast: " + ex.getMessage());
        }
    }

    /**
     * Send error information broadcast with integer error code.
     * @param hwErrorCode Hardware error code (as int)
     * @param errorSummary Error summary (can be empty or null)
     */
    public void sendErrorInfo(int hwErrorCode, String errorSummary) {
        sendErrorInfo(String.valueOf(hwErrorCode), errorSummary);
    }
}
