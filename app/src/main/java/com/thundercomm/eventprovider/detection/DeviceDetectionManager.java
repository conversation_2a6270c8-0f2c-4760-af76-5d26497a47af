/*
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.eventprovider.detection;

import android.os.Handler;
import android.os.Looper;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.utils.EventBrokerManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Device detection manager
 * Responsible for managing device detection process, timeout handling and result recording
 */
public class DeviceDetectionManager {

    private static final String TAG = "DeviceDetectionManager";
    private static volatile DeviceDetectionManager sInstance;

    // Timeout duration: 1 minute
    private static final long DETECTION_TIMEOUT_MS = 60000;

    // Detection status
    private boolean mDetectionInProgress = false;
    private long mDetectionStartTime;

    // Hardware ID management
    private Set<Integer> mExpectedHwIds = EventType.HardwareRuntimeError.HwId.DETECTABLE_HW_IDS;
    private Set<Integer> mReceivedHwIds = ConcurrentHashMap.newKeySet();

    // Timeout handling
    private Handler mTimeoutHandler = new Handler(Looper.getMainLooper());
    private Runnable mTimeoutRunnable;

    private DeviceDetectionManager() {
        EventProviderApp.LOG.i(TAG + ": Initialized " + mExpectedHwIds.size() + " expected HW IDs");
    }

    public static DeviceDetectionManager getInstance() {
        if (sInstance == null) {
            synchronized (DeviceDetectionManager.class) {
                if (sInstance == null) {
                    sInstance = new DeviceDetectionManager();
                }
            }
        }
        return sInstance;
    }

    /**
     * Start device detection.
     */
    public void startDetection() {
        if (mDetectionInProgress) {
            EventProviderApp.LOG.w(TAG + ": Detection already in progress");
            return;
        }

        mDetectionInProgress = true;
        mDetectionStartTime = System.currentTimeMillis();
        mReceivedHwIds.clear();

        // Start timeout monitoring
        startTimeoutMonitor();

        EventProviderApp.LOG.d(TAG + ": Device detection started, expecting " + mExpectedHwIds.size() + " devices");
    }

    /**
     * Record received hardware response.
     */
    public void recordResponse(int hwId) {
        if (!mDetectionInProgress) {
            EventProviderApp.LOG.w(TAG + ": Received response for HW ID " + hwId + " but detection not in progress");
            return;
        }

        mReceivedHwIds.add(hwId);
        EventProviderApp.LOG.d(TAG + ": Received response from HW ID " + hwId + 
                             " (" + mReceivedHwIds.size() + "/" + mExpectedHwIds.size() + ")");

        // Check if all devices have responded
        if (mReceivedHwIds.containsAll(mExpectedHwIds)) {
            completeDetection();
        }
    }

    /**
     * Start timeout monitoring.
     */
    private void startTimeoutMonitor() {
        mTimeoutRunnable = () -> {
            handleDetectionTimeout();
        };
        mTimeoutHandler.postDelayed(mTimeoutRunnable, DETECTION_TIMEOUT_MS);
    }

    /**
     * Handle detection timeout.
     */
    private void handleDetectionTimeout() {
        if (!mDetectionInProgress) {
            return;
        }

        // Get unresponsive hardware IDs
        Set<Integer> missingHwIds = new HashSet<>(mExpectedHwIds);
        missingHwIds.removeAll(mReceivedHwIds);

        EventProviderApp.LOG.w(TAG + ": Detection timeout, missing responses from " + missingHwIds.size() + " devices");

        // Create exception events for timed-out hardware
        for (int hwId : missingHwIds) {
            createTimeoutErrorEvent(hwId);
        }

        completeDetection();
    }

    /**
     * Create exception event for timed-out hardware.
     */
    private void createTimeoutErrorEvent(int hwId) {
        try {
            // Find corresponding error code based on hwId
            EventType.HardwareRuntimeError error = findErrorByHwId(hwId);
            if (error == null) {
                EventProviderApp.LOG.w(TAG + ": No error definition found for HW ID " + hwId);
                return;
            }

            // Create timeout error JSON (compliant with field specifications)
            JSONObject timeoutJson = new JSONObject();
            timeoutJson.put("hwid", hwId);                                    // Required
            timeoutJson.put("errorCode", error.getErrorCode());               // Required (when abnormal)
            timeoutJson.put("hwErrorCode", String.valueOf(error.getHwErrorCode())); // Required (when abnormal)

            // Optional data field
            JSONObject dataObject = new JSONObject();
            dataObject.put("error_type", "detection_timeout");
            dataObject.put("timestamp", System.currentTimeMillis());
            dataObject.put("timeout_duration", DETECTION_TIMEOUT_MS);
            timeoutJson.put("data", dataObject);                              // Optional

            // Send timeout error event
            EventBrokerManager.getInstance().publishJsonEvent(
                EventCategory.RUNTIME_ERROR,
                error.getHwErrorCode(),
                "device_detection_timeout",
                timeoutJson.toString()
            );

            EventProviderApp.LOG.w(TAG + ": HW ID " + hwId + " timeout - marked as abnormal " +
                                 "(errorCode: " + error.getErrorCode() + ", hwErrorCode: " + error.getHwErrorCode() + ")");

        } catch (JSONException e) {
            EventProviderApp.LOG.e(TAG + ": Failed to create timeout error event for HW ID " + hwId, e);
        }
    }

    /**
     * Find corresponding hardware error enumeration based on hwId.
     */
    private EventType.HardwareRuntimeError findErrorByHwId(int hwId) {
        for (EventType.HardwareRuntimeError error : EventType.HardwareRuntimeError.values()) {
            if (error.getHwId() == hwId) {
                return error;
            }
        }
        return null;
    }

    /**
     * Complete detection.
     */
    private void completeDetection() {
        if (!mDetectionInProgress) {
            return;
        }

        // Cancel timeout monitoring
        if (mTimeoutRunnable != null) {
            mTimeoutHandler.removeCallbacks(mTimeoutRunnable);
        }

        mDetectionInProgress = false;
        long detectionDuration = System.currentTimeMillis() - mDetectionStartTime;

        EventProviderApp.LOG.d(TAG + ": Device detection completed in " + detectionDuration + "ms. " +
                             "Received: " + mReceivedHwIds.size() + "/" + mExpectedHwIds.size());

        // Generate detection report
        generateDetectionReport();
    }

    /**
     * Generate detection report.
     */
    private void generateDetectionReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== Device Detection Report ===\n");
        report.append("Total Expected: ").append(mExpectedHwIds.size()).append("\n");
        report.append("Total Received: ").append(mReceivedHwIds.size()).append("\n");

        Set<Integer> missingHwIds = new HashSet<>(mExpectedHwIds);
        missingHwIds.removeAll(mReceivedHwIds);

        if (!missingHwIds.isEmpty()) {
            report.append("Missing HW IDs: ").append(missingHwIds).append("\n");
        }

        report.append("Detection Duration: ").append(System.currentTimeMillis() - mDetectionStartTime).append("ms\n");
        report.append("===============================");

        EventProviderApp.LOG.i(TAG + ":\n" + report.toString());
    }

    /**
     * Check if detection is in progress
     */
    public boolean isDetectionInProgress() {
        return mDetectionInProgress;
    }
}
