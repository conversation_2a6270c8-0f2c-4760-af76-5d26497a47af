/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.core;

import android.content.Context;
import android.content.Intent;

public interface IController {

    void handleIntent(Intent intent);

    void setContext(Context context);

    void setAction(String action);
}
