/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.core;

import android.content.Context;

public abstract class BaseController implements IController {

    private Context mContext;
    private String mAction;

    @Override
    public void setAction(String action) {
        mAction = action;
    }

    public void setContext(Context context) {
        mContext = context;
    }

    public Context getContext() {
        return mContext;
    }

    public String getAction() {
        return mAction;
    }
}
