/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.core;

import android.content.Context;

import vendor.thundercomm.eventbroker.EventEntry;

public interface IEventListener {

    void onEventReceived(EventEntry eventEntry);

    void setContext(Context context);

    void setEventTypes(int[] eventTypes);

    void setCategoryId(byte categoryId);

    Context getContext();

    int[] getEventTypes();

    byte getCategoryId();
}
