/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.core;

import android.content.Context;

/**
 * Interface for broadcast senders.
 * Defines the contract for all sender implementations.
 */
public interface ISender {

    /**
     * Send broadcast with the given parameters.
     *
     * @param params Parameters for the broadcast
     */
    void sendBroadcast(Object... params);

    /**
     * Set the context for the sender.
     *
     * @param context Android context
     */
    void setContext(Context context);

    /**
     * Set the action for the sender.
     *
     * @param action Broadcast action
     */
    void setAction(String action);

    /**
     * Get the action of the sender.
     *
     * @return Broadcast action
     */
    String getAction();
}
