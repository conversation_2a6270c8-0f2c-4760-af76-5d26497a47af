/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.core;

public enum PayloadType {
    // json type
    JSON ((byte)0, "json"),
    NATIVE((byte)1, "native"),
    PROTO((byte)2, "proto"),
    BINDER((byte)3, "binder"),
    CUSTOM((byte)4, "custom");

    final byte type;
    final String name;

    PayloadType(byte type, String name) {
        this.type = type;
        this.name = name;
    }

    public byte getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
