/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.core;

import android.content.Context;

public abstract class BaseEventListener implements IEventListener {

    private Context mContext;

    private byte mCategoryId;

    private int[] mEventTypes;

    @Override
    public void setContext(Context context) {
        mContext = context;
    }

    @Override
    public void setEventTypes(int[] eventTypes) {
        mEventTypes = eventTypes;
    }

    @Override
    public void setCategoryId(byte categoryId) {
        mCategoryId = categoryId;
    }

    @Override
    public byte getCategoryId() {
        return mCategoryId;
    }

    @Override
    public int[] getEventTypes() {
        return mEventTypes;
    }

    @Override
    public Context getContext() {
        return mContext;
    }
}
