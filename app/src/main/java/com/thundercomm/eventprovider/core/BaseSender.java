/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.core;

import android.content.Context;
import android.content.Intent;
import com.thundercomm.eventprovider.anno.ActionConstants;

/**
 * Base class for all broadcast senders.
 * Provides common functionality and context management.
 */
public abstract class BaseSender implements ISender {

    private Context mContext;
    private String mAction;

    @Override
    public void setAction(String action) {
        mAction = action;
    }

    @Override
    public void setContext(Context context) {
        mContext = context;
    }

    /**
     * Get the context.
     *
     * @return Android context
     */
    public Context getContext() {
        return mContext;
    }

    @Override
    public String getAction() {
        return mAction;
    }

    /**
     * Send broadcast with permission control.
     * All broadcasts sent through this method will require receivers to have
     * the RECEIVE_BROADCASTS permission.
     *
     * @param intent The intent to broadcast
     */
    protected void sendBroadcastWithPermission(Intent intent) {
        if (mContext != null) {
            mContext.sendBroadcast(intent, ActionConstants.PERMISSION_RECEIVE_BROADCASTS);
        }
    }

    /**
     * Get the required permission for receiving broadcasts from this sender.
     *
     * @return The permission string
     */
    protected String getRequiredPermission() {
        return ActionConstants.PERMISSION_RECEIVE_BROADCASTS;
    }
}
