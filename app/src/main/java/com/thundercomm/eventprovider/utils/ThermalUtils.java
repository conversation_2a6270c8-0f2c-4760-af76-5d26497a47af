/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.utils;

import android.content.Context;
import android.os.IThermalEventListener;
import android.os.IThermalService;
import android.os.RemoteException;
import android.os.ServiceManager;
import android.os.Temperature;

import com.android.internal.annotations.GuardedBy;
import com.thundercomm.eventprovider.EventProviderApp;

public class ThermalUtils {
    private static final String THERMAL_SERVICE = "thermalservice";
    private final Object mThermalLock = new Object();
    @GuardedBy("mThermalLock")
    private IThermalService mThermalService;
    private IThermalEventListener mThermalEventListener;

    private IThermalService getIThermalService() {
        synchronized (mThermalLock) {
            if (mThermalService == null) {
                mThermalService = IThermalService.Stub.asInterface(
                        ServiceManager.getService(THERMAL_SERVICE));
                if (mThermalService != null) {
                    try {
                        mThermalService.asBinder().linkToDeath(() -> {
                            synchronized (mThermalLock) {
                                mThermalService = null;
                            }
                        }, /* flags */ 0);
                    } catch (RemoteException ex) {
                        EventProviderApp.LOG.w("linkToDeath with thermalService failed", ex);
                        mThermalService = null;
                    }
                }
            }
            return mThermalService;
        }
    }

    /**
     * Sets the thermal event listener.
     *
     * @param listener the thermal event listener to set
     */
    public void setThermalEventListener(IThermalEventListener listener) {
        mThermalEventListener = listener;
    }

    /**
     * Sets the thermal event listener.
     */
    public void registerEventListeners(int type) {
        if (!Temperature.isValidType(type)) {
            return;
        }
        // Enable push notifications of throttling from vendor thermal
        // management subsystem via thermalservice.
        IThermalService thermalService = getIThermalService();
        if (thermalService != null && mThermalEventListener != null) {
            try {
                thermalService.registerThermalEventListenerWithType(mThermalEventListener, type);
                EventProviderApp.LOG.d("register thermal listener successfully");
            } catch (RemoteException ex) {
                EventProviderApp.LOG.w("failed to register thermal listener", ex);
            }
        }
    }

    /**
     * Unregisters the thermal event listeners.
     */
    public void unregisterEventListeners() {
        // Enable push notifications of throttling from vendor thermal
        // management subsystem via thermalservice.
        IThermalService thermalService = getIThermalService();
        if (thermalService != null && mThermalEventListener != null) {
            try {
                thermalService.unregisterThermalEventListener(mThermalEventListener);
                mThermalEventListener = null; // Clear the listener after unregistering
                EventProviderApp.LOG.d("unregister thermal listener successfully");
            } catch (RemoteException ex) {
                EventProviderApp.LOG.w("failed to register thermal listener", ex);
            }
        }
    }

}
