/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.utils;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.core.PayloadType;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentLinkedQueue;

import vendor.thundercomm.eventbroker.EventBody;
import vendor.thundercomm.eventbroker.EventEntry;
import vendor.thundercomm.eventbroker.EventHeader;
import vendor.thundercomm.eventbroker.IEventBroker;
import vendor.thundercomm.eventbroker.IEventObserver;

/**
 * EventBroker management utility class
 * Provides singleton EventBroker service connection and event publishing functionality
 * Based on pushJsonEvent method and connection logic in EventMonitorService
 */
public class EventBrokerManager {

    /**
     * EventBroker connection status callback interface
     */
    public interface EventBrokerCallback {
        /**
         * Connection success callback
         * @param name Service component name
         * @param broker EventBroker interface instance
         */
        void onConnected(ComponentName name, IBinder broker);

        /**
         * Connection disconnected callback
         * @param name Service component name
         */
        void onDisconnected(ComponentName name);

        /**
         * Connection failed callback
         * @param error Error message
         */
        void onConnectionFailed(String error);

        void onBindingDied(ComponentName name);
    }

    private static final String TAG = "EventBrokerManager";
    private static volatile EventBrokerManager sInstance;

    private Context mContext;
    private IEventBroker mEventBroker;
    private IBinder mIBinder;
    private boolean mIsConnected = false;
    private boolean mIsConnecting = false;

    // Pending event queue
    private final ConcurrentLinkedQueue<PendingJsonEvent> mPendingEvents = new ConcurrentLinkedQueue<>();

    // Connection status callback list
    private final ConcurrentLinkedQueue<EventBrokerCallback> mCallbacks = new ConcurrentLinkedQueue<>();

    private ServiceConnection mServiceConnection;

    private EventBrokerManager() {
        // Private constructor
    }

    /**
     * Get EventBrokerManager singleton instance
     */
    public static EventBrokerManager getInstance() {
        if (sInstance == null) {
            synchronized (EventBrokerManager.class) {
                if (sInstance == null) {
                    sInstance = new EventBrokerManager();
                }
            }
        }
        return sInstance;
    }

    /**
     * Initialize EventBrokerManager
     * @param context Context
     */
    public synchronized void init(Context context) {
        if (mContext == null) {
            mContext = context.getApplicationContext();
            connectToEventBroker();
        }
    }

    /**
     * Internal method for publishing events with common logic
     * @param category Category ID
     * @param type Type ID
     * @param name Event name
     * @param content Content
     * @param payloadType Payload type
     */
    private void publishEventInternal(byte category, int type, String name, String content, int payloadType) {
        // Parameter validation (reuses validation logic from pushJsonEvent)
        if (TextUtils.isEmpty(name) || TextUtils.isEmpty(content)) {
            EventProviderApp.LOG.w(TAG + ": Name or content is empty, cannot publish event");
            return;
        }

        PendingJsonEvent event = new PendingJsonEvent(category, type, name, content, payloadType);

        if (mIsConnected && mEventBroker != null) {
            // If connected, publish directly
            executePublishJsonEvent(event);
        } else {
            // If not connected, add to pending queue
            mPendingEvents.offer(event);
            // Ensure connection is started
            ensureConnection();
        }
    }

    /**
     * Publish JSON event (based on pushJsonEvent method in EventMonitorService)
     * @param category Category ID
     * @param type Type ID
     * @param name Event name
     * @param content JSON content
     */
    public void publishJsonEvent(byte category, int type, String name, String content) {
        publishEventInternal(category, type, name, content, PayloadType.JSON.getType());
    }

    /**
     * Publish event (extended interface, supports custom payload type)
     * @param category Category ID
     * @param type Type ID
     * @param name Event name
     * @param content Content
     * @param payloadType Payload type
     */
    public void publishEvent(byte category, int type, String name, String content, int payloadType) {
        publishEventInternal(category, type, name, content, payloadType);
    }

    /**
     * Publish event (convenience interface, uses default parameters)
     * @param name Event name
     * @param content JSON content
     */
    public void publishEvent(String name, String content) {
        publishJsonEvent((byte) 1, 2, name, content);
    }

    /**
     * Add connection status callback
     * @param callback Callback interface
     */
    public void addCallback(EventBrokerCallback callback) {
        if (callback != null && !mCallbacks.contains(callback)) {
            mCallbacks.offer(callback);

            // If already connected, callback immediately
            if (mIsConnected && mEventBroker != null && mIBinder != null) {
                ComponentName serviceName = new ComponentName("vendor.thundercomm.brokeragent",
                                                            "vendor.thundercomm.brokeragent.BrokerAgentService");
                callback.onConnected(serviceName, mIBinder);
            }
        }
    }

    /**
     * Remove connection status callback
     * @param callback Callback interface
     */
    public void removeCallback(EventBrokerCallback callback) {
        if (callback != null) {
            mCallbacks.remove(callback);
        }
    }

    /**
     * Clear all callbacks
     */
    public void clearCallbacks() {
        mCallbacks.clear();
    }

    /**
     * Ensure EventBroker connection
     */
    private synchronized void ensureConnection() {
        if (!mIsConnected && !mIsConnecting) {
            if (mContext != null) {
                connectToEventBroker();
            } else {
                EventProviderApp.LOG.w(TAG + ": Context is null, cannot connect to EventBroker");
            }
        }
    }

    /**
     * Connect to EventBroker service (reuses bindEventBroker logic from EventMonitorService)
     */
    private void connectToEventBroker() {
        if (mContext == null) {
            EventProviderApp.LOG.e(TAG + ": Context is null, cannot bind EventBroker service");
            return;
        }

        mIsConnecting = true;
        mServiceConnection = new EventBrokerServiceConnection();

        Intent intent = new Intent("com.thundercomm.brokeragent.SERVICE");
        intent.setClassName("vendor.thundercomm.brokeragent", 
                          "vendor.thundercomm.brokeragent.BrokerAgentService");

        boolean result = mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
        if (!result) {
            EventProviderApp.LOG.e(TAG + ": Failed to bind EventBroker service");
            mIsConnecting = false;
            notifyConnectionFailed("Failed to bind EventBroker service");
        }
    }

    /**
     * Execute JSON event publishing (based on pushJsonEvent logic in EventMonitorService)
     */
    private void executePublishJsonEvent(PendingJsonEvent event) {
        try {
            // Completely reuse EventEntry creation logic from pushJsonEvent
            EventEntry eventEntry = new EventEntry();
            eventEntry.header = new EventHeader();
            eventEntry.header.categoryId = event.category;
            eventEntry.header.typeId = event.type;
            eventEntry.header.timestamp = System.currentTimeMillis();
            eventEntry.body = new EventBody();
            eventEntry.body.name = event.name;
            eventEntry.body.payloadType = (byte) event.payloadType;
            eventEntry.body.payloadData = event.content.getBytes(StandardCharsets.UTF_8);
            eventEntry.body.payloadSize = eventEntry.body.payloadData.length;

            mEventBroker.publishEvent(eventEntry);
            EventProviderApp.LOG.d(TAG + ": JSON event published successfully - " + event.name);
        } catch (RemoteException ex) {
            EventProviderApp.LOG.e(TAG + ": Failed to publish JSON event - " + event.name, ex);
        }
    }

    /**
     * Process pending queue after successful connection
     */
    private void processPendingOperations() {
        // Process pending events
        while (!mPendingEvents.isEmpty()) {
            PendingJsonEvent event = mPendingEvents.poll();
            if (event != null) {
                executePublishJsonEvent(event);
            }
        }
    }

    /**
     * Notify all callbacks of successful connection
     * @param name Service component name
     * @param broker EventBroker interface instance
     */
    private void notifyConnected(ComponentName name, IBinder broker) {
        for (EventBrokerCallback callback : mCallbacks) {
            try {
                callback.onConnected(name, broker);
            } catch (Exception e) {
                EventProviderApp.LOG.w(TAG + ": Error in callback onConnected", e);
            }
        }
    }

    /**
     * Notify all callbacks of disconnection
     * @param name Service component name
     */
    private void notifyDisconnected(ComponentName name) {
        for (EventBrokerCallback callback : mCallbacks) {
            try {
                callback.onDisconnected(name);
            } catch (Exception e) {
                EventProviderApp.LOG.w(TAG + ": Error in callback onDisconnected", e);
            }
        }
    }

    /**
     * Notify all callbacks of binding died
     * @param name Service component name
     */
    private void onBindingDied(ComponentName name) {
        for (EventBrokerCallback callback : mCallbacks) {
            try {
                callback.onBindingDied(name);
            } catch (Exception e) {
                EventProviderApp.LOG.w(TAG + ": Error in callback onDisconnected", e);
            }
        }
    }

    /**
     * Notify all callbacks of connection failure
     * @param error Error message
     */
    private void notifyConnectionFailed(String error) {
        for (EventBrokerCallback callback : mCallbacks) {
            try {
                callback.onConnectionFailed(error);
            } catch (Exception e) {
                EventProviderApp.LOG.w(TAG + ": Error in callback onConnectionFailed", e);
            }
        }
    }

    /**
     * Disconnect
     */
    public synchronized void disconnect() {
        if (mServiceConnection != null && mContext != null) {
            try {
                mContext.unbindService(mServiceConnection);
            } catch (Exception ex) {
                EventProviderApp.LOG.w(TAG + ": Error unbinding service", ex);
            }
        }
        mIsConnected = false;
        mIsConnecting = false;
        mEventBroker = null;
        mServiceConnection = null;
    }

    /**
     * Check if connected
     */
    public boolean isConnected() {
        return mIsConnected && mEventBroker != null;
    }

    // Internal class: Pending JSON event
    private static class PendingJsonEvent {
        final byte category;
        final int type;
        final String name;
        final String content;
        final int payloadType;

        PendingJsonEvent(byte category, int type, String name, String content, int payloadType) {
            this.category = category;
            this.type = type;
            this.name = name;
            this.content = content;
            this.payloadType = payloadType;
        }
    }

    // Internal class: ServiceConnection implementation (reuses logic from EventMonitorService)
    private class EventBrokerServiceConnection implements ServiceConnection {

        /**
         * Called when a connection to the Service has been established, with
         * the {@link IBinder} of the communication channel to the
         * Service.
         *
         * <p class="note"><b>Note:</b> If the system has started to bind your
         * client app to a service, it's possible that your app will never receive
         * this callback. Your app won't receive a callback if there's an issue with
         * the service, such as the service crashing while being created.
         *
         * @param name    The concrete component name of the service that has
         *                been connected.
         * @param service The IBinder of the Service's communication channel,
         *                which you can now make calls on.
         */
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mIBinder = service;
            mEventBroker = IEventBroker.Stub.asInterface(service);
            if (mEventBroker == null) {
                EventProviderApp.LOG.w(TAG + ": IEventBroker is null, cannot register event observer");
                mIsConnecting = false;
                notifyConnectionFailed("IEventBroker is null");
                return;
            }

            mIsConnected = true;
            mIsConnecting = false;
            EventProviderApp.LOG.d(TAG + ": IEventBroker is connected");

            // Notify all callbacks of successful connection
            notifyConnected(name, mIBinder);

            // Process pending operations
            processPendingOperations();
        }

        /**
         * Called when a connection to the Service has been lost.  This typically
         * happens when the process hosting the service has crashed or been killed.
         * This does <em>not</em> remove the ServiceConnection itself -- this
         * binding to the service will remain active, and you will receive a call
         * to {@link #onServiceConnected} when the Service is next running.
         *
         * @param name The concrete component name of the service whose
         *             connection has been lost.
         */
        @Override
        public void onServiceDisconnected(ComponentName name) {
            EventProviderApp.LOG.d(TAG + ": IEventBroker is disconnected");
            mIsConnected = false;
            mEventBroker = null;

            // Notify all callbacks of disconnection
            notifyDisconnected(name);
        }

        /**
         * Called when the binding to this connection is dead.  This means the
         * interface will never receive another connection.  The application will
         * need to unbind and rebind the connection to activate it again.  This may
         * happen, for example, if the application hosting the service it is bound to
         * has been updated.
         *
         * <p class="note"><b>Note:</b> The app that requested the binding must call
         * {@link Context#unbindService(ServiceConnection)} to release the tracking
         * resources associated with this ServiceConnection even if this callback was
         * invoked following {@link Context#bindService Context.bindService() bindService()}.
         *
         * @param name The concrete component name of the service whose connection is dead.
         */
        @Override
        public void onBindingDied(ComponentName name) {
            ServiceConnection.super.onBindingDied(name);
            onBindingDied(name);
        }
    }

    // Internal class: Default event observer (reuses logic from EventMonitorService)
    private static class DefaultEventObserver extends IEventObserver.Stub {
        @Override
        public void onEventReceived(EventEntry eventEntry) throws RemoteException {
            EventProviderApp.LOG.d(TAG + ": Event received - " + 
                                 (eventEntry.body != null ? eventEntry.body.name : "unknown"));
        }

        @Override
        public int getInterfaceVersion() throws RemoteException {
            return 0;
        }

        @Override
        public String getInterfaceHash() throws RemoteException {
            return "";
        }
    }
}
