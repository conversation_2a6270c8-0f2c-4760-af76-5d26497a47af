/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.utils;

public class StatesUtils {
    // Upper limit for CPU temperature, At 86 ℃,
    // enter standby mode (BLE set to AirMode, WLAN set to OFF, BLE set to OFF,
    // unable to perform normal recording/Event recording/Parking recording)
    public static final float TEMP_UPPER_LIMIT = 86.0f;
    // Reduce to 74 ℃, exit the above TEMP_UPPER_LIMIT state, and restore normal state again
    public static final float TEMP_RESTORE_NORMAL = 74.0f;
    public static final int TEMP_STATE_OVERHEAD = 0x01;
    public static final int TEMP_STATE_RESTORE_NORMAL = 0x00;
    private static final int BIT_SHIFT_LEFT_4 = 4;
    private static final int BIT_SHIFT_LEFT_3 = 3;
    private static final int BIT_SHIFT_LEFT_2 = 2;
    private static final int BIT_SHIFT_LEFT_1 = 1;
    private static final int FLAG_LTE_ENABLED = 1; // 0001
    private static final int FLAG_BLE_ENABLED = 1 << BIT_SHIFT_LEFT_1; // 0010
    private static final int FLAG_GPS_ENABLED = 1 << BIT_SHIFT_LEFT_2; // 0100
    private static final int FLAG_WIFI_ENABLED = 1 << BIT_SHIFT_LEFT_3; // 1000
    private static final int FLAG_WLAN_ENABLED = 1 << BIT_SHIFT_LEFT_4; // 01 0000
    private static StatesUtils instance;

    private float mLastCpuTemperature = 0.0f;
    // LTE airplane mode, BLE off, GPS off, Wi-Fi off, WLAN off etc.
    private int mLastFlags = 0;

    private StatesUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Get the singleton instance of StatesUtils.
     *
     * @return the singleton instance of StatesUtils.
     */
    public static synchronized StatesUtils getInstance() {
        if (instance == null) {
            instance = new StatesUtils();
        }
        return instance;
    }

    /**
     * Get the last CPU temperature.
     *
     * @return the last CPU temperature.
     */
    public float getLastCpuTemperature() {
        return mLastCpuTemperature;
    }

    /**
     * Set the last CPU temperature.
     *
     * @param temperature the last CPU temperature to set.
     */
    public void setLastCpuTemperature(float temperature) {
        this.mLastCpuTemperature = temperature;
    }

    /**
     * Get the last flags representing the state of various features.
     *
     * @return the last flags, representing the state of LTE, BLE, GPS, Wi-Fi, and WLAN.
     */
    public int getLastFlags() {
        return mLastFlags;
    }

    /**
     * Set the last flags representing the state of various features.
     *
     * @param flags the flags to set, representing the state of LTE, BLE, GPS, Wi-Fi, and WLAN.
     */
    public void setLastFlags(int flags) {
        this.mLastFlags = flags;
    }

    /**
     * Check if the last state has LTE enabled.
     *
     * @return true if LTE is enabled, false otherwise.
     */
    public boolean isLastLteEnabled() {
        return (mLastFlags & FLAG_LTE_ENABLED) != 0;
    }

    /**
     * Set the last state for LTE enabled or disabled.
     *
     * @param enabled true to enable LTE, false to disable.
     */
    public void setLastLteEnabled(boolean enabled) {
        if (enabled) {
            mLastFlags |= FLAG_LTE_ENABLED;
        } else {
            mLastFlags &= ~FLAG_LTE_ENABLED;
        }
    }

    /**
     * Check if the last state has BLE enabled.
     *
     * @return true if BLE is enabled, false otherwise.
     */
    public boolean isLastBleEnabled() {
        return (mLastFlags & FLAG_BLE_ENABLED) != 0;
    }

    /**
     * Set the last state for BLE enabled or disabled.
     *
     * @param enabled true to enable BLE, false to disable.
     */
    public void setLastBleEnabled(boolean enabled) {
        if (enabled) {
            mLastFlags |= FLAG_BLE_ENABLED;
        } else {
            mLastFlags &= ~FLAG_BLE_ENABLED;
        }
    }

    /**
     * Check if the last state has GPS enabled.
     *
     * @return true if GPS is enabled, false otherwise.
     */
    public boolean isLastGpsEnabled() {
        return (mLastFlags & FLAG_GPS_ENABLED) != 0;
    }

    /**
     * Set the last state for GPS enabled or disabled.
     *
     * @param enabled true to enable GPS, false to disable.
     */
    public void setLastGpsEnabled(boolean enabled) {
        if (enabled) {
            mLastFlags |= FLAG_GPS_ENABLED;
        } else {
            mLastFlags &= ~FLAG_GPS_ENABLED;
        }
    }

    /**
     * Check if the last state has Wi-Fi enabled.
     *
     * @return true if Wi-Fi is enabled, false otherwise.
     */
    public boolean isLastWifiEnabled() {
        return (mLastFlags & FLAG_WIFI_ENABLED) != 0;
    }

    /**
     * Set the last state for Wi-Fi enabled or disabled.
     *
     * @param enabled true to enable Wi-Fi, false to disable.
     */
    public void setLastWifiEnabled(boolean enabled) {
        if (enabled) {
            mLastFlags |= FLAG_WIFI_ENABLED;
        } else {
            mLastFlags &= ~FLAG_WIFI_ENABLED;
        }
    }

    /**
     * Check if the last state has WLAN enabled.
     *
     * @return true if WLAN is enabled, false otherwise.
     */
    public boolean isLastWlanEnabled() {
        return (mLastFlags & FLAG_WLAN_ENABLED) != 0;
    }

    /**
     * Set the last state for WLAN enabled or disabled.
     *
     * @param enabled true to enable WLAN, false to disable.
     */
    public void setLastWlanEnabled(boolean enabled) {
        if (enabled) {
            mLastFlags |= FLAG_WLAN_ENABLED;
        } else {
            mLastFlags &= ~FLAG_WLAN_ENABLED;
        }
    }

    /**
     * Check if the last state has all features disabled.
     *
     * @return true if all features are disabled, false otherwise.
     */
    public boolean isLastAllDisabled() {
        return mLastFlags == 0;
    }

    /**
     * Check if the last state has all features enabled.
     *
     * @return true if all features are enabled, false otherwise.
     */
    public boolean isLastAllEnabled() {
        return (mLastFlags & (FLAG_LTE_ENABLED | FLAG_BLE_ENABLED | FLAG_GPS_ENABLED
                | FLAG_WIFI_ENABLED | FLAG_WLAN_ENABLED))
                == (FLAG_LTE_ENABLED | FLAG_BLE_ENABLED | FLAG_GPS_ENABLED
                | FLAG_WIFI_ENABLED | FLAG_WLAN_ENABLED);
    }
}
