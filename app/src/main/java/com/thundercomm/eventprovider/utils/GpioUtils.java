package com.thundercomm.eventprovider.utils;

import com.thundercomm.eventprovider.EventProviderApp;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;

public class GpioUtils {

    public static final String GPIO_79_PATH = "/sys/devices/platform/soc/soc:gpio_info/qcm_info_mcu2_gpio79";

    /**
     * Set gpio value.
     *
     * @param filePath gpio file path
     * @param target   value
     * @return true if success, false otherwise.
     */
    public static boolean writeGpioFile(String filePath, String target) {
        EventProviderApp.LOG.i("writeGpioFile: filePath=" + filePath + ", target=" + target);
        File nodeFile = new File(filePath);
        if (!nodeFile.exists()) {
            EventProviderApp.LOG.w("writeGpioFile: filePath=" + filePath + " NOT exists.");
            return false;
        }
        try (Writer outputStream = new BufferedWriter(new FileWriter(filePath))) {
            outputStream.write(target);
            return true;
        } catch (IOException ex) {
            EventProviderApp.LOG.w("writeGpioFile: failed", ex);
            return false;
        }
    }
}
