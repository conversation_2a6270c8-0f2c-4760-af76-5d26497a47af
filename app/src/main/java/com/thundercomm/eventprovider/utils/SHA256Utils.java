/*
 * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
 */

package com.thundercomm.eventprovider.utils;

import com.thundercomm.eventprovider.EventProviderApp;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * The class for calculating the SHA-256 hash of a file.
 */
public final class SHA256Utils {

    /**
     * Calculate the SHA-256 hash of the file.
     *
     * @param content content
     * @return SHA-256 hash value (hexadecimal string), null returned if it fails
     */
    public static String calculateSHA256(String content) {
        MessageDigest digest;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException ex) {
            EventProviderApp.LOG.w("SHA-256 algorithm not found", ex);
            return null;
        }
        // Convert to a hexadecimal string
        return bytesToHex(digest.digest(content.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * Byte arrays to hexadecimal strings.
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
