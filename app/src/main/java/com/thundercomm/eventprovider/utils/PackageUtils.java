/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.text.TextUtils;
import android.util.Base64;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

public final class PackageUtils {

    public static final int RESULT_SUCCESS = 0;
    public static final int RESULT_FAILED = 1;
    public static final int RESULT_NOT_FOUND = 2;

    /**
     * Checks whether the device has an application with the specified package name installed.
     *
     * @param context     context
     * @param packageName app package name
     * @return true if the app is installed, false otherwise
     */
    public static boolean isAppInstalled(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException ex) {
            return false;
        }
    }

    /**
     * Get app version name.
     *
     * @param context     context
     * @param packageName app package name
     * @return app version name
     */
    public static String getAppVersionName(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(packageName, 0);
            return packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException ex) {
            return "";
        }
    }

    /**
     * Is the app can be uninstalled.
     *
     * @param context     context
     * @param packageName app package name
     * @return 0: success, 1: failed, 2: not found
     */
    public static int checkAppSignature(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_SIGNATURES);
            Signature[] signatures = packageInfo.signatures;
            String encryptionPublicKey = "";
            for (Signature signature : signatures) {
                byte[] sign = signature.toByteArray();
                try (InputStream input = new ByteArrayInputStream(sign)) {
                    CertificateFactory cf = CertificateFactory.getInstance("X.509");
                    X509Certificate cert = (X509Certificate) cf.generateCertificate(input);
                    byte[] pubKeyEncoded = cert.getPublicKey().getEncoded();
                    String pubKeyBase64 = Base64.encodeToString(pubKeyEncoded, Base64.NO_WRAP);
                    encryptionPublicKey = SHA256Utils.calculateSHA256(pubKeyBase64);
                    break;
                }
            }
            if (TextUtils.isEmpty(encryptionPublicKey)) {
                EventProviderApp.LOG.w("checkAppSignature error: public key is null !");
                return RESULT_FAILED;
            }
            if (encryptionPublicKey.equals(ActionConstants.SIGNATURE_SSOL)) {
                return RESULT_SUCCESS;
            } else {
                return RESULT_FAILED;
            }
        } catch (PackageManager.NameNotFoundException ex) {
            EventProviderApp.LOG.w("checkAppSignature not found: ", ex);
            return RESULT_NOT_FOUND;
        } catch (Exception ex) {
            EventProviderApp.LOG.w("checkAppSignature failed: ", ex);
        }
        return RESULT_FAILED;
    }
}