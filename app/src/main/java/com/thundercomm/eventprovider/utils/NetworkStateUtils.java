/*
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 */

package com.thundercomm.eventprovider.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.LinkAddress;
import android.net.LinkProperties;
import android.net.Network;
import android.os.Build;
import android.telephony.CellIdentity;
import android.telephony.CellInfo;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;

import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;

import com.thundercomm.eventprovider.EventProviderApp;

import java.lang.reflect.Method;
import java.net.InetAddress;
import java.util.List;

public class NetworkStateUtils {
    private static final String NET_CONN_VALUE_NOSIGNAL = "No Signal";
    private static final String NET_CONN_VALUE_IDLE = "Idle";
    private static final String NET_CONN_VALUE_IN_COMMUNICATION = "In Communication";
    private static final int HIDDEN_CODE_LENGTH = 4;
    private static final int SIM_ACTIVATION_STATE_ACTIVATED = 2;

    /**
     *  Return ipv4 or ipv6 by inetClass.
     * @param context the application context used to access resources and services
     * @param inetClass An instance of InetAddress representing the network address
     * @return Return IPv4 or IPv6 based on input parameters. If it does not exist, return an empty string
     */
    public static String getIpAddress(Context context, Class<? extends InetAddress> inetClass) {
        String ip = "";
        List<LinkAddress> linkAddresses = getLinkAddress(context);
        if (linkAddresses == null) {
            EventProviderApp.LOG.d("NetworkStateUtils: No InetAddress");
            return "";
        }

        for (LinkAddress address : linkAddresses) {
            InetAddress netAddress = address.getAddress();
            if (inetClass.isInstance(netAddress)) {
                ip = netAddress.getHostAddress();
                EventProviderApp.LOG.d("NetworkStateUtils: IP = " + ip);
                break;
            }
        }
        return ip;
    }

    private static List<LinkAddress> getLinkAddress(Context context) {
        List<LinkAddress> linkAddresses = null;
        ConnectivityManager cm = context.getSystemService(ConnectivityManager.class);
        Network activeNetwork = cm.getActiveNetwork();
        if (activeNetwork == null) {
            EventProviderApp.LOG.d("NetworkStateUtils:  activeNetwork == null");
            return linkAddresses;
        }
        LinkProperties lp = cm.getLinkProperties(activeNetwork);
        if (lp != null) {
            linkAddresses = lp.getLinkAddresses();
            return linkAddresses;
        } else {
            EventProviderApp.LOG.e("NetworkStateUtils:  LinkProperties == null");
            return linkAddresses;
        }
    }

    /**
     * Return ipv4.
     * @param context the application context used to access resources and services
     * @return Return network connection status
     */
    @RequiresApi(api = Build.VERSION_CODES.P)
    @SuppressLint("MissingPermission")
    public static String getNetWorkConnection(Context context) {
        TelephonyManager tm = (TelephonyManager)
                context.getSystemService(Context.TELEPHONY_SERVICE);
        EventProviderApp.LOG.i("NetworkStateUtils SimState: " + tm.getSimState());
        EventProviderApp.LOG.i("NetworkStateUtils NetworkType: " + tm.getDataNetworkType());

        try {
            int state = tm.getDataActivationState();
            EventProviderApp.LOG.i("NetworkStateUtils getDataActivationState : " + state);
            if (SIM_ACTIVATION_STATE_ACTIVATED == state) {
                int dataActivity = tm.getDataActivity();
                if (TelephonyManager.DATA_ACTIVITY_NONE == dataActivity ||
                        TelephonyManager.DATA_ACTIVITY_DORMANT == dataActivity) {
                    return NET_CONN_VALUE_IDLE;
                } else {
                    return NET_CONN_VALUE_IN_COMMUNICATION;
                }
            } else {
                return NET_CONN_VALUE_NOSIGNAL;
            }
        } catch (Exception exception) {
            EventProviderApp.LOG.e("NetworkStateUtils Exception : " + exception);
            return NET_CONN_VALUE_NOSIGNAL;
        }
    }

    /**
     * Return SIM card phone number
     * @param context the application context used to access resources and services
     * @return Return the phone number of the SIM card
     */
    public static String getPhoneNumber(Context context) {
        TelephonyManager tm = (TelephonyManager)
                context.getSystemService(Context.TELEPHONY_SERVICE);
        @SuppressLint("MissingPermission")
        String phoneNumber = tm.getLine1Number();
        EventProviderApp.LOG.i("NetworkStateUtils phoneNumber: " + maskPhoneNumber(phoneNumber));
        return phoneNumber;
    }

    private static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() <= 4) {
            return phoneNumber;
        }
        return "****" + phoneNumber.substring(phoneNumber.length() - HIDDEN_CODE_LENGTH);
    }

    /**
     * Return Obtain LTE network information
     * @param context the application context used to access resources and services
     * @return Return LTE network information
     */
    @RequiresApi(api = Build.VERSION_CODES.P)
    public static int getLteRsrp(Context context) {
        TelephonyManager tm = (TelephonyManager)
                context.getSystemService(Context.TELEPHONY_SERVICE);
        SignalStrength signalStrength = tm.getSignalStrength();
        try {
            int value = signalStrength.getLteRsrp();
            EventProviderApp.LOG.i("NetworkStateUtils getLteRsrp : " + value);
            return value;
        } catch (Exception exception) {
            EventProviderApp.LOG.e("NetworkStateUtils Exception : " + exception);
            return -1;
        }
    }

    /**
     * Return Obtain LTE network information
     * @param context the application context used to access resources and services
     * @return Return LTE network information
     */
    @RequiresApi(api = Build.VERSION_CODES.P)
    public static int getLteRsrq(Context context) {
        TelephonyManager tm = (TelephonyManager)
                context.getSystemService(Context.TELEPHONY_SERVICE);
        SignalStrength signalStrength = tm.getSignalStrength();
        try {
            int value = signalStrength.getLteRsrq();
            EventProviderApp.LOG.i("NetworkStateUtils getLteRsrq : " + value);
            return value;
        } catch (Exception exception) {
            EventProviderApp.LOG.e("NetworkStateUtils Exception : " + exception);
            return -1;
        }
    }

    /**
     * Return the cellular network information currently connected to the device
     * @param context the application context used to access resources and services
     * @return Return network information
     */
    @RequiresApi(api = Build.VERSION_CODES.R)
    public static String getCellId(Context context) {
        TelephonyManager tm = (TelephonyManager)
                context.getSystemService(Context.TELEPHONY_SERVICE);

        @SuppressLint("MissingPermission")
        List<CellInfo> cellInfos = tm.getAllCellInfo();
        EventProviderApp.LOG.i("NetworkStateUtils cellInfos : " + cellInfos.size());
        for (CellInfo cellInfo : cellInfos) {
            CellIdentity cellIdentity = cellInfo.getCellIdentity();
            if (cellIdentity == null) return "";
            try {
                @SuppressLint({"BlockedPrivateApi"})
                Method globalCellId = cellIdentity.getClass().getDeclaredMethod("getGlobalCellId");
                globalCellId.setAccessible(true);
                String cellId = (String) globalCellId.invoke(cellIdentity);
                EventProviderApp.LOG.i("NetworkStateUtils getGlobalCellId : " + cellId);
                return cellId;
            } catch (Exception exception) {
                EventProviderApp.LOG.e("NetworkStateUtils Exception : " + exception);
            }
        }
        return "";
    }
}
