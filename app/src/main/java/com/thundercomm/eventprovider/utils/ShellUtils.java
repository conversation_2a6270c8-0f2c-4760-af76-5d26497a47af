/*
 * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
 */

package com.thundercomm.eventprovider.utils;

import static com.thundercomm.eventprovider.EventProviderApp.LOG;

import android.text.TextUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;

public class ShellUtils {

    /**
     * Run shell command.
     *
     * @param commandName command name
     * @param command     command
     * @return result
     */
    public static String runShellCommand(String commandName, String command) {
        if (TextUtils.isEmpty(command)) {
            return "";
        }
        StringBuilder resultBuilder = new StringBuilder();
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(command);
            final int exitCode = process.waitFor();
            InputStreamReader inputStreamReader = new InputStreamReader(process.getInputStream());
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                if (resultBuilder.length() == 0) {
                    resultBuilder.append(line);
                } else {
                    resultBuilder.append("\n").append(line);
                }
            }
            bufferedReader.close();
            inputStreamReader.close();
            LOG.d(commandName + " command completed with exit code: " + exitCode + ", and result: "
                    + resultBuilder.toString());
        } catch (Exception ex) {
            LOG.e(commandName + " command error: " + ex);
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
        return resultBuilder.toString();
    }
}
