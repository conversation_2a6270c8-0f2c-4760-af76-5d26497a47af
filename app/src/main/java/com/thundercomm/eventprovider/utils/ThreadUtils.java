/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.utils;

import static com.thundercomm.eventprovider.EventProviderApp.LOG;

import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public class ThreadUtils {

    private static final int THREAD_POOL_SIZE = 2;
    private static volatile Thread sMainThread;
    private static volatile Handler sMainThreadHandler;
    private static volatile ExecutorService sThreadExecutor;
    private static final int TIMEOUT_CHECK_TERMINATE = 60;

    /**
     * Returns true if the current thread is the UI thread.
     */
    public static boolean isMainThread() {
        if (sMainThread == null) {
            sMainThread = Looper.getMainLooper().getThread();
        }
        return Thread.currentThread() == sMainThread;
    }

    /**
     * Returns a shared UI thread handler.
     */
    public static Handler getUiThreadHandler() {
        if (sMainThreadHandler == null) {
            sMainThreadHandler = new Handler(Looper.getMainLooper());
        }

        return sMainThreadHandler;
    }

    /**
     * Checks that the current thread is the UI thread. Otherwise throws an exception.
     */
    public static void ensureMainThread() {
        if (!isMainThread()) {
            throw new RuntimeException("Must be called on the UI thread");
        }
    }

    /**
     * Posts runnable in background using shared background thread pool.
     *
     * @Return A future of the task that can be monitored for updates or cancelled.
     */
    public static Future postOnBackgroundThread(Runnable runnable) {
        return getThreadExecutor().submit(runnable);
    }

    /**
     * Posts callable in background using shared background thread pool.
     *
     * @Return A future of the task that can be monitored for updates or cancelled.
     */
    public static Future postOnBackgroundThread(Callable callable) {
        return getThreadExecutor().submit(callable);
    }

    /**
     * Posts the runnable on the main thread.
     */
    public static void postOnMainThread(Runnable runnable) {
        getUiThreadHandler().post(runnable);
    }

    /**
     * Posts the runnable on the main thread with a delay.
     */
    public static void postOnMainThreadDelayed(Runnable runnable, long delayMillis) {
        getUiThreadHandler().postDelayed(runnable, delayMillis);
    }

    private static synchronized ExecutorService getThreadExecutor() {
        if (sThreadExecutor == null) {
            sThreadExecutor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        }
        return sThreadExecutor;
    }

    /**
     * Shuts down the thread executor service.
     */
    public static void shutdownThreadExecutor() {
        if (sThreadExecutor != null && !sThreadExecutor.isShutdown()) {
            try {
                sThreadExecutor.shutdown();
                if (!sThreadExecutor.awaitTermination(TIMEOUT_CHECK_TERMINATE, TimeUnit.SECONDS)) {
                    sThreadExecutor.shutdownNow();
                    if (!sThreadExecutor.awaitTermination(TIMEOUT_CHECK_TERMINATE, TimeUnit.SECONDS)) {
                        LOG.e("Thread pool did not terminate");
                    }
                }
            } catch (InterruptedException ie) {
                sThreadExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            sThreadExecutor = null;
        }
    }
}
