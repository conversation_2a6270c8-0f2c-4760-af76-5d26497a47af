/*
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 *  Not a contribution.
 */

package com.thundercomm.eventprovider;

import android.app.Application;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ApplicationInfo;
import android.text.TextUtils;

import com.thundercomm.eventprovider.anno.ControllerInfo;
import com.thundercomm.eventprovider.anno.EventListenerInfo;
import com.thundercomm.eventprovider.anno.SenderInfo;
import com.thundercomm.eventprovider.core.IController;
import com.thundercomm.eventprovider.core.IEventListener;
import com.thundercomm.eventprovider.core.ISender;
import com.thundercomm.eventprovider.manager.ConfigManager;
import com.thundercomm.eventprovider.manager.TsnvManager;
import com.thundercomm.eventprovider.utils.GpioUtils;
import com.thundercomm.eventprovider.utils.Logger;
import com.thundercomm.eventprovider.utils.ThreadUtils;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import dalvik.system.DexFile;

public class EventProviderApp extends Application {

    private static final String TAG = "EventProviderApp";
    private static final String CONTROLLER_CASES_PACKAGE = "com.thundercomm.eventprovider.controller";
    private static final String LISTENER_CASES_PACKAGE = "com.thundercomm.eventprovider.listener";
    private static final String SENDER_CASES_PACKAGE = "com.thundercomm.eventprovider.send";
    private static final Map<String, IController> CONTROLLERS = new HashMap<>();
    private static final Map<Byte, IEventListener> EVENT_LISTENERS = new HashMap<>();
    private static final Map<String, ISender> SENDERS = new HashMap<>();
    public static final Logger LOG = new Logger(EventProviderApp.class);

    /**
     * Called when the application is starting, before any activity, service, or receiver objects (including content providers) have been created.
     * This method is used to perform one-time initialization of the application.
     */
    @Override
    public void onCreate() {
        super.onCreate();
        new Thread(
                () -> {
                    try {
                        initControllers();
                    } catch (Exception ex) {
                        LOG.w("onCreate: Exception occurred while initializing controllers", ex);
                    }
                    try {
                        initSenders();
                    } catch (Exception ex) {
                        LOG.w("onCreate: Exception occurred while initializing senders", ex);
                    }
                    try {
                        initListeners();
                    } catch (Exception ex) {
                        LOG.w("onCreate: Exception occurred while initializing listeners", ex);
                    }
                    try {
                        ConfigManager.getInstance(getApplicationContext()).init();
                    } catch (Exception ex) {
                        LOG.w("onCreate: Exception occurred while initializing ConfigManager", ex);
                    }
                    startMonitorService();
                    // Write the gpio high after boot.
                    try {
                        boolean result79 = GpioUtils.writeGpioFile(GpioUtils.GPIO_79_PATH, "1");
                        LOG.i("writeGpioBoot: result79=" + result79);
                    } catch (Exception ex) {
                        LOG.w("writeGpioBoot: failed", ex);
                    }
                }
        ).start();
    }


    @Override
    public void onTerminate() {
        super.onTerminate();
        CONTROLLERS.clear();
        SENDERS.clear();
        EVENT_LISTENERS.clear();
        ThreadUtils.shutdownThreadExecutor();
    }

    /**
     * Starts the EventMonitorService to monitor events.
     * This service is responsible for handling event monitoring tasks.
     */
    private void startMonitorService() {
        Intent intent = new Intent("com.thundercomm.eventprovider.action.MONITOR")
                .setPackage("com.thundercomm.eventprovider")
                .setClassName("com.thundercomm.eventprovider",
                        "com.thundercomm.eventprovider.EventMonitorService");
        startService(intent);
    }

    /**
     * Initializes listeners for the application.
     * This method is responsible for setting up any listeners needed by the application.
     */
    private void initListeners() throws Exception {
        ApplicationInfo info = getPackageManager().getApplicationInfo(getPackageName(), 0);
        String apkPath = info.sourceDir;
        LOG.d("initListeners: APK path: " + apkPath);
        DexFile dexFile = new DexFile(apkPath);
        try {
            Enumeration<String> entries = dexFile.entries();
            while (entries.hasMoreElements()) {
                String className = entries.nextElement();
                if (className.startsWith(LISTENER_CASES_PACKAGE)) {
                    Class<?> clazz;
                    try {
                        clazz = Class.forName(className);
                    } catch (ClassNotFoundException ex) {
                        continue;
                    }
                    EventListenerInfo annotation = clazz.getAnnotation(EventListenerInfo.class);
                    if (annotation != null) {
                        boolean need2Monitor = annotation.isNeed2Monitor();
                        if (!need2Monitor) {
                            LOG.d("initListeners: Listener " + className + " does not need to monitor events");
                            continue;
                        }
                        byte categoryId = annotation.categoryId();
                        int[] eventTypes = annotation.eventTypes();
                        Object instance = clazz.getDeclaredConstructor().newInstance();
                        if (instance instanceof IEventListener) {
                            IEventListener listener = (IEventListener) instance;
                            listener.setContext(this);
                            listener.setEventTypes(eventTypes);
                            listener.setCategoryId(categoryId);
                            EVENT_LISTENERS.put(categoryId, listener);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOG.w("initControllers: Exception occurred while initializing controllers", ex);
        } finally {
            try {
                dexFile.close();
            } catch (IOException ex) {
                LOG.w("initControllers: Failed to close DexFile", ex);
            }
        }
    }

    /**
     * Initializes controllers for the application.
     * This method is responsible for setting up any controllers needed by the application.
     */
    private void initControllers() throws Exception {

        ApplicationInfo info = getPackageManager().getApplicationInfo(getPackageName(), 0);
        String apkPath = info.sourceDir;
        LOG.d("initControllers: APK path: " + apkPath);
        DexFile dexFile = new DexFile(apkPath);
        try {
            Enumeration<String> entries = dexFile.entries();
            while (entries.hasMoreElements()) {
                String className = entries.nextElement();
                if (className.startsWith(CONTROLLER_CASES_PACKAGE)) {
                    Class<?> clazz;
                    try {
                        clazz = Class.forName(className);
                    } catch (ClassNotFoundException ex) {
                        continue;
                    }
                    ControllerInfo annotation = clazz.getAnnotation(ControllerInfo.class);
                    if (annotation != null) {
                        String action = annotation.action();
                        Object instance = clazz.getDeclaredConstructor().newInstance();
                        if (instance instanceof IController) {
                            IController controller = (IController) instance;
                            if (!TextUtils.isEmpty(action)) {
                                controller.setAction(action);
                                controller.setContext(this);
                                CONTROLLERS.put(action, controller);
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOG.w("initControllers: Exception occurred while initializing controllers", ex);
        } finally {
            try {
                dexFile.close();
            } catch (IOException ex) {
                LOG.w("initControllers: Failed to close DexFile", ex);
            }
        }
    }

    /**
     * Initializes senders for the application.
     * This method is responsible for setting up any senders needed by the application.
     */
    private void initSenders() throws Exception {
        ApplicationInfo info = getPackageManager().getApplicationInfo(getPackageName(), 0);
        String apkPath = info.sourceDir;
        LOG.d("initSenders: APK path: " + apkPath);
        DexFile dexFile = new DexFile(apkPath);
        try {
            Enumeration<String> entries = dexFile.entries();
            while (entries.hasMoreElements()) {
                String className = entries.nextElement();
                if (className.startsWith(SENDER_CASES_PACKAGE)) {
                    Class<?> clazz = Class.forName(className);
                    SenderInfo annotation = clazz.getAnnotation(SenderInfo.class);
                    if (annotation != null) {
                        String action = annotation.action();
                        Object instance = clazz.getDeclaredConstructor().newInstance();
                        if (instance instanceof ISender) {
                            ISender sender = (ISender) instance;
                            if (!TextUtils.isEmpty(action)) {
                                sender.setAction(action);
                                sender.setContext(this);
                                SENDERS.put(action, sender);
                                LOG.d("initSenders: Registered sender for action: " + action);
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOG.w("initSenders: Exception occurred while initializing senders", ex);
        } finally {
            try {
                dexFile.close();
            } catch (IOException ex) {
                LOG.w("initSenders: Failed to close DexFile", ex);
            }
        }
    }

    /**
     * Gets a controller for a specific action.
     *
     * @param action The action string associated with the controller.
     * @return controller The controller instance to be registered.
     */
    public static IController getController(String action) {
        return CONTROLLERS.get(action);
    }

    /**
     * Gets a sender for a specific action.
     *
     * @param action The action string associated with the sender.
     * @return sender The sender instance to be registered.
     */
    public static ISender getSender(String action) {
        return SENDERS.get(action);
    }

    public static boolean isNeedRegisterReceiver() {
        return !CONTROLLERS.isEmpty();
    }

    public static IntentFilter getIntentFilter() {
        if (CONTROLLERS.isEmpty()) {
            return null;
        }
        String[] array = CONTROLLERS.keySet().toArray(new String[0]);
        IntentFilter filter = new IntentFilter();
        for (String action : array) {
            LOG.d("getIntentFilter: Adding action " + action);
            filter.addAction(action);
        }
        return filter;
    }


    public static IEventListener getEventListener(byte categoryId) {
        return EVENT_LISTENERS.get(categoryId);
    }

    public static Map<Byte, IEventListener> getEventListeners() {
        return EVENT_LISTENERS;
    }

}
