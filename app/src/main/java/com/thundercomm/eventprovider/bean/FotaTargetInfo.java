/*
 * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
 */

package com.thundercomm.eventprovider.bean;

import java.io.Serializable;

/**
 * The class for fota target info.
 * "fotaNo": "2",
 * "targetName": "CS3",
 * "fotaType": "yosemite",
 * "fotaServer": "0",
 * "userAgent": "SSOLMS/1.0(CS3;Unit;000[1.0])"
 */
public class FotaTargetInfo implements Serializable {

    private String fotaNo;
    private String targetName;
    private String fotaType;
    private String fotaServer;
    private String userAgent;
    //Extra info
    private String currentVersion;
    private String url;

    public String getFotaNo() {
        return fotaNo;
    }

    public void setFotaNo(String fotaNo) {
        this.fotaNo = fotaNo;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getFotaType() {
        return fotaType;
    }

    public void setFotaType(String fotaType) {
        this.fotaType = fotaType;
    }

    public String getFotaServer() {
        return fotaServer;
    }

    public void setFotaServer(String fotaServer) {
        this.fotaServer = fotaServer;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getCurrentVersion() {
        return currentVersion;
    }

    public void setCurrentVersion(String currentVersion) {
        this.currentVersion = currentVersion;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return "FotaTargetInfo{" +
                "fotaNo='" + fotaNo + '\'' +
                ", targetName='" + targetName + '\'' +
                ", fotaType='" + fotaType + '\'' +
                ", fotaServer='" + fotaServer + '\'' +
                ", userAgent='" + userAgent + '\'' +
                '}';
    }
}
