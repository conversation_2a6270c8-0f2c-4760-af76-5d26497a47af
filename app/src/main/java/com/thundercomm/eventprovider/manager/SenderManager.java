/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.manager;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.core.ISender;

/**
 * Manager for broadcast senders.
 * Provides a unified interface to send broadcasts through registered senders.
 */
public class SenderManager {

    private static SenderManager sInstance;

    /**
     * Get the singleton instance of SenderManager.
     *
     * @return SenderManager instance
     */
    public static SenderManager getInstance() {
        if (sInstance == null) {
            synchronized (SenderManager.class) {
                if (sInstance == null) {
                    sInstance = new SenderManager();
                }
            }
        }
        return sInstance;
    }

    private SenderManager() {
        // Private constructor for singleton
    }

    /**
     * Send broadcast using the sender registered for the given action.
     *
     * @param action Broadcast action
     * @param params Parameters for the broadcast
     * @return true if sender was found and broadcast was sent, false otherwise
     */
    public boolean sendBroadcast(String action, Object... params) {
        ISender sender = EventProviderApp.getSender(action);
        if (sender != null) {
            try {
                sender.sendBroadcast(params);
                EventProviderApp.LOG.d("SenderManager: Broadcast sent successfully for action: " + action);
                return true;
            } catch (Exception ex) {
                EventProviderApp.LOG.e("SenderManager: Failed to send broadcast for action: " + action, ex);
                return false;
            }
        } else {
            EventProviderApp.LOG.w("SenderManager: No sender found for action: " + action);
            return false;
        }
    }

    /**
     * Check if a sender is registered for the given action.
     *
     * @param action Broadcast action
     * @return true if sender is registered, false otherwise
     */
    public boolean hasSender(String action) {
        return EventProviderApp.getSender(action) != null;
    }
}
