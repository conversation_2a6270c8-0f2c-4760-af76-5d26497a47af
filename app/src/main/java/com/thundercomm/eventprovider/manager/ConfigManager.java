package com.thundercomm.eventprovider.manager;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.thundercomm.configtool.api.ConfigToolServiceManager;
import com.thundercomm.configtool.api.IConfigToolServiceConnectCallback;
import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.bean.FotaTargetInfo;
import com.thundercomm.eventprovider.utils.ThreadUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class ConfigManager {

    private static final String TAG = ConfigManager.class.getSimpleName();

    public static final String CONFIG_KEY_LCD = "lcdUse";
    public static final String CONFIG_KEY_VERSION = "configVersion";
    public static final String CONFIG_KEY_FOTA_TARGET_INFO = "fotaTargetInfo";
    public static final String CONFIG_VALUE_FOTA_TYPE_APK = "apk";
    public static final String CONFIG_VALUE_FOTA_TYPE_CONFIG = "config";
    public static final String CONFIG_VALUE_FOTA_TYPE_FIRMWARE = "firmware";

    private static volatile ConfigManager sInstance;

    private Context mContext;

    private ConfigToolServiceManager mConfigManager;
    private boolean mIsConfigServiceConnected = false;

    /**
     * Get the singleton instance of ConfigManager.
     *
     * @param context context for application
     * @return instance
     */
    public static ConfigManager getInstance(Context context) {
        if (sInstance == null) {
            synchronized (ConfigManager.class) {
                if (sInstance == null) {
                    sInstance = new ConfigManager(context);
                }
            }
        }
        return sInstance;
    }

    /**
     * Constructor method (Private).
     *
     * @param context context for application
     */
    private ConfigManager(Context context) {
        EventProviderApp.LOG.i("ConfigManager created");
        mContext = context.getApplicationContext();
    }

    /**
     * Init function.
     * To bind the config service.
     */
    public void init() {
        ThreadUtils.postOnBackgroundThread(() -> {
            mConfigManager = ConfigToolServiceManager.getInstance();
            // Set connection callback
            mConfigManager.setServiceConnectCallback(mConnection);
            mConfigManager.bindService(mContext);
        });
    }

    /**
     * Destroy function.
     */
    public void destroy() {
        if (mConfigManager != null) {
            mConfigManager.unbindService(mContext);
            mIsConfigServiceConnected = false;
        }
    }

    private final IConfigToolServiceConnectCallback mConnection = new IConfigToolServiceConnectCallback() {
        @Override
        public void onServiceConnected() {
            // Service is connected, you can now use the API
            EventProviderApp.LOG.d("onServiceConnected: IConfigToolService is connected");
            mIsConfigServiceConnected = true;
            //Init config for EventProvider
            initLcdSystemReady();
        }

        @Override
        public void onServiceDisconnected() {
            EventProviderApp.LOG.w("onServiceDisconnected: IConfigToolService is disconnected");
            mIsConfigServiceConnected = false;
        }

        @Override
        public void onConfigChanged(String modifiedFields, String addedFields, long timestamp) {
            EventProviderApp.LOG.d("onConfigChanged: modifiedFields=" + modifiedFields + ", addedFields="
                    + addedFields + ", timestamp=" + timestamp);
            if (TextUtils.isEmpty(modifiedFields)) {
                initLcdSystemReady();
                return;
            }
            try {
                JSONObject js = new JSONObject(modifiedFields);
                if (js.has(CONFIG_KEY_LCD)) {
                    boolean enabled = js.optBoolean(CONFIG_KEY_LCD);
                    setLcdDisabledToTsnv(!enabled);
                }
            } catch (JSONException ex) {
                EventProviderApp.LOG.w("onConfigChanged: JSONException: ", ex);
            } catch (Exception ex) {
                EventProviderApp.LOG.w("onConfigChanged: Exception: ", ex);
            }
        }
    };

    /**
     * Init LCD when system ready.
     */
    private void initLcdSystemReady() {
        EventProviderApp.LOG.d("initLcdSystemReady");
        boolean isLcdDisabled = isLcdDisabled(false);
        setLcdDisabledToTsnv(isLcdDisabled);
    }

    /**
     * Set LCD to TSNV.
     *
     * @param isLcdDisabled (HAL: 1:disabled 0:enabled)
     */
    private void setLcdDisabledToTsnv(boolean isLcdDisabled) {
        TsnvManager manager = new TsnvManager();
        manager.setLcdDisabledTsnv(isLcdDisabled, new ResultCallbackImpl());
    }

    private static class ResultCallbackImpl implements TsnvManager.ResultCallback {
        @Override
        public void onResult(int result) {
            EventProviderApp.LOG.i("handleIntent: setLcdDisabledTsnv result = " + result);
        }
    }

    ///////////////////////////////////////////Public API//////////////////////////////////////////////

    /**
     * Is ConfigToolService connected.
     *
     * @return true is yes, false is no
     */
    public boolean isConfigServiceConnected() {
        return mIsConfigServiceConnected;
    }

    /**
     * Set LCD disabled.
     *
     * @param enabled true is enabled, false is disabled.
     */
    public boolean setLcdEnabled(boolean enabled) {
        try {
            EventProviderApp.LOG.i("setLcdEnabled: enabled = " + enabled);
            JsonObject js = new JsonObject();
            //True is enabled, false is disabled (in Config file), so get another result.
            js.addProperty(CONFIG_KEY_LCD, enabled);
            boolean success = false;
            if (mConfigManager != null) {
                success = mConfigManager.saveConfig(js.toString());
            }
            EventProviderApp.LOG.i("setLcdEnabled: success = " + success);
            return success;
        } catch (Exception ex) {
            EventProviderApp.LOG.w("setLcdEnabled: error: " + ex.getMessage());
        }
        return false;
    }

    /**
     * Get FotaTargetInfo by targetName.
     *
     * @param targetName target name
     * @return info
     */
    public FotaTargetInfo getFotaTargetInfoByTargetName(String targetName) {
        String fotaTargetInfoJson = mConfigManager.getConfigValue(CONFIG_KEY_FOTA_TARGET_INFO);
        if (TextUtils.isEmpty(fotaTargetInfoJson)) {
            EventProviderApp.LOG.w("getFotaTargetInfoByTargetName: fotaTargetInfoJson is empty !");
            return null;
        }
        List<FotaTargetInfo> infoList = new Gson().fromJson(fotaTargetInfoJson,
                new TypeToken<List<FotaTargetInfo>>() {
                }.getType());
        if (infoList == null || infoList.isEmpty()) {
            EventProviderApp.LOG.w("getFotaTargetInfoByTargetName: infoList is empty !");
            return null;
        }
        for (FotaTargetInfo info : infoList) {
            if (info.getTargetName().equals(targetName)) {
                return info;
            }
        }
        return null;
    }

    /**
     * Get config version.
     *
     * @return version
     */
    public String getVersion() {
        try {
            String value = mConfigManager.getConfigValue(CONFIG_KEY_VERSION);
            EventProviderApp.LOG.i("getVersion: value = " + value);
            return value;
        } catch (Exception ex) {
            EventProviderApp.LOG.e("getVersion: error: " + ex.getMessage());
            return "";
        }
    }

    /**
     * Check if LCD disabled.
     *
     * @param isDefaultDisabled is default LCD disabled.
     * @return true if disabled, false if enabled.
     */
    public boolean isLcdDisabled(boolean isDefaultDisabled) {
        try {
            String value = mConfigManager.getConfigValue(CONFIG_KEY_LCD);
            EventProviderApp.LOG.i("isLcdOn: value = " + value);
            return value != null && value.equals("false");
        } catch (Exception ex) {
            EventProviderApp.LOG.e("isLcdOn: error: " + ex.getMessage());
            //Default LCD is on.
            return isDefaultDisabled;
        }
    }
}
