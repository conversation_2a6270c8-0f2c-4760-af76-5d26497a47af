package com.thundercomm.eventprovider.manager;

import android.os.RemoteException;

import com.thundercomm.eventprovider.EventProviderApp;

import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import vendor.thundercomm.hardware.tsnv.V1_0.ITsnv;

public class TsnvManager {

    private static final int AWAIT_TIME_S = 5;

    private ITsnv mTsnvService;

    /**
     * Set LCD ON/OFF to TSNV.
     *
     * @param disabled (HAL: 1:disabled 0:enabled)
     */
    public void setLcdDisabledTsnv(boolean disabled, ResultCallback callback) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.submit(() -> {
            try {
                mTsnvService = ITsnv.getService();
                if (mTsnvService == null) {
                    EventProviderApp.LOG.w("Failed to get TSNV service");
                    return;
                }
                EventProviderApp.LOG.i("TSNV service connected");
            } catch (RemoteException ex) {
                EventProviderApp.LOG.w("Failed to get TSNV service", ex);
            }
            try {
                ArrayList<Byte> buffer = new ArrayList<>();
                buffer.add((byte) (disabled ? 1 : 0));
                int result = mTsnvService.setTsnvWrapper(29, buffer, 4);
                EventProviderApp.LOG.i("setTsnvWrapper result = " + result);
                if (callback != null) {
                    callback.onResult(result);
                }
            } catch (Exception ex) {
                EventProviderApp.LOG.w("setTsnvWrapper error: " + ex.getMessage());
                if (callback != null) {
                    callback.onResult(-1);
                }
            } finally {
                mTsnvService = null;
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(AWAIT_TIME_S, TimeUnit.SECONDS)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException ex) {
                    EventProviderApp.LOG.e("Interrupted while waiting for executor service to terminate");
                    executorService.shutdownNow();
                }
            }
        });
    }

    public interface ResultCallback {
        void onResult(int result);
    }
}
