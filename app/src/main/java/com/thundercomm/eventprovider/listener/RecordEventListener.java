/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.listener;

import android.content.Context;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventListenerInfo;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseEventListener;

import vendor.thundercomm.eventbroker.EventEntry;


@EventListenerInfo(isNeed2Monitor = true,
        eventTypes = {EventType.Record.ACC,EventType.Record.COLLISION,EventType.Record.PARKING},
        categoryId = EventCategory.RECORD)
public class RecordEventListener extends BaseEventListener {
    @Override
    public void onEventReceived(EventEntry eventEntry) {
        // Handle the event received for recording
        if (eventEntry == null || eventEntry.header == null
                || eventEntry.header.categoryId != getCategoryId()) {
            return; // Ignore events not matching the category ID
        }
        EventProviderApp.LOG.d("RecordEventListener");
    }
}

