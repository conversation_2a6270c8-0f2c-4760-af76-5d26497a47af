/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.listener;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.PowerManager;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventListenerInfo;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseEventListener;
import com.thundercomm.eventprovider.utils.ShellUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import vendor.thundercomm.eventbroker.EventEntry;

@EventListenerInfo(isNeed2Monitor = true,
        eventTypes = {EventType.Controller.POWER_OFF, EventType.Controller.REBOOT},
        categoryId = EventCategory.CONTROLLER)
public class ControllerEventListener extends BaseEventListener implements Handler.Callback {

    private static final int MSG_WHAT_REBOOT = 20001;
    private static final int MSG_WHAT_SHUTDOWN = 20002;
    private static final int DELAY = 3000;

    private final Handler mHandler = new Handler(Looper.getMainLooper(), this);

    @Override
    public void onEventReceived(EventEntry eventEntry) {
        // Handle the event received for device controller
        if (eventEntry == null || eventEntry.header == null
                || eventEntry.header.categoryId != getCategoryId()) {
            EventProviderApp.LOG.w("ControllerEventListener: onEventReceived error params!");
            return; // Ignore events not matching the category ID
        }
        EventProviderApp.LOG.d("ControllerEventListener: onEventReceived begin");
        int typeId = eventEntry.header.typeId;
        switch (typeId) {
            case EventType.Controller.POWER_OFF:
                if (mHandler.hasMessages(MSG_WHAT_SHUTDOWN)) {
                    return;
                }
                mHandler.sendEmptyMessageDelayed(MSG_WHAT_SHUTDOWN, DELAY);
                break;
            case EventType.Controller.REBOOT:
                if (mHandler.hasMessages(MSG_WHAT_REBOOT)) {
                    return;
                }
                mHandler.sendEmptyMessageDelayed(MSG_WHAT_REBOOT, DELAY);
                break;
            default:
                EventProviderApp.LOG.w("ControllerEventListener: onEventReceived errorType = " + typeId);
                break;
        }
        EventProviderApp.LOG.i("ControllerEventListener: onEventReceived finished !");
    }

    @Override
    public boolean handleMessage(Message msg) {
        switch (msg.what) {
            case MSG_WHAT_REBOOT: {
                EventProviderApp.LOG.i("ControllerEventListener: handleDeviceReboot");
                // sync before reboot
                sync();
                PowerManager powerManager = getContext().getSystemService(PowerManager.class);
                powerManager.reboot("DeviceRequest");
                break;
            }
            case MSG_WHAT_SHUTDOWN: {
                EventProviderApp.LOG.i("ControllerEventListener: handleDevicePowerOff");
                // sync before shutdown
                sync();
                PowerManager powerManager = getContext().getSystemService(PowerManager.class);
                powerManager.shutdown(false, "DeviceRequest", false);
                break;
            }
            default:
                break;
        }
        try {
            mHandler.removeMessages(msg.what);
        } catch (Exception ex) {
            EventProviderApp.LOG.e("ControllerEventListener: removeMessages error: " + ex.getMessage());
        }
        return true;
    }

    /**
     * Sync device.
     */
    private void sync() {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.submit(() -> {
            try {
                ShellUtils.runShellCommand("DevicePower", "sync");
            } finally {
                executorService.shutdown();
            }
        });
    }
}

