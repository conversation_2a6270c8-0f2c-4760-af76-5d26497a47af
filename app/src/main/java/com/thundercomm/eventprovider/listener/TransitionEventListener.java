/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.listener;

import android.content.Intent;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventListenerInfo;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseEventListener;

import vendor.thundercomm.eventbroker.EventEntry;

@EventListenerInfo(categoryId = EventCategory.TRANSITION,
        isNeed2Monitor = true,
        eventTypes = {EventType.Transition.ACC, EventType.Transition.BT, EventType.Transition.WIFI,
                EventType.Transition.CALLING, EventType.Transition.GNSS, EventType.Transition.AGNSS,
                EventType.Transition.OS_UPGRADE, EventType.Transition.APP_UPGRADE,
        })
public class TransitionEventListener extends BaseEventListener {
    @Override
    public void onEventReceived(EventEntry eventEntry) {
        // Handle the event received for recording
        if (eventEntry == null || eventEntry.header == null
                || eventEntry.header.categoryId != getCategoryId()) {
            return; // Ignore events not matching the category ID
        }
        EventProviderApp.LOG.d("TransitionEventListener");
        int typeId = eventEntry.header.typeId;
        switch (typeId) {
            case EventType.Transition.ACC:
                EventProviderApp.LOG.i("TransitionEventListener: onEventReceived ACC");
                handleAccStateEvent(eventEntry);
                break;
            default:
                EventProviderApp.LOG.w("TransitionEventListener: onEventReceived uncaught type=" + typeId);
                break;
        }
    }

    /**
     * Handle the event received for ACC state change.
     *
     * @param eventEntry event entry
     */
    private void handleAccStateEvent(EventEntry eventEntry) {
        EventProviderApp.LOG.i("handleAccStateEvent: eventEntry=" + eventEntry);
        if (eventEntry.body == null
                || eventEntry.body.payloadData == null
                || eventEntry.body.payloadData.length < 1) {
            EventProviderApp.LOG.w("handleAccStateEvent: Invalid acc state data!");
            return;
        }
        int accState = eventEntry.body.payloadData[0];
        EventProviderApp.LOG.i("handleAccStateEvent: accState=" + accState);
        responseAccState(accState == 1);
    }

    /**
     * Response acc state change broadcast.
     *
     * @param accOn true if acc on, false if acc off
     */
    private void responseAccState(boolean accOn) {
        EventProviderApp.LOG.i("responseAccState: accOn=" + accOn);
        Intent responseIntent = new Intent(ActionConstants.ACTION_ACC_STATE_CHANGE);
        responseIntent.putExtra(ActionConstants.EXTRA_KEY_STATUS, accOn ? 1 : 0);
        getContext().sendBroadcast(responseIntent);
    }
}
