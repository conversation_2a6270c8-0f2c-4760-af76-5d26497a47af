/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.listener;

import android.hardware.lights.TcLightsManager;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventListenerInfo;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseEventListener;

import vendor.thundercomm.eventbroker.EventEntry;

@EventListenerInfo(isNeed2Monitor = true,
        eventTypes = {EventType.LedControl.POWER,
                EventType.LedControl.RECORDING,
                EventType.LedControl.RECORDING_LTE_WEAK,
                EventType.LedControl.RECORDING_LTE_OUT,
                EventType.LedControl.PROCESSING,
                EventType.LedControl.PROCESSING_LTE_WEAK,
                EventType.LedControl.PROCESSING_LTE_OUT,
                EventType.LedControl.DEVICE_WARNING,
                EventType.LedControl.SDCARD_ERROR,
                EventType.LedControl.DEVICE_ERROR,
                EventType.LedControl.SLEEP,
        }, // Specify the event types if needed
        categoryId = EventCategory.LED_CONTROL)
// Define the category for LED events, if applicable)
public class LedEventListener extends BaseEventListener {

    @Override
    public void onEventReceived(EventEntry eventEntry) {
        EventProviderApp.LOG.d("LedEventListener: onEventReceived begin");
        // Handle the event received for recording
        if (eventEntry == null || eventEntry.header == null
                || eventEntry.header.categoryId != getCategoryId()) {
            EventProviderApp.LOG.w("LedEventListener: onEventReceived error");
            return; // Ignore events not matching the category ID
        }
        TcLightsManager manager = (TcLightsManager) getContext().getSystemService("tc_lights");
        if (manager == null) {
            EventProviderApp.LOG.w("LedEventListener: onEventReceived error: TcLightsManager is null !");
            return;
        }
        try {
            int value = eventEntry.body.payloadData[0];
            EventProviderApp.LOG.w("LedEventListener: onEventReceived value = " + value);
            manager.handleLedControlEvent(eventEntry.header.typeId, value);
        } catch (Exception ex) {
            EventProviderApp.LOG.e("LedEventListener: onEventReceived error: " + ex.getMessage());
        }
        EventProviderApp.LOG.i("LedEventListener: onEventReceived finished !");
    }
}
