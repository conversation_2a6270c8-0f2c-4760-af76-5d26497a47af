/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider.listener;

import static android.provider.Settings.Global.AIRPLANE_MODE_ON;
import static com.thundercomm.eventprovider.core.PayloadType.JSON;
import static com.thundercomm.eventprovider.utils.StatesUtils.TEMP_STATE_OVERHEAD;
import static com.thundercomm.eventprovider.utils.StatesUtils.TEMP_STATE_RESTORE_NORMAL;

import android.bluetooth.BluetoothAdapter;
import android.hardware.lights.TcLightsManager;
import android.provider.Settings;
import android.text.TextUtils;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventListenerInfo;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseEventListener;
import com.thundercomm.eventprovider.manager.SenderManager;
import com.thundercomm.eventprovider.send.ErrorInfoSender;
import com.thundercomm.eventprovider.utils.EventBrokerManager;
import com.thundercomm.eventprovider.utils.StatesUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;

import android.os.Environment;
import android.content.pm.PackageManager;
import androidx.core.content.ContextCompat;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.content.Context;
import java.util.List;

import vendor.thundercomm.eventbroker.EventEntry;


@EventListenerInfo(isNeed2Monitor = true,
        eventTypes = {EventType.HardwareRuntimeError.HwId.MCU,
                EventType.HardwareRuntimeError.HwId.LED,
                EventType.HardwareRuntimeError.HwId.G_SENSOR,
                EventType.HardwareRuntimeError.HwId.GYRO_SENSOR,
                EventType.HardwareRuntimeError.HwId.FRONT_CAMERA,
                EventType.HardwareRuntimeError.HwId.INCABIN_CAMERA,
                EventType.HardwareRuntimeError.HwId.REAR_CAMERA,
                EventType.HardwareRuntimeError.HwId.OPTION_CAMERA,
                EventType.HardwareRuntimeError.HwId.SD_CARD,
                EventType.HardwareRuntimeError.HwId.LTE_MODULE,
                EventType.HardwareRuntimeError.HwId.GNSS,
                EventType.HardwareRuntimeError.HwId.BT,
                EventType.HardwareRuntimeError.HwId.WIFI,
                EventType.HardwareRuntimeError.HwId.IF_BOX_UNIT,
                EventType.HardwareRuntimeError.HwId.TEMPERATURE,
                EventType.HardwareRuntimeError.HwId.VOLTAGE}, // Specify the event types if needed
        categoryId = EventCategory.RUNTIME_ERROR) // Define the category for runtime errors
public class RuntimeErrorListener extends BaseEventListener {

    // Constants
    private static final int INVALID_ERROR_CODE = -1;
    private static final int SPECIAL_HANDLING_REQUIRED = -2;
    private static final int MIN_PAYLOAD_LENGTH = 1;
    private static final int AIRPLANE_MODE_DISABLED = 0;
    private static final int AIRPLANE_MODE_ENABLED = 1;
    private static final int WIFI_DISABLED = 0;
    private static final int WIFI_ENABLED = 1;
    private static final int SD_CARD_TEST_BUFFER_SIZE = 4;
    private static final int LED_VALUE_ON = 1;
    private static final int LED_VALUE_OFF = 0;

    @Override
    public void onEventReceived(EventEntry eventEntry) {
        // Handle the event received for recording
        if (eventEntry == null || eventEntry.header == null
                || eventEntry.header.categoryId != getCategoryId()) {
            return; // Ignore events not matching the category ID
        }

        int hwErrorCode = getHwErrorCode(eventEntry);
        EventProviderApp.LOG.d("RuntimeErrorListener");

        // Send error broadcast for all hardware errors using SenderManager
        switch (hwErrorCode) {
            // MCU Errors
            case EventType.HardwareRuntimeError.HwErrorCode.MCU_NO_RESPONSE:
                EventProviderApp.LOG.w("RuntimeErrorListener: MCU no response detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // LED Errors
            case EventType.HardwareRuntimeError.HwErrorCode.LED_CONTROL_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: LED control unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // IMU Errors
            case EventType.HardwareRuntimeError.HwErrorCode.G_SENSOR_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: G-Sensor unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.GYRO_SENSOR_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: Gyro sensor unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // Front Camera Errors
            case EventType.HardwareRuntimeError.HwErrorCode.FRONT_CAMERA_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: Front camera unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.FRONT_CAMERA_NO_SIGNAL:
                EventProviderApp.LOG.w("RuntimeErrorListener: Front camera no signal detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // Incabin Camera Errors
            case EventType.HardwareRuntimeError.HwErrorCode.INCABIN_CAMERA_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: Incabin camera unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.INCABIN_CAMERA_NO_SIGNAL:
                EventProviderApp.LOG.w("RuntimeErrorListener: Incabin camera no signal detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // Rear Camera Errors
            case EventType.HardwareRuntimeError.HwErrorCode.REAR_CAMERA_NOT_DETECTED:
                EventProviderApp.LOG.w("RuntimeErrorListener: Rear camera not detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.REAR_CAMERA_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: Rear camera unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.REAR_CAMERA_NO_SIGNAL:
                EventProviderApp.LOG.w("RuntimeErrorListener: Rear camera no signal detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // Option Camera Errors
            case EventType.HardwareRuntimeError.HwErrorCode.OPTION_CAMERA_NOT_DETECTED:
                EventProviderApp.LOG.w("RuntimeErrorListener: Option camera not detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.OPTION_CAMERA_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: Option camera unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.OPTION_CAMERA_NO_SIGNAL:
                EventProviderApp.LOG.w("RuntimeErrorListener: Option camera no signal detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // SD Card Errors
            case EventType.HardwareRuntimeError.HwErrorCode.NO_SD_CARD:
                EventProviderApp.LOG.w("RuntimeErrorListener: No SD card detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.SD_CARD_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: SD card unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.READ_WRITE_FAIL:
                EventProviderApp.LOG.w("RuntimeErrorListener: Storage read/write error detected!");
                // SD card health detection
                if (!testSdCard()) {
                    handleLedErrorDisplay(hwErrorCode, true);
                }
                break;

            // LTE Module Errors
            case EventType.HardwareRuntimeError.HwErrorCode.NO_SIM_CARD:
                EventProviderApp.LOG.w("RuntimeErrorListener: No SIM card detected!");
                // TODO: Need context-aware handling for LTE errors (ACC state dependent)
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.LTE_MODULE_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: LTE module unavailable detected!");
                // TODO: Need context-aware handling for LTE errors (ACC state dependent)
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.LTE_NETWORK_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: LTE network unavailable detected!");
                // TODO: Need context-aware handling for LTE errors (ACC state dependent)
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // GNSS Errors
            case EventType.HardwareRuntimeError.HwErrorCode.GNSS_FAIL_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: GNSS fail unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;
            case EventType.HardwareRuntimeError.HwErrorCode.DR_ALGORITHM_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: DR algorithm unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // BT Errors
            case EventType.HardwareRuntimeError.HwErrorCode.BT_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: BT unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // WiFi Errors
            case EventType.HardwareRuntimeError.HwErrorCode.WIFI_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: WiFi unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // I/F Box Unit Errors
            case EventType.HardwareRuntimeError.HwErrorCode.IF_BOX_UNIT_UNAVAILABLE:
                EventProviderApp.LOG.w("RuntimeErrorListener: I/F Box Unit unavailable detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            // Temperature Errors
            case EventType.HardwareRuntimeError.HwErrorCode.CPU_HIGH_TEMPERATURE:
                EventProviderApp.LOG.w("RuntimeErrorListener: Temperature overheat detected!");
                handleTemperatureError(eventEntry);
                break;

            // Voltage Errors
            case EventType.HardwareRuntimeError.HwErrorCode.BATTERY_LOW_POWER:
                EventProviderApp.LOG.w("RuntimeErrorListener: Voltage error detected!");
                handleLedErrorDisplay(hwErrorCode, true);
                break;

            default:
                EventProviderApp.LOG.w("RuntimeErrorListener: Unknown runtime error type: " + hwErrorCode);
        }
    }

    private int getHwErrorCode(EventEntry eventEntry) {
        // Handle the temperature error event
        if (eventEntry.body == null || eventEntry.body.payloadData == null
                || eventEntry.body.payloadData.length < MIN_PAYLOAD_LENGTH) {
            EventProviderApp.LOG.w("RuntimeErrorListener: Invalid temperature overheat data!");
            return INVALID_ERROR_CODE;
        }
        if (eventEntry.body.payloadType == JSON.getType()) {
            String data = new String(eventEntry.body.payloadData, StandardCharsets.UTF_8);
            if (TextUtils.isEmpty(data)) {
                EventProviderApp.LOG.w("RuntimeErrorListener: Temperature overheat data is empty!");
                return INVALID_ERROR_CODE;
            }
            try {
                EventProviderApp.LOG.w("RuntimeErrorListener: Temperature overheat data: " + data);
                JSONObject jsonObject = new JSONObject(data);
                int hwErrorCode = jsonObject.getInt("hwErrorCode");     // 155
                return hwErrorCode;
            } catch (JSONException ex) {
                EventProviderApp.LOG.w("RuntimeErrorListener: "
                        + "Failed to parse temperature error JSON data: " + ex.getMessage());
            }
        }
        return INVALID_ERROR_CODE;
    }

    private void handleTemperatureError(EventEntry eventEntry) {
        // Handle the temperature error event
        if (eventEntry.body == null || eventEntry.body.payloadData == null || eventEntry.body.payloadData.length < 1) {
            EventProviderApp.LOG.w("RuntimeErrorListener: Invalid temperature overheat data!");
            return;
        }
        if (eventEntry.body.payloadType == JSON.getType()) {
            String data = new String(eventEntry.body.payloadData, StandardCharsets.UTF_8);
            if (TextUtils.isEmpty(data)) {
                EventProviderApp.LOG.w("RuntimeErrorListener: Temperature overheat data is empty!");
                return;
            }
            try {
                EventProviderApp.LOG.w("RuntimeErrorListener: Temperature overheat data: " + data);
                JSONObject jsonObject = new JSONObject(data);

                // Get error code information
                int errorCode = jsonObject.getInt("errorCode");         // 5
                int hwErrorCode = jsonObject.getInt("hwErrorCode");     // 155

                // Get data object
                JSONObject dataObject = jsonObject.getJSONObject("data");
                int state = dataObject.getInt("state");
                int type = dataObject.getInt("type");
                String tempStr = dataObject.getString("temp");

                EventProviderApp.LOG.d("RuntimeErrorListener: Error Code: " + errorCode
                                     + ", HW Error Code: " + hwErrorCode
                                     + ", State: " + state
                                     + ", Type: " + type
                                     + ", Temp: " + tempStr);

                if (state == TEMP_STATE_OVERHEAD) { //   LTE airplane mode, BLE off, GPS off, Wi-Fi off, WLAN off etc.
                    EventProviderApp.LOG.w("RuntimeErrorListener: Temperature is too high! Taking action...");
                    takeOffForHighTemperature();
                    // High temperature - turn on warning LED
                    handleLedErrorDisplay(hwErrorCode, true);
                } else if (state == TEMP_STATE_RESTORE_NORMAL) {
                    EventProviderApp.LOG.d("RuntimeErrorListener: Temperature has returned to normal.");
                    restoreForNormalTemperature();
                    // Temperature restored to normal - turn off warning LED
                    handleLedErrorDisplay(hwErrorCode, false);
                }

            } catch (JSONException ex) {
                EventProviderApp.LOG.w("RuntimeErrorListener: "
                        + "Failed to parse temperature error JSON data: " + ex.getMessage());
            }
        }
    }

    /**
     * LTE airplane mode, BLE off, GPS off, Wi-Fi off, WLAN off etc.
     */
    private void takeOffForHighTemperature() {
        boolean airplaneModeOn = Settings.Global.getInt(getContext().getContentResolver(),
                AIRPLANE_MODE_ON, AIRPLANE_MODE_DISABLED) == AIRPLANE_MODE_ENABLED;
        if (!airplaneModeOn) {
            Settings.Global.putInt(getContext().getContentResolver(), AIRPLANE_MODE_ON, AIRPLANE_MODE_ENABLED);
            StatesUtils.getInstance().setLastLteEnabled(false);
        } else {
            StatesUtils.getInstance().setLastLteEnabled(true);
        }
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        if (adapter.isEnabled()) {
            boolean bluetoothDisabled = adapter.disable();
            if (bluetoothDisabled) {
                StatesUtils.getInstance().setLastBleEnabled(true);
            }
            EventProviderApp.LOG.i("RuntimeErrorListener: Disabling Bluetooth"
                    + " due to high temperature, bluetoothDisabled=" + bluetoothDisabled);
        } else {
            StatesUtils.getInstance().setLastBleEnabled(false);
            EventProviderApp.LOG.i("RuntimeErrorListener: Bluetooth is already disabled.");
        }
        boolean gpsEnabled = Settings.Secure.getInt(getContext().getContentResolver(),
                Settings.Secure.LOCATION_MODE, Settings.Secure.LOCATION_MODE_OFF) != Settings.Secure.LOCATION_MODE_OFF;
        if (gpsEnabled) {
            Settings.Secure.putInt(getContext().getContentResolver(),
                    Settings.Secure.LOCATION_MODE, Settings.Secure.LOCATION_MODE_OFF);
            EventProviderApp.LOG.i("RuntimeErrorListener: Disabling GPS due to high temperature.");
            StatesUtils.getInstance().setLastGpsEnabled(true);
        } else {
            EventProviderApp.LOG.i("RuntimeErrorListener: GPS is already disabled.");
            StatesUtils.getInstance().setLastGpsEnabled(false);
        }
        boolean wifiEnabled = Settings.Global.getInt(getContext().getContentResolver(),
                Settings.Global.WIFI_ON, WIFI_DISABLED) == WIFI_ENABLED;
        if (wifiEnabled) {
            Settings.Global.putInt(getContext().getContentResolver(), Settings.Global.WIFI_ON, WIFI_DISABLED);
            EventProviderApp.LOG.i("RuntimeErrorListener: Disabling Wi-Fi due to high temperature.");
            StatesUtils.getInstance().setLastWifiEnabled(true);
        } else {
            EventProviderApp.LOG.i("RuntimeErrorListener: Wi-Fi is already disabled.");
            StatesUtils.getInstance().setLastWifiEnabled(false);
        }
    }

    private void restoreForNormalTemperature() {
        if (StatesUtils.getInstance().isLastLteEnabled()) {
            Settings.Global.putInt(getContext().getContentResolver(), AIRPLANE_MODE_ON, AIRPLANE_MODE_ENABLED);
            EventProviderApp.LOG.i("RuntimeErrorListener: Restoring LTE state to normal.");
        } else {
            Settings.Global.putInt(getContext().getContentResolver(), AIRPLANE_MODE_ON, AIRPLANE_MODE_DISABLED);
            EventProviderApp.LOG.i("RuntimeErrorListener: Restoring LTE state to normal.");
        }
        if (StatesUtils.getInstance().isLastBleEnabled()) {
            BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
            boolean bluetoothEnabled = adapter.enable();
            EventProviderApp.LOG.i("RuntimeErrorListener:"
                    + " Restoring Bluetooth state to normal, bluetoothEnabled=" + bluetoothEnabled);
        }
        if (StatesUtils.getInstance().isLastGpsEnabled()) {
            Settings.Secure.putInt(getContext().getContentResolver(),
                    Settings.Secure.LOCATION_MODE, Settings.Secure.LOCATION_MODE_HIGH_ACCURACY);
            EventProviderApp.LOG.i("RuntimeErrorListener: Restoring GPS state to normal.");
        }
        if (StatesUtils.getInstance().isLastWifiEnabled()) {
            Settings.Global.putInt(getContext().getContentResolver(), Settings.Global.WIFI_ON, WIFI_ENABLED);
            EventProviderApp.LOG.i("RuntimeErrorListener: Restoring Wi-Fi state to normal.");
        }
        // Add any other restoration logic needed for normal temperature state
        EventProviderApp.LOG.d("RuntimeErrorListener: Temperature has returned to normal, all states restored.");
    }

    /**
     * Removable SD card health detection using StorageManager API.
     * @return true if removable SD card is healthy, false otherwise
     */
    private boolean testSdCard() {
        try {
            StorageManager storageManager = (StorageManager)
                getContext().getSystemService(Context.STORAGE_SERVICE);

            if (storageManager == null) {
                EventProviderApp.LOG.w("RuntimeErrorListener: StorageManager not available");
                return false;
            }

            // Get all storage volumes
            List<StorageVolume> volumes = storageManager.getStorageVolumes();
            StorageVolume removableVolume = null;

            // Find removable storage volume
            for (StorageVolume volume : volumes) {
                if (volume.isRemovable() && !volume.isPrimary()) {
                    removableVolume = volume;
                    EventProviderApp.LOG.d("RuntimeErrorListener: Found removable SD card: "
                    + volume.getDescription(getContext()));
                    break;
                }
            }

            if (removableVolume == null) {
                EventProviderApp.LOG.w("RuntimeErrorListener: No removable SD card found");
                return false;
            }

            // Get SD card path first
            String sdCardPath = removableVolume.getPath();
            if (sdCardPath == null) {
                EventProviderApp.LOG.w("RuntimeErrorListener: SD card path is null");
                return false;
            }

            // Check mount state using path
            String state = storageManager.getVolumeState(sdCardPath);
            if (!Environment.MEDIA_MOUNTED.equals(state)) {
                EventProviderApp.LOG.w("RuntimeErrorListener: SD card not mounted, state: " + state);
                return false;
            }

            return performReadWriteTest(new File(sdCardPath));

        } catch (Exception es) {
            EventProviderApp.LOG.e("RuntimeErrorListener: SD card test failed", es);
            return false;
        }
    }

    /**
     * Perform read/write test on the specified SD card directory.
     * @param sdCardDir The SD card directory to test
     * @return true if read/write test passes, false otherwise
     */
    private boolean performReadWriteTest(File sdCardDir) {
        try {
            // Check if directory is writable
            if (!sdCardDir.canWrite()) {
                EventProviderApp.LOG.w("RuntimeErrorListener: SD card directory not writable");
                return false;
            }

            // Create test file
            File testFile = new File(sdCardDir, "test_sdcard.tmp");

            // Write test using try-with-resources
            try (FileWriter writer = new FileWriter(testFile)) {
                writer.write("test");
            }

            // Read test using try-with-resources
            int length;
            char[] buffer = new char[SD_CARD_TEST_BUFFER_SIZE];
            try (FileReader reader = new FileReader(testFile)) {
                length = reader.read(buffer);
            }

            // Delete test file
            testFile.delete();

            // Verify result
            boolean result = length == SD_CARD_TEST_BUFFER_SIZE && "test".equals(new String(buffer));
            EventProviderApp.LOG.d("RuntimeErrorListener: SD card test " + (result ? "passed" : "failed"));
            return result;

        } catch (Exception es) {
            EventProviderApp.LOG.e("RuntimeErrorListener: SD card read/write test failed", es);
            return false;
        }
    }

    /**
     * Unified LED error management based on hardware error code
     * Maps hardware runtime errors to corresponding LED control types
     */
    private void handleLedErrorDisplay(int hwErrorCode, boolean isErrorActive) {
        if (isErrorActive) {
            SenderManager.getInstance().sendBroadcast(
                    ActionConstants.ACTION_ERROR_INFO,
                    hwErrorCode,
                    ""
            );
        }
        int ledControlType = mapHwErrorCodeToLedControl(hwErrorCode);
        if (ledControlType == SPECIAL_HANDLING_REQUIRED) {
            // Special handling for LTE errors that need ACC state context
            handleLteErrorDisplay(hwErrorCode, isErrorActive);
        } else if (ledControlType != -1) {
            sendLedControlEvent(ledControlType, isErrorActive ? LED_VALUE_ON : LED_VALUE_OFF);
        }
    }

    /**
     * Handle LTE errors with ACC state context.
     * LTE errors need different LED modes based on current ACC state
     */
    private void handleLteErrorDisplay(int hwErrorCode, boolean isErrorActive) {
        if (!isErrorActive) {
            // If error is resolved, we might need to restore normal LED state
            // TODO: Implement error recovery logic
            EventProviderApp.LOG.i("RuntimeErrorListener: LTE error resolved, hwErrorCode: " + hwErrorCode);
            return;
        }

        // TODO: Get current ACC state to determine correct LED mode
        // For now, assume we're in recording mode (ACC ON)
        // In real implementation, you would check the current system state

        int ledControlType = -1;
        switch (hwErrorCode) {
            case EventType.HardwareRuntimeError.HwErrorCode.NO_SIM_CARD:
            case EventType.HardwareRuntimeError.HwErrorCode.LTE_MODULE_UNAVAILABLE:
                // LTE module unavailable -> LTE_WEAK modes
                // TODO: Check ACC state to choose between RECORDING_LTE_WEAK or PROCESSING_LTE_WEAK
                ledControlType = EventType.LedControl.RECORDING_LTE_WEAK; // Default to recording mode
                break;

            case EventType.HardwareRuntimeError.HwErrorCode.LTE_NETWORK_UNAVAILABLE:
                // LTE network unavailable -> LTE_OUT modes
                // TODO: Check ACC state to choose between RECORDING_LTE_OUT or PROCESSING_LTE_OUT
                ledControlType = EventType.LedControl.RECORDING_LTE_OUT; // Default to recording mode
                break;

            default:
                EventProviderApp.LOG.w("RuntimeErrorListener: No LTE LED handling for hwErrorCode: " + hwErrorCode);
                break;
        }

        if (ledControlType != -1) {
            sendLedControlEvent(ledControlType, LED_VALUE_ON);
            EventProviderApp.LOG.i("RuntimeErrorListener: LTE error LED set - Type: "
                    + ledControlType + " for hwErrorCode: " + hwErrorCode);
        }
    }

    /**
     * Map hardware error code to LED control type based on LED display logic.
     * According to the LED pattern table provided
     */
    private int mapHwErrorCodeToLedControl(int hwErrorCode) {
        switch (hwErrorCode) {
            // SD Card Errors -> SDCARD_ERROR LED (high priority)
            case EventType.HardwareRuntimeError.HwErrorCode.NO_SD_CARD:
            case EventType.HardwareRuntimeError.HwErrorCode.SD_CARD_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.READ_WRITE_FAIL:
                return EventType.LedControl.SDCARD_ERROR;

            // LED Hardware Error -> DEVICE_ERROR LED (high priority)
            case EventType.HardwareRuntimeError.HwErrorCode.LED_CONTROL_UNAVAILABLE:
                return EventType.LedControl.DEVICE_ERROR;

            // Critical Hardware Errors -> DEVICE_ERROR LED (high priority)
            case EventType.HardwareRuntimeError.HwErrorCode.MCU_NO_RESPONSE:
                return EventType.LedControl.DEVICE_ERROR;

            // Hardware Warnings -> DEVICE_WARNING LED (medium priority)
            case EventType.HardwareRuntimeError.HwErrorCode.G_SENSOR_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.GYRO_SENSOR_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.FRONT_CAMERA_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.FRONT_CAMERA_NO_SIGNAL:
            case EventType.HardwareRuntimeError.HwErrorCode.INCABIN_CAMERA_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.INCABIN_CAMERA_NO_SIGNAL:
            case EventType.HardwareRuntimeError.HwErrorCode.REAR_CAMERA_NOT_DETECTED:
            case EventType.HardwareRuntimeError.HwErrorCode.REAR_CAMERA_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.REAR_CAMERA_NO_SIGNAL:
            case EventType.HardwareRuntimeError.HwErrorCode.OPTION_CAMERA_NOT_DETECTED:
            case EventType.HardwareRuntimeError.HwErrorCode.OPTION_CAMERA_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.OPTION_CAMERA_NO_SIGNAL:
            case EventType.HardwareRuntimeError.HwErrorCode.GNSS_FAIL_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.DR_ALGORITHM_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.BT_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.WIFI_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.IF_BOX_UNIT_UNAVAILABLE:
            case EventType.HardwareRuntimeError.HwErrorCode.BATTERY_LOW_POWER:
            case EventType.HardwareRuntimeError.HwErrorCode.CPU_HIGH_TEMPERATURE:
                return EventType.LedControl.DEVICE_WARNING;

            // LTE Errors -> Need context-aware handling (ACC state dependent)
            case EventType.HardwareRuntimeError.HwErrorCode.NO_SIM_CARD:
            case EventType.HardwareRuntimeError.HwErrorCode.LTE_MODULE_UNAVAILABLE:
                // Should trigger LTE_WEAK modes: RECORDING_LTE_WEAK or PROCESSING_LTE_WEAK
                // Need to check current ACC state to determine which one
                return SPECIAL_HANDLING_REQUIRED; // Special handling required

            case EventType.HardwareRuntimeError.HwErrorCode.LTE_NETWORK_UNAVAILABLE:
                // Should trigger LTE_OUT modes: RECORDING_LTE_OUT or PROCESSING_LTE_OUT
                // Need to check current ACC state to determine which one
                return SPECIAL_HANDLING_REQUIRED; // Special handling required

            // Temperature errors are now handled by unified LED system
            // Removed from here since it's now in DEVICE_WARNING section above

            default:
                EventProviderApp.LOG.w("RuntimeErrorListener: No LED mapping for hwErrorCode: " + hwErrorCode);
                return -1; // No LED mapping
        }
    }

    /**
     * Send LED control event to LED controller.
     */
    private void sendLedControlEvent(int ledControlType, int state) {
        TcLightsManager manager = (TcLightsManager) getContext().getSystemService("tc_lights");
        if (manager == null) {
            EventProviderApp.LOG.w("RuntimeErrorListener: TcLightsManager is null!");
            return;
        }
        try {
            EventProviderApp.LOG.i("RuntimeErrorListener: Sending LED control - Type: "
                    + ledControlType + ", State: " + state);
            manager.handleLedControlEvent(ledControlType, state);
        } catch (Exception ex) {
            EventProviderApp.LOG.e("RuntimeErrorListener: Failed to send LED control event: " + ex.getMessage());
        }
    }
}
