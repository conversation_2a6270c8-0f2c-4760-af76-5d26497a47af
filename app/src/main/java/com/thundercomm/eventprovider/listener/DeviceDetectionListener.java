/*
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.eventprovider.listener;

import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventListenerInfo;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.BaseEventListener;
import com.thundercomm.eventprovider.detection.DeviceDetectionManager;
import com.thundercomm.eventprovider.utils.EventBrokerManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;

import vendor.thundercomm.eventbroker.EventEntry;

/**
 * Device detection event listener
 * Listens to device detection response events (category=6, type=2)
 */
@EventListenerInfo(isNeed2Monitor = true,
        eventTypes = {EventType.CategoryTool.RECEIVED_TEST_RESULTS}, // 2
        categoryId = EventCategory.CATEGORY_TOOL_EVENT) // 6
public class DeviceDetectionListener extends BaseEventListener {

    private static final String TAG = "DeviceDetectionListener";

    @Override
    public void onEventReceived(EventEntry eventEntry) {
        EventProviderApp.LOG.d(TAG + ": Received device detection response");
        processDetectionResponse(eventEntry);
    }

    /**
     * Process device detection response
     */
    private void processDetectionResponse(EventEntry eventEntry) {
        if (eventEntry.body == null || eventEntry.body.payloadData == null || eventEntry.body.payloadData.length < 1) {
            EventProviderApp.LOG.w(TAG + ": Invalid detection response data");
            return;
        }

        try {
            String jsonData = new String(eventEntry.body.payloadData, StandardCharsets.UTF_8);
            EventProviderApp.LOG.d(TAG + ": Detection response data: " + jsonData);

            JSONObject jsonObject = new JSONObject(jsonData);

            // Validate field completeness
            if (!validateDetectionResponse(jsonObject)) {
                return;
            }

            int state = jsonObject.getInt("state");
            int hwId = jsonObject.getInt("hwid");

            // Record received response
            DeviceDetectionManager.getInstance().recordResponse(hwId);

            if (state == 0) {
                // Normal state: record and print log
                EventProviderApp.LOG.d(TAG + ": HW ID " + hwId + " is NORMAL");
            } else if (state == 1) {
                // Abnormal state: forward error information (validated, required fields complete)
                EventProviderApp.LOG.w(TAG + ": HW ID " + hwId + " is ABNORMAL - forwarding to RUNTIME_ERROR");
                forwardRuntimeError(jsonObject);
            } else {
                EventProviderApp.LOG.w(TAG + ": Invalid state value: " + state + " for HW ID " + hwId + " (expected 0 or 1)");
            }

        } catch (JSONException e) {
            EventProviderApp.LOG.e(TAG + ": Failed to parse detection response", e);
        }
    }

    /**
     * Validate field completeness of detection response
     */
    private boolean validateDetectionResponse(JSONObject jsonObject) {
        try {
            // Check required fields
            if (!jsonObject.has("state")) {
                EventProviderApp.LOG.w(TAG + ": Missing required field: state");
                return false;
            }

            if (!jsonObject.has("hwid")) {
                EventProviderApp.LOG.w(TAG + ": Missing required field: hwid");
                return false;
            }

            int state = jsonObject.getInt("state");

            // If state=1, check required fields for abnormal state
            if (state == 1) {
                if (!jsonObject.has("errorCode")) {
                    EventProviderApp.LOG.w(TAG + ": Missing required field for abnormal state: errorCode");
                    return false;
                }

                if (!jsonObject.has("hwErrorCode")) {
                    EventProviderApp.LOG.w(TAG + ": Missing required field for abnormal state: hwErrorCode");
                    return false;
                }
            }

            return true;

        } catch (JSONException e) {
            EventProviderApp.LOG.e(TAG + ": Error validating response fields", e);
            return false;
        }
    }

    /**
     * Forward runtime error event
     */
    private void forwardRuntimeError(JSONObject responseJson) {
        try {
            // Extract required fields
            int hwId = responseJson.getInt("hwid");
            int errorCode = responseJson.getInt("errorCode");
            int hwErrorCode = responseJson.getInt("hwErrorCode");

            // Build forwarded JSON
            JSONObject forwardJson = new JSONObject();
            forwardJson.put("hwid", hwId);
            forwardJson.put("errorCode", errorCode);
            forwardJson.put("hwErrorCode", hwErrorCode);

            // Handle optional data field
            if (responseJson.has("data")) {
                JSONObject data = responseJson.getJSONObject("data");
                forwardJson.put("data", data);
                EventProviderApp.LOG.d(TAG + ": Including data field in forwarded event");
            } else {
                EventProviderApp.LOG.d(TAG + ": No data field in response, forwarding without data");
            }

            // Send RUNTIME_ERROR event
            EventBrokerManager.getInstance().publishJsonEvent(
                EventCategory.RUNTIME_ERROR,
                    hwId, // Use hwId as typeId
                "device_detection_error",
                forwardJson.toString()
            );

            EventProviderApp.LOG.w(TAG + ": HW ID " + hwId + " ABNORMAL - forwarded to RUNTIME_ERROR " +
                                 "(errorCode: " + errorCode + ", hwErrorCode: " + hwErrorCode + ")");

        } catch (JSONException | NumberFormatException e) {
            EventProviderApp.LOG.e(TAG + ": Failed to forward runtime error", e);
        }
    }
}
