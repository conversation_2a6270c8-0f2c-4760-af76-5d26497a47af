/*
 *
 *  * Copyright (C) 2025-2026 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 *  * All Rights Reserved.
 *  * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *  *
 *  *  Not a contribution.
 *
 */

package com.thundercomm.eventprovider;

import static com.thundercomm.eventprovider.utils.StatesUtils.TEMP_STATE_OVERHEAD;
import static com.thundercomm.eventprovider.utils.StatesUtils.TEMP_STATE_RESTORE_NORMAL;
import static com.thundercomm.eventprovider.utils.StatesUtils.TEMP_UPPER_LIMIT;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.database.ContentObserver;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.net.Uri;
import android.os.Handler;
import android.os.IBinder;
import android.os.IThermalEventListener;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.os.SystemProperties;
import android.os.Temperature;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.thundercomm.eventprovider.anno.EventCategory;
import com.thundercomm.eventprovider.anno.EventType;
import com.thundercomm.eventprovider.core.IEventListener;
import com.thundercomm.eventprovider.core.PayloadType;
import com.thundercomm.eventprovider.detection.DeviceDetectionManager;
import com.thundercomm.eventprovider.utils.EventBrokerManager;
import com.thundercomm.eventprovider.utils.StatesUtils;
import com.thundercomm.eventprovider.utils.ThermalUtils;
import com.thundercomm.eventprovider.utils.ThreadUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import vendor.thundercomm.eventbroker.EventBody;
import vendor.thundercomm.eventbroker.EventEntry;
import vendor.thundercomm.eventbroker.EventHeader;
import vendor.thundercomm.eventbroker.IEventBroker;
import vendor.thundercomm.eventbroker.IEventObserver;

public class EventMonitorService extends Service {
    private final IntentApiReceiver mReceiver = new IntentApiReceiver();
    private boolean mIsRegisterReceiver = false;
    private boolean mIsCollisionSensorEnabled = false;
    private SensorManager mSensorManager;
    private Sensor mSensor;
    private SensorEventListener mSensorEventListener;
    private IEventBroker mEventBroker;
    private final ThermalUtils mThermalUtils = new ThermalUtils();
    public static final int INDEX_COLLISION_LEVEL = 0;
    public static final int INDEX_COLLISION_X = 1;
    public static final int INDEX_COLLISION_Y = 2;
    public static final int INDEX_COLLISION_Z = 3;
    public static final int LENGTH_COLLISION = 4;
    public static final int RECORDING_START = 0x01;
    public static final int RECORDING_STOP = 0x00;
    /**
     * The type of sensor used to detect collisions.
     * This is a placeholder value; actual implementation may vary based on the device's capabilities.
     */
    public static final int TYPE_COLLISION = 0x10001;

    public static final int MSG_COLLISION = 0x01;
    public static final int MSG_THERMAL_NOTIFY = 0x02;
    public static final int MSG_ENABLE_COLLISION_SENSOR = 0x03;
    public static final int MSG_DISABLE_COLLISION_SENSOR = 0x04;

    // Event lifetime constants
    private static final int EVENT_LIFETIME_ONE_HOUR = 36000; // 1 hour in seconds
    // 6 seconds delay to enable collision sensor
    private static final int DELAY_ENABLE_COLLISION_SENSOR = 6000;
    private static final int PAYLOAD_TYPE_NATIVE = 1;
    private static final int EMPTY_ARRAY_LENGTH = 0;
    private final Handler mHandler = new Handler(Objects.requireNonNull(Looper.myLooper())) {
        @Override
        public void handleMessage(@NonNull android.os.Message msg) {
            switch (msg.what) {
                case MSG_COLLISION:
                    handleCollisionEvent(msg);
                    break;
                case MSG_THERMAL_NOTIFY:
                    handleThermalEvent(msg);
                    break;
                case MSG_ENABLE_COLLISION_SENSOR:
                    enableCollisionSensor();
                    break;
                case MSG_DISABLE_COLLISION_SENSOR:
                    disableCollisionSensor();
                    break;
                default:
                    super.handleMessage(msg);
            }
        }
    };
    private static final String KEY_COLLISION_ENABLED = "collision_enabled";

    private static final int COLLISION_ENABLED = 1; // Collision sensor enabled
    private static final int COLLISION_DISABLED = 0; // Collision sensor disabled
    private static final String SETTINGS_KEY_IMPACT_SENSITIVITY = "settings_key_impact_sensitivity";
    private static final String IMPACT_SENSITIVITY_HIGH = "high_sensitivity";
    private static final String IMPACT_SENSITIVITY_STANDARD = "standard_sensitivity";
    private static final String PROP_COLLISION_SENSITIVITY_KEY
            = "persist.vendor.collision.sensitivity";
    /**
     * 总共有六个参数, 定义如下: 第一个参数是周期, 第二个参数是最小触发阈值,
     * 第三个参数是最大触发阈值, 第四个参数是斜率, 第五和第六个参数预留, 暂不使用
     * 测试的时候, 只需要修改第二和第三个参数即可实现不同的碰撞检测等级, 本地测试,
     * 这两个参数越高, 触发碰撞, 上报数据越难, 参数越低, 触发碰撞, 上报数据越容易.
     */
    private static final String PROP_COLLISION_SENSITIVITY_VALUE_HIGH
            = "0.004,0.3,1.0,-1,5,6";
    private static final String PROP_COLLISION_SENSITIVITY_VALUE_STANDARD
            = "0.004,0.8,1.5,-1,5,6";
    private final Uri mCollisionUri = Settings.System.getUriFor(KEY_COLLISION_ENABLED);
    private final Uri mCollisionLevelUri = Settings.System.getUriFor(SETTINGS_KEY_IMPACT_SENSITIVITY);
    private final ContentObserver mCollisionObserver = new ContentObserver(mHandler) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            super.onChange(selfChange);
            try {
                if (mCollisionLevelUri.equals(uri)) {
                    String impactSensitivity = Settings.System.getString(getContentResolver(),
                            SETTINGS_KEY_IMPACT_SENSITIVITY);
                    setCollisionSensitivity(impactSensitivity);
                } else if (mCollisionUri.equals(uri)) {
                    int collisionEnabled = Settings.System.getInt(getContentResolver(),
                            KEY_COLLISION_ENABLED, COLLISION_DISABLED);
                    if (collisionEnabled == COLLISION_ENABLED) {
                        if (!mIsCollisionSensorEnabled) {
                            EventProviderApp.LOG.d("enable Collision sensor");
                            mHandler.sendEmptyMessage(MSG_ENABLE_COLLISION_SENSOR);
                        }
                    } else if (collisionEnabled == COLLISION_DISABLED) {
                        if (mIsCollisionSensorEnabled) {
                            EventProviderApp.LOG.d("Collision sensor disabled, disabling sensor");
                            mHandler.sendEmptyMessage(MSG_DISABLE_COLLISION_SENSOR);
                        }
                    } else {
                        EventProviderApp.LOG.w("Unknown collision enabled state: " + collisionEnabled);
                    }
                }
            } catch (Exception ex) {
                EventProviderApp.LOG.w("onChange: Exception occurred while checking collision enabled state", ex);
            }
        }
    };


    /**
     * Called when the service is first created.  Do not call this method directly.
     * Instead, either implement {@link #onStartCommand(Intent, int, int)} or
     * {@link #onBind(Intent)} to have the system call this method for you.
     */
    @Override
    public void onCreate() {
        super.onCreate();
        bindEventBroker();
        try {
            IntentFilter intentFilter = EventProviderApp.getIntentFilter();
            if (intentFilter != null) {
                registerReceiver(mReceiver, intentFilter);
                mIsRegisterReceiver = true;
            } else {
                EventProviderApp.LOG.d("Intent filter is null, cannot register receiver");
            }
        } catch (Exception ex) {
            EventProviderApp.LOG.w("onCreate: Exception occurred while registering receiver", ex);
        }
        boolean collisionEnabled = Settings.System.getInt(getContentResolver(),
                KEY_COLLISION_ENABLED, COLLISION_DISABLED) == COLLISION_ENABLED;
        if (collisionEnabled) {
            mHandler.sendEmptyMessageDelayed(MSG_ENABLE_COLLISION_SENSOR, DELAY_ENABLE_COLLISION_SENSOR);
        }
        startCollisionMonitoring();
        mThermalUtils.setThermalEventListener(new ThermalEventListener());
        // only monitor CPU temperature
        mThermalUtils.registerEventListeners(Temperature.TYPE_CPU);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mIsRegisterReceiver) {
            unregisterReceiver(mReceiver);
            mIsRegisterReceiver = false;
        } else {
            EventProviderApp.LOG.d("Receiver was not registered, no need to unregister");
        }
        mHandler.sendEmptyMessage(MSG_DISABLE_COLLISION_SENSOR);
        stopCollisionMonitoring();
        mThermalUtils.unregisterEventListeners();
    }

    /**
     * Extracts the collision event from the message and processes it.
     *
     * @param msg The message containing the sensor event.
     */
    private void handleCollisionEvent(@NonNull Message msg) {
        SensorEvent event = (SensorEvent) msg.obj;
        if (event != null && event.sensor != null) {
            long timestamp = event.timestamp;
            if (event.values == null || event.values.length < LENGTH_COLLISION) {
                EventProviderApp.LOG.w("handleCollisionEvent: Invalid SensorEvent values, ignoring");
                return;
            }
            float collisionLevel = event.values[INDEX_COLLISION_LEVEL]; // Collision level
            float accelX = event.values[INDEX_COLLISION_X]; // accel-x
            float accelY = event.values[INDEX_COLLISION_Y]; // accel-y
            float accelZ = event.values[INDEX_COLLISION_Z]; // accel-z
            EventProviderApp.LOG.d("handleCollisionEvent: Collision detected - timestamp: "
                    + timestamp + ", level: " + collisionLevel + ", accel-x: " + accelX
                    + ", accel-y: " + accelY + ", accel-z: " + accelZ);
            if (mEventBroker != null) {
                try {
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("recording_state", RECORDING_START);
                        jsonObject.put("collisionLevel", String.valueOf(collisionLevel));
                        jsonObject.put("accelX", String.valueOf(accelX));
                        jsonObject.put("accelY", String.valueOf(accelY));
                        jsonObject.put("accelZ", String.valueOf(accelZ));
                    } catch (JSONException ex) {
                        EventProviderApp.LOG.w(
                                "handleCollisionEvent: Failed to create JSON object for timestamp", ex);
                    }
                    String jsonStr = jsonObject.toString();
                    pushJsonEvent(EventCategory.RECORD, EventType.Record.COLLISION,
                            "CollisionEvent", jsonStr);
                    EventProviderApp.LOG.d("handleCollisionEvent: Collision event published successfully");
                } catch (RemoteException ex) {
                    EventProviderApp.LOG.w("handleCollisionEvent: Failed to publish collision event", ex);
                }
            } else {
                EventProviderApp.LOG.w("handleCollisionEvent: IEventBroker is null,"
                        + " cannot publish collision event");
            }
        } else {
            EventProviderApp.LOG.w("handleCollisionEvent: Received null SensorEvent, ignoring");
        }
    }

    private void handleThermalEvent(@NonNull Message msg) {
        Temperature temp = (Temperature) msg.obj;
        if (temp != null) {
            EventProviderApp.LOG.d("handleThermalEvent: Thermal event received - "
                    + temp);
            if (temp.getType() != Temperature.TYPE_CPU) {
                EventProviderApp.LOG.w("handleThermalEvent: Ignoring non-CPU thermal event of type "
                        + temp.getType());
                return;
            }
            boolean stateChanged = isStateChanged(temp);
            if (!stateChanged) {
                EventProviderApp.LOG.d("handleThermalEvent: No state change detected, ignoring thermal event");
                return;
            }
            StatesUtils.getInstance().setLastCpuTemperature(temp.getValue());
            if (mEventBroker != null) {
                try {
                    // Create data object
                    JSONObject dataObject = new JSONObject();
                    dataObject.put("state", temp.getValue() > TEMP_UPPER_LIMIT
                            ? TEMP_STATE_OVERHEAD : TEMP_STATE_RESTORE_NORMAL);  // int
                    dataObject.put("type", temp.getType()); // int
                    dataObject.put("temp", String.valueOf(temp.getValue())); // float-> string

                    // Create main JSON object
                    EventType.HardwareRuntimeError tempError = EventType.HardwareRuntimeError.CPU_HIGH_TEMPERATURE;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("errorCode", tempError.getErrorCode());
                    jsonObject.put("hwErrorCode", tempError.getHwErrorCode());
                    jsonObject.put("data", dataObject);

                    String jsonStr = jsonObject.toString();
                    pushJsonEvent(EventCategory.RUNTIME_ERROR, tempError.getHwId(),
                            "ThermalEvent", jsonStr);
                    EventProviderApp.LOG.d("handleThermalEvent: Thermal event published successfully");
                } catch (RemoteException | JSONException ex) {
                    EventProviderApp.LOG.w("handleThermalEvent: Failed to publish thermal event", ex);
                }
            } else {
                EventProviderApp.LOG.w("handleThermalEvent: IEventBroker is null, cannot publish thermal event");
            }
        } else {
            EventProviderApp.LOG.w("handleThermalEvent: Received null Temperature object, ignoring");
        }
    }

    private void pushJsonEvent(byte category, int type, String name, String content) throws RemoteException {
        if (TextUtils.isEmpty(name) || TextUtils.isEmpty(content)) {
            EventProviderApp.LOG.w("pushJsonEvent: Name or content is empty, cannot publish event");
            return;
        }
        EventEntry eventEntry = new EventEntry();
        eventEntry.header = new EventHeader();
        eventEntry.header.categoryId = category;
        eventEntry.header.typeId = type;
        eventEntry.header.timestamp = System.currentTimeMillis();
        eventEntry.body = new EventBody();
        eventEntry.body.name = name;
        eventEntry.body.payloadType = PayloadType.JSON.getType();
        eventEntry.body.payloadData = content.getBytes(StandardCharsets.UTF_8);
        mEventBroker.publishEvent(eventEntry);
    }

    /**
     * Check if the CPU temperature state has changed based on the last recorded temperature.
     *
     * @param temp The current CPU temperature.
     * @return true if the state has changed, false otherwise.
     */
    private static boolean isStateChanged(Temperature temp) {
        float lastCpuTemperature = StatesUtils.getInstance().getLastCpuTemperature();
        boolean stateChanged = false;
        EventProviderApp.LOG.d("isStateChanged: Last CPU temperature: " + lastCpuTemperature
                + ", Current temperature: " + temp.getValue());
        if (lastCpuTemperature >= TEMP_UPPER_LIMIT && temp.getValue() < StatesUtils.TEMP_RESTORE_NORMAL) {
            // CPU temperature dropped below restore temp, reset state
            stateChanged = true;
        } else if (lastCpuTemperature < TEMP_UPPER_LIMIT && temp.getValue() >= TEMP_UPPER_LIMIT) {
            // CPU temperature exceeded upper limit, update state
            stateChanged = true;
        }
        return stateChanged;
    }

    private void setCollisionSensitivity(String impactSensitivity) {
        if (IMPACT_SENSITIVITY_HIGH.equals(impactSensitivity)) {
            EventProviderApp.LOG.d("Collision sensitivity set to high, re-enabling sensor");
            SystemProperties.set(PROP_COLLISION_SENSITIVITY_KEY, PROP_COLLISION_SENSITIVITY_VALUE_HIGH);
        } else if (IMPACT_SENSITIVITY_STANDARD.equals(impactSensitivity)) {
            EventProviderApp.LOG.d("Collision sensitivity set to standard, disabling sensor");
            SystemProperties.set(PROP_COLLISION_SENSITIVITY_KEY, PROP_COLLISION_SENSITIVITY_VALUE_STANDARD);
        } else {
            EventProviderApp.LOG.w("Unknown collision sensitivity state: " + impactSensitivity);
        }
    }

    /**
     * Enable the collision sensor to monitor device collisions.
     */
    private void enableCollisionSensor() {
        mSensorManager = (SensorManager) getSystemService(Context.SENSOR_SERVICE);
        if (mSensorManager == null) {
            EventProviderApp.LOG.w("enableCollisionSensor: SensorManager is null,"
                    + " cannot register sensor listener");
            return;
        }
        List<Sensor> sensorList = mSensorManager.getSensorList(TYPE_COLLISION);
        for (Sensor sensor : sensorList) {
            EventProviderApp.LOG.d("enableCollisionSensor: Skipping sensor: " + sensor.getName()
                    + ", type: " + sensor.getType() + ", wakeUpSensor: " + sensor.isWakeUpSensor());
            if (sensor.getType() == TYPE_COLLISION && sensor.isWakeUpSensor()) {
                mSensor = sensor;
                EventProviderApp.LOG.d("enableCollisionSensor: Found collision sensor: " + sensor.getName());
                break;
            }
        }
        if (mSensor == null) {
            EventProviderApp.LOG.w("enableCollisionSensor: Accelerometer sensor not available");
            return;
        }
        mSensorEventListener = new SensorEventListenerImpl();
        mSensorManager.registerListener(mSensorEventListener, mSensor, SensorManager.SENSOR_DELAY_NORMAL);
        mIsCollisionSensorEnabled = true;
        String impactSensitivity = Settings.System.getString(getContentResolver(),
                SETTINGS_KEY_IMPACT_SENSITIVITY);
        setCollisionSensitivity(impactSensitivity);
    }

    /**
     * Start monitoring device collisions by registering a content observer
     * to listen for changes in the collision sensor settings.
     * This should be called when the service is created or when collision monitoring is needed.
     */
    private void startCollisionMonitoring() {
        getContentResolver().registerContentObserver(mCollisionUri, true, mCollisionObserver);
        getContentResolver().registerContentObserver(mCollisionLevelUri, true, mCollisionObserver);
    }

    /**
     * Stop monitoring device collisions by unregistering the content observer.
     * This should be called when the service is destroyed or no longer needed.
     */
    private void stopCollisionMonitoring() {
        getContentResolver().unregisterContentObserver(mCollisionObserver);
    }

    /**
     * Disable the collision sensor to stop monitoring device collisions.
     */
    private void disableCollisionSensor() {
        if (mSensorManager != null && mSensorEventListener != null) {
            mSensorManager.unregisterListener(mSensorEventListener);
            mIsCollisionSensorEnabled = false;
            mSensorEventListener = null;
            mSensor = null;
            mSensorManager = null;
        } else {
            EventProviderApp.LOG.d("disableCollisionSensor: "
                    + "SensorManager or SensorEventListener is null, cannot unregister listener");
        }
    }

    /**
     * Return the communication channel to the service.  May return null if
     * clients can not bind to the service.  The returned
     * {@link IBinder} is usually for a complex interface
     * that has been <a href="{@docRoot}guide/components/aidl.html">described using
     * aidl</a>.
     *
     * <p><em>Note that unlike other application components, calls on to the
     * IBinder interface returned here may not happen on the main thread
     * of the process</em>.  More information about the main thread can be found in
     * <a href="{@docRoot}guide/topics/fundamentals/processes-and-threads.html">Processes and
     * Threads</a>.</p>
     *
     * @param intent The Intent that was used to bind to this service,
     *               as given to {@link Context#bindService
     *               Context.bindService}.  Note that any extras that were included with
     *               the Intent at that point will <em>not</em> be seen here.
     * @return Return an IBinder through which clients can call on to the
     * service.
     */
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        throw new RuntimeException("not implemented");
    }


    /**
     * Called when the service is no longer used and is being destroyed.
     * This is where you should clean up any resources such as threads, registered
     * listeners, or receivers that have been created.
     */
    private void bindEventBroker() {
        EventBrokerManager.getInstance().init(this);
        EventBrokerManager.getInstance().addCallback(new EventBrokerManager.EventBrokerCallback() {
            @Override
            public void onConnected(ComponentName name, IBinder broker) {
                mEventBroker = IEventBroker.Stub.asInterface(broker);
                if (mEventBroker == null) {
                    EventProviderApp.LOG.w("onServiceConnected: IEventBroker is null, cannot register event observer");
                    return;
                }
                EventProviderApp.LOG.d("onServiceConnected: IEventBroker is connected");
                try {
                    EventObserver eventObserver = new EventObserver();
                    Map<Byte, IEventListener> eventListeners = EventProviderApp.getEventListeners();
                    if (eventListeners != null && !eventListeners.isEmpty()) {
                        for (Map.Entry<Byte, IEventListener> entry : eventListeners.entrySet()) {
                            byte categoryId = entry.getKey();
                            IEventListener listener = entry.getValue();
                            if (listener != null) {
                                int[] eventTypes = listener.getEventTypes();
                                if (eventTypes == null || eventTypes.length == EMPTY_ARRAY_LENGTH) {
                                    EventProviderApp.LOG.w("onServiceConnected: Listener for type "
                                            + categoryId + " has no event types defined");
                                    continue;
                                }
                                for (int eventTypeId : eventTypes) {
                                    mEventBroker.registerEventObserver(eventObserver, categoryId, eventTypeId);
                                    EventProviderApp.LOG.d(
                                            "onServiceConnected: Registered event observer for type "
                                                    + eventTypeId + " category " + categoryId);
                                }
                            } else {
                                EventProviderApp.LOG.w("onServiceConnected: Listener for type "
                                        + categoryId + " is null");
                            }
                        }
                    } else {
                        EventProviderApp.LOG.w("onServiceConnected: No event listeners available to register");
                    }
                } catch (RemoteException ex) {
                    EventProviderApp.LOG.w("onServiceConnected: Failed to register event observer", ex);
                }
                // Start device detection
                startDeviceDetection(mEventBroker);
            }

            @Override
            public void onDisconnected(ComponentName name) {
                EventProviderApp.LOG.w("onServiceDisconnected: Service connection lost for " + name);
            }

            @Override
            public void onConnectionFailed(String error) {
                EventProviderApp.LOG.w("onConnectionFailed: " + error);
            }

            @Override
            public void onBindingDied(ComponentName name) {
                EventProviderApp.LOG.w("onBindingDied: " + name);
            }
        });
    }

    static class EventObserver extends IEventObserver.Stub {
        @Override
        public void onEventReceived(EventEntry eventEntry) throws RemoteException {
            EventProviderApp.LOG.d("onEventReceived: Event received - " + eventEntry.body.name);
            if (eventEntry.header == null) {
                EventProviderApp.LOG.w("onEventReceived: EventEntry or header is null, ignoring event");
                return; // Ignore events with null entries or headers
            }
            byte categoryId = eventEntry.header.categoryId;
            IEventListener listener = EventProviderApp.getEventListener(categoryId);
            if (listener == null) {
                EventProviderApp.LOG.w("onEventReceived: No listener found for category ID " + categoryId);
                return; // Ignore events without a registered listener
            }
            ThreadUtils.postOnBackgroundThread(() -> {
                try {
                    listener.onEventReceived(eventEntry);
                } catch (Exception ex) {
                    EventProviderApp.LOG.w("onEventReceived: Exception occurred while processing event", ex);
                }
            });

        }

        @Override
        public int getInterfaceVersion() throws RemoteException {
            return VERSION;
        }

        @Override
        public String getInterfaceHash() throws RemoteException {
            return HASH;
        }
    }

    /**
     * A simple implementation of {@link SensorEventListener} that does nothing.
     * This can be used as a base class for more specific sensor event listeners.
     */
    class SensorEventListenerImpl implements SensorEventListener {
        /**
         * Called when there is a new sensor event.  Note that "on changed"
         * is somewhat of a misnomer, as this will also be called if we have a
         * new reading from a sensor with the exact same sensor values (but a
         * newer timestamp).
         *
         * <p>See {@link SensorManager SensorManager}
         * for details on possible sensor types.
         *
         * <p>See also {@link SensorEvent SensorEvent}.
         *
         * <p><b>NOTE:</b> The application doesn't own the
         * {@link SensorEvent event}
         * object passed as a parameter and therefore cannot hold on to it.
         * The object may be part of an internal pool and may be reused by
         * the framework.
         *
         * @param event the {@link SensorEvent SensorEvent}.
         */
        @Override
        public void onSensorChanged(SensorEvent event) {
            if (event == null || event.sensor == null) {
                EventProviderApp.LOG.w("onSensorChanged: SensorEvent or sensor is null, ignoring event");
                return; // Ignore null events
            }
            if (event.sensor.getType() != TYPE_COLLISION) {
                EventProviderApp.LOG.w("onSensorChanged: Ignoring non-collision sensor event of type "
                        + event.sensor.getType());
                return; // Ignore non-collision sensor events
            }
            // Process the collision data here
            Message message = Message.obtain();
            message.what = MSG_COLLISION;
            message.obj = event; // Pass the SensorEvent object
            mHandler.sendMessage(message);


        }

        /**
         * Called when the accuracy of the registered sensor has changed.  Unlike
         * onSensorChanged(), this is only called when this accuracy value changes.
         *
         * <p>See the SENSOR_STATUS_* constants in
         * {@link SensorManager SensorManager} for details.
         *
         * @param sensor   The {@link Sensor Sensor} whose accuracy has changed.
         * @param accuracy The new accuracy of this sensor, one of
         *                 {@code SensorManager.SENSOR_STATUS_*}
         */
        @Override
        public void onAccuracyChanged(Sensor sensor, int accuracy) {

        }
    }

    // Thermal event received from vendor thermal management subsystem
    private final class ThermalEventListener extends IThermalEventListener.Stub {
        @Override
        public void notifyThrottling(Temperature temp) {
            // Process the collision data here
            Message message = Message.obtain();
            message.what = MSG_THERMAL_NOTIFY;
            message.obj = temp;
            mHandler.sendMessage(message);
        }
    }

    /**
     * Start device detection.
     */
    private void startDeviceDetection(IEventBroker eventBroker) {
        if (eventBroker == null) {
            EventProviderApp.LOG.w("startDeviceDetection: IEventBroker is null, cannot start detection");
            return;
        }

        try {
            // Send device detection command
            EventBody eventBody = new EventBody();
            eventBody.name = "checkdevice";
            eventBody.payloadType = PAYLOAD_TYPE_NATIVE;

            try {
                byte[] bytes = "checkdevice".getBytes("utf-8");
                eventBody.payloadSize = bytes.length;
                eventBody.payloadData = bytes;
            } catch (UnsupportedEncodingException ex) {
                EventProviderApp.LOG.e("startDeviceDetection: Failed to encode payload data", ex);
                return;
            }

            EventHeader eventHeader = new EventHeader();
            eventHeader.categoryId = EventCategory.CATEGORY_TOOL_EVENT; // 6
            eventHeader.typeId = EventType.CategoryTool.START_DETECTION; // 1
            eventHeader.timestamp = System.currentTimeMillis();
            eventHeader.lifetime = EVENT_LIFETIME_ONE_HOUR;

            EventEntry eventEntry = new EventEntry();
            eventEntry.body = eventBody;
            eventEntry.header = eventHeader;

            eventBroker.publishEvent(eventEntry);
            EventProviderApp.LOG.d("startDeviceDetection: Device detection command sent successfully");

            // Start detection manager
            DeviceDetectionManager.getInstance().startDetection();

        } catch (RemoteException ex) {
            EventProviderApp.LOG.e("startDeviceDetection: Failed to send device detection command", ex);
        }
    }

}
