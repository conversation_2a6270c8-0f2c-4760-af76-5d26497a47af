plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.thundercomm.eventprovider"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.thundercomm.eventprovider"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}

dependencies {
    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.activity)
    implementation(libs.constraintlayout)
    implementation(files("libs/vendor.thundercomm.eventbroker-V1-java.jar"))
    implementation(files("libs/configtool.api.jar"))
    implementation(files("libs/vendor.thundercomm.hardware.tsnv-V1.0-java.jar"))
    implementation(files("libs/gson.jar"))
    compileOnly(files("libs/framework-header.jar"))
    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
}