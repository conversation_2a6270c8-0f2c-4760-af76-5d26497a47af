# EventProvider 开发指南

## 快速开始

### 1. 环境准备
- Android Studio 4.0+
- Android SDK API Level 28+
- Java 8+

### 2. 项目导入
```bash
git clone <repository-url>
cd EventProvider
./gradlew build
```

### 3. 基本使用

#### 初始化EventBrokerManager
```java
public class EventProviderApp extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化EventBrokerManager
        EventBrokerManager.getInstance().init(this);
    }
}
```

#### 发布事件
```java
// 发布JSON事件
EventBrokerManager.getInstance().publishJsonEvent(
    EventCategory.RUNTIME_ERROR,
    EventType.HardwareRuntimeError.HwErrorCode.CPU_HIGH_TEMPERATURE,
    "thermal_event",
    "{\"errorCode\":5,\"hwErrorCode\":155,\"data\":{\"temp\":\"85.5\"}}"
);

// 便捷方法
EventBrokerManager.getInstance().publishEvent("event_name", "json_content");
```

#### 监听连接状态
```java
EventBrokerManager.getInstance().addCallback(new EventBrokerManager.EventBrokerCallback() {
    @Override
    public void onConnected(ComponentName name, IEventBroker broker) {
        Log.d("App", "EventBroker connected");
    }
    
    @Override
    public void onDisconnected(ComponentName name) {
        Log.w("App", "EventBroker disconnected");
    }
    
    @Override
    public void onConnectionFailed(String error) {
        Log.e("App", "Connection failed: " + error);
    }
});
```

## 新功能开发

### 1. 添加新的事件监听器

#### 步骤1: 创建监听器类
```java
@EventListenerInfo(isNeed2Monitor = true,
        eventTypes = {EventType.YourEvent.TYPE_ID},
        categoryId = EventCategory.YOUR_CATEGORY)
public class YourEventListener extends BaseEventListener {
    
    @Override
    public void onEventReceived(EventEntry eventEntry) {
        // 处理事件
        processEvent(eventEntry);
    }
    
    private void processEvent(EventEntry eventEntry) {
        // 解析事件数据
        String jsonData = new String(eventEntry.body.payloadData, StandardCharsets.UTF_8);
        JSONObject jsonObject = new JSONObject(jsonData);
        
        // 处理业务逻辑
        handleBusinessLogic(jsonObject);
    }
}
```

#### 步骤2: 注册监听器
监听器会通过注解自动注册，无需手动注册。

### 2. 添加新的硬件检测

#### 步骤1: 定义硬件ID
```java
public static class HwId {
    public static final int YOUR_HARDWARE = 17; // 新的硬件ID
}
```

#### 步骤2: 定义错误码
```java
public static class HwErrorCode {
    public static final int YOUR_HARDWARE_ERROR = 94; // 新的错误码
}
```

#### 步骤3: 更新检测管理器
```java
// 在DeviceDetectionManager.initializeExpectedHwIds()中添加
mExpectedHwIds.add(EventType.HardwareRuntimeError.HwId.YOUR_HARDWARE);
```

### 3. 添加新的LED控制类型

#### 步骤1: 定义LED控制类型
```java
public static class LedControl {
    public static final int YOUR_LED_ERROR = NEW_VALUE;
}
```

#### 步骤2: 使用LED控制
```java
EventBrokerManager.getInstance().publishJsonEvent(
    EventCategory.LED_CONTROL,
    EventType.LedControl.YOUR_LED_ERROR,
    "your_led_error",
    jsonData
);
```

## 测试指南

### 1. 单元测试

#### 测试EventBrokerManager
```java
@Test
public void testEventBrokerManager() {
    EventBrokerManager manager = EventBrokerManager.getInstance();
    assertNotNull(manager);
    
    // 测试单例
    EventBrokerManager manager2 = EventBrokerManager.getInstance();
    assertEquals(manager, manager2);
}
```

#### 测试SD卡检测
```java
@Test
public void testSDCardDetection() {
    // 模拟SD卡错误
    RuntimeErrorListener listener = new RuntimeErrorListener();
    
    // 创建测试事件
    EventEntry eventEntry = createTestEvent(
        EventType.HardwareRuntimeError.HwErrorCode.READ_WRITE_FAIL
    );
    
    // 验证处理结果
    listener.onEventReceived(eventEntry);
}
```

### 2. 集成测试

#### 测试设备检测流程
```java
@Test
public void testDeviceDetection() {
    // 启动检测
    DeviceDetectionManager.getInstance().startDetection();
    
    // 模拟设备响应
    simulateDeviceResponse(hwId, state, errorCode);
    
    // 验证结果
    assertTrue(DeviceDetectionManager.getInstance().isDetectionInProgress());
}
```

### 3. 手动测试

#### 测试SD卡错误
1. 在MainActivity中取消注释测试代码
2. 运行应用
3. 观察日志输出
4. 验证LED控制事件是否发送

#### 测试设备检测
1. 启动应用
2. 观察设备检测启动日志
3. 等待1分钟观察超时处理
4. 检查检测报告

## 调试技巧

### 1. 日志过滤
```bash
# 过滤EventProvider相关日志
adb logcat | grep "EventProvider\|DeviceDetection\|EventBrokerManager"

# 过滤特定标签
adb logcat -s "DeviceDetectionManager"
```

### 2. 事件追踪
```java
// 在关键位置添加日志
EventProviderApp.LOG.d(TAG + ": Event published - " + eventName);
EventProviderApp.LOG.d(TAG + ": Response received from HW ID " + hwId);
```

### 3. 状态检查
```java
// 检查连接状态
boolean isConnected = EventBrokerManager.getInstance().isConnected();

// 检查检测状态
boolean isDetecting = DeviceDetectionManager.getInstance().isDetectionInProgress();
```

## 常见问题

### 1. EventBroker连接失败
**问题**: 无法连接到EventBroker服务
**解决方案**:
- 检查服务是否正在运行
- 验证AIDL接口版本
- 检查权限配置

### 2. 事件发布失败
**问题**: 事件无法发布
**解决方案**:
- 检查连接状态
- 验证事件格式
- 查看错误日志

### 3. SD卡检测失败
**问题**: SD卡检测总是失败
**解决方案**:
- 检查存储权限
- 验证SD卡挂载状态
- 检查可用空间

### 4. 设备检测超时
**问题**: 设备检测总是超时
**解决方案**:
- 检查其他模块是否正常运行
- 验证事件分类和类型
- 增加超时时间（如果需要）

## 性能优化建议

### 1. 事件发布优化
- 使用批量发布减少AIDL调用
- 避免在主线程进行耗时操作
- 合理设置事件生命周期

### 2. 内存优化
- 及时移除不需要的回调
- 避免持有Activity引用
- 使用弱引用处理长期持有的对象

### 3. 网络优化
- 合并相似的事件
- 使用压缩减少数据传输
- 实现重试机制

## 代码规范

### 1. 命名规范
- 类名使用PascalCase
- 方法名使用camelCase
- 常量使用UPPER_SNAKE_CASE
- 包名使用小写字母

### 2. 注释规范
```java
/**
 * 设备检测管理器
 * 负责管理设备检测流程、超时处理和结果记录
 */
public class DeviceDetectionManager {
    
    /**
     * 开始设备检测
     * @return true if detection started successfully, false otherwise
     */
    public boolean startDetection() {
        // 实现代码
    }
}
```

### 3. 异常处理
```java
try {
    // 可能抛出异常的代码
    performOperation();
} catch (SpecificException e) {
    // 处理特定异常
    EventProviderApp.LOG.e(TAG + ": Specific error occurred", e);
} catch (Exception e) {
    // 处理通用异常
    EventProviderApp.LOG.e(TAG + ": Unexpected error", e);
}
```

## 发布流程

### 1. 代码审查
- 检查代码质量
- 验证测试覆盖率
- 确认文档更新

### 2. 测试验证
- 运行所有单元测试
- 执行集成测试
- 进行手动测试

### 3. 版本发布
- 更新版本号
- 生成发布包
- 更新文档

## 联系方式

如有问题或建议，请联系开发团队。
