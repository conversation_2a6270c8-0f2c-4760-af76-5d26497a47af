*S - Format: Log Type - Time(microsec) - Message - Optional Info
S - Log Type: B - Since Boot(Power On Reset),  D - Delta,  S - Statistic
S - QC_IMAGE_VERSION_STRING=BOOT.MXF.1.0-01055-LAHAINA-1
S - IMAGE_VARIANT_STRING=SocKodiakLAA
S - OEM_IMAGE_VERSION_STRING=f0e2f5e65d22
S - Boot Interface: UFS
S - Secure Boot: Off
S - Boot Config @ 0x00786070 = 0x000000c1
S - JTAG ID @ 0x00786130 = 0x001980e1
S - OEM ID @ 0x00786138 = 0x00000000
S - Serial Number @ 0x00786134 = 0xf7384cf8
S - OEM Config Row 0 @ 0x007841c0 = 0x0000000000000000
S - OEM Config Row 1 @ 0x007841c8 = 0x0000000000000000
S - Feature Config Row 0 @ 0x00784148 = 0x0000000000000000
S - Feature Config Row 1 @ 0x00784150 = 0x0000000000000000
S - Core 0 Frequency, 1516 MHz
S - PBL Patch Ver: 1
D -      6626 - pbl_apps_init_timestamp
D -     31569 - bootable_media_detect_timestamp
D -       978 - bl_elf_metadata_loading_timestamp
D -       708 - bl_hash_seg_auth_timestamp
D -      6807 - bl_elf_loadable_segment_loading_timestamp
D -      4556 - bl_elf_segs_hash_verify_timestamp
D -     17372 - bl_sec_hash_seg_auth_timestamp
D -       821 - bl_sec_segs_hash_verify_timestamp
D -        29 - pbl_populate_shared_data_and_exit_timestamp
S -     69466 - PBL, End
B -     78934 - SBL1, Start
B -    208650 - SBL1 BUILD @ 15:17:26 on Jul 30 2025
B -    212737 - usb: usb_shared_hs_phy_init: hs phy cfg size , 0xc
B -    221948 - usb: eud_serial_upd , 0xf7384cf8
D -    222284 - sbl1_hw_init
B -    351573 - UFS INQUIRY ID: SAMSUNG KM2L9001CM-B518 0700
B -    353098 - UFS Boot LUN: 1
B -    363468 - UFS GEAR: 3
D -    143624 - boot_media_init
D -        31 - smss_load_cancel
B -    373411 - SMSS -  Image Load, Start
D -      3081 - SMSS -  Image Loaded, Delta - (0 Bytes)
D -       915 - Auth Metadata
D -      5734 - sbl1_xblconfig_init
B -    388295 - XBL Config -  Image Load, Start
D -         0 - shrm_load_cancel
B -    396073 - SHRM -  Image Load, Start
D -       549 - Auth Metadata
D -      1250 - Segments hash check
D -     11925 - SHRM -  Image Loaded, Delta - (39616 Bytes)
D -         0 - boot_default_cdt_init
B -    416904 - Using default CDT
D -      3508 - boot_cdt_init
B -    423523 - CDT -  Image Load, Start
B -    426329 - CDT Version:3,Platform ID:34,Major ID:1,Minor ID:0,Subtype:0
D -     16683 - sbl1_hw_platform_pre_ddr
D -         0 - devcfg init
B -    452589 - PMIC A:2.0 B:1.0 C:2.2
B -    454297 - PM: xVdd reset
B -    456219 - PM: PON by PWR key DEB
B -    685853 - PM: SET_VAL:Skip
B -    685884 - PM: Verifying PON-Trigger specific configurations & current PON-Trigger
B -    694637 - PM: All PON-Trigger specific configs verified. Proceeding to BOOT
B -    704001 - PM: PSI: b0x06_v0x3f
B -    706807 - PM: Device Init # SPMI Transn: 13585
D -    264648 - pm_device_init, Delta
B -    712510 - pm_driver_init, Start
B -    722575 - PM: Driver Init # SPMI Transn: 442
D -      6588 - pm_driver_init, Delta
B -    727242 - PM: CHG Init # SPMI Transn: 14031
B -    730719 - vsense_init, Start
D -         0 - vsense_init, Delta
D -    304756 - sbl1_hw_pre_ddr_init
D -         0 - boot_dload_handle_forced_dload_timeout
D -      2989 - sbl1_load_ddr_training_data
B -    756217 - Pre_DDR_clock_init, Start
D -        91 - Pre_DDR_clock_init, Delta
D -     12932 - sbl1_ddr_set_params
B -    767929 - sbl1_ddr_init, Start
B -    771345 - LP4 DDR detected
B -    785924 - eCDT MRR - Data Starting Address: 0x09066D00

D -     15220 - sbl1_ddr_init, Delta
B -    791597 - DSF version = 262.0.47
B -    794982 - Manufacturer ID = 1, Device Type = 7
B -    798551 - Rank 0 size = 2048 MB, Rank 1 size = 4096 MB
B -    803400 - Row Hamming DDR
B -    808921 - Row Hammer Check : DRAM supports unlimited MAC Value : MR_RH[OP2:0 = 0] & MR_RH[OP3 = 1] for CH0 & CS0
B -    817583 - Row Hammer Check : DRAM supports unlimited MAC Value : MR_RH[OP2:0 = 0] & MR_RH[OP3 = 1] for CH0 & CS1
B -    828288 - Row Hammer Check : DRAM supports unlimited MAC Value : MR_RH[OP2:0 = 0] & MR_RH[OP3 = 1] for CH1 & CS0
B -    838994 - Row Hammer Check : DRAM supports unlimited MAC Value : MR_RH[OP2:0 = 0] & MR_RH[OP3 = 1] for CH1 & CS1
D -     81892 - sbl1_ddr_init
D -        31 - boot_pre_ddi_entry
B -    857477 - do_ddr_training, Start
B -    898652 - DDR: Start of DDR Training Restore
B -    902220 - Current DDR Freq = 1709 MHz
B -    903318 - Max enabled DDR Freq = 2092 MHz
B -    907344 - DDR: End of DDR Training Restore
D -     51057 - do_ddr_training, Delta
D -     58834 - sbl1_do_ddr_training
D -       518 - boot_ddi_entry
B -    923174 - Pimem init cmd, entry
D -      9211 - Pimem init cmd, exit
B -    935618 - External heap init, Start
B -    938668 - External heap init, End
D -     22112 - sbl1_post_ddr_init
D -         0 - sbl1_hw_init_secondary
B -    949404 - DDR -  Image Load, Start
B -    953094 - usb: UFS Serial - d6175429
B -    956724 - usb: fedl, vbus_det_err
D -      7655 - boot_fedl_check
B -    964318 - APDP -  Image Load, Start
D -       885 - Auth Metadata
D -       488 - Segments hash check
D -      9608 - APDP -  Image Loaded, Delta - (7844 Bytes)
D -         0 - boot_dload_dump_security_regions
D -         0 - ramdump_load_cancel
B -    987041 - RamDump -  Image Load, Start
D -      3324 - RamDump -  Image Loaded, Delta - (0 Bytes)
D -         0 - boot_update_abnormal_reset_status
D -         0 - boot_cache_set_memory_barrier
D -        30 - boot_smem_debug_init
D -       549 - boot_smem_init
D -         0 - boot_smem_alloc_for_minidump
D -        61 - boot_smem_store_pon_status
D -        30 - sbl1_hw_platform_smem
D -        92 - boot_ddr_share_data_to_aop
D -       488 - boot_clock_init_rpm
D -         0 - boot_vsense_copy_to_smem
D -        31 - boot_populate_ram_partition_table
D -         0 - boot_populate_ddr_details_shared_table
D -         0 - sbl1_tlmm_init
D -         0 - sbl1_efs_handle_cookies
B -   1049810 - OEM_MISC -  Image Load, Start
D -       549 - Auth Metadata
D -       244 - Segments hash check
D -     10644 - OEM_MISC -  Image Loaded, Delta - (8016 Bytes)
B -   1063779 - QTI_MISC -  Image Load, Start
D -      5703 - QTI_MISC -  Image Loaded, Delta - (0 Bytes)
B -   1079151 - PM: PM Total Mem Allocated: 2089 
D -      5460 - sbl1_pm_aop_pre_init_wrapper
B -   1083726 - AOP -  Image Load, Start
D -       701 - Auth Metadata
D -      1586 - Segments hash check
D -     12962 - AOP -  Image Loaded, Delta - (203320 Bytes)
B -   1099982 - QSEE Dev Config -  Image Load, Start
D -       549 - Auth Metadata
D -       518 - Segments hash check
D -     13054 - QSEE Dev Config -  Image Loaded, Delta - (47104 Bytes)
B -   1122064 - QSEE -  Image Load, Start
D -     17720 - Auth Metadata
D -     22021 - Segments hash check
D -     83509 - QSEE -  Image Loaded, Delta - (3691627 Bytes)
D -        31 - sbl1_hw_play_vibr
B -   1214510 - SEC -  Image Load, Start
D -      3324 - SEC -  Image Loaded, Delta - (64 Bytes)
B -   1221403 - CPUCPFW -  Image Load, Start
D -     17294 - Auth Metadata
D -     17599 - Segments hash check
D -     47915 - CPUCPFW -  Image Loaded, Delta - (171304 Bytes)
B -   1278346 - QHEE -  Image Load, Start
D -     17141 - Auth Metadata
D -      9333 - Segments hash check
D -     31781 - QHEE -  Image Loaded, Delta - (1946881 Bytes)
B -   1313452 - APPSBL -  Image Load, Start
D -       702 - Auth Metadata
D -     11041 - Segments hash check
D -     23729 - APPSBL -  Image Loaded, Delta - (2560000 Bytes)
D -         0 - sbl1_save_appsbl_index
B -   1346392 - SBL1, End
D -   1270935 - SBL1, Delta
S - Flash Throughput, 188021 KB/s  (8837018 Bytes,  47309 us)
S - DDR Frequency, 1555 MHz
0       1.515505 Hypervisor cold boot, version: haven-35898553a prod (Wed Oct 4 10:26:07 2023 UTC)
[RM]Starting Resource Manager, version: 9a25288c (Tue Jan 31 10:13:11 2023 UTC)
[RM]init completed
[RM]UART is disabled


UEFI Start     [ 1576]
 - 0x09FC01000 [ 1579] Sec.efi
ASLR        : ON
DEP         : ON (RTB)
Timer Delta : +6 mS
RAM Entry 0 : Base 0x0080000000  Size 0x003A400000
RAM Entry 1 : Base 0x0100000000  Size 0x0100000000
RAM Entry 2 : Base 0x00C0000000  Size 0x0040000000
Total Available RAM : 6052 MB (0x017A400000)
Total Installed RAM : 6144 MB (0x0180000000)
Init 1 aux cores of 7
Init CPU core 1
  > Scheduler up on Core 1
UEFI Ver    : 6.0.250730.BOOT.MXF.1.0-01055-LAHAINA-1
Build Info  : 64b Jul 30 2025 15:18:35
Boot Device : UFS
PROD Mode   : TRUE
Retail      : TRUE
PM0: 47, PM1: 63, PM2: 49, 
boot from ufs, NumFilters = 1
Partition not found
Module cannot re-initialize DAL module environment
boot from ufs, NumFilters = 1
Partition not found
UFS INQUIRY ID: SAMSUNG KM2L9001CM-B518 0700
UFS Boot LUN: 1
 Protective MBR validation might be needed. 
 Protective MBR validation might be needed. 
HW Wdog Setting from PCD : Disabled
QseeResponse->result = 0xFFFFFFFF
Status = 0x7
QseeResponse->result = 0xFFFFFFFF
Status = 0x7
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
smem_alloc_ex: SMEM alloc_ex failed with err=-3! smem_type=478, remote=3, size=32, flags=0x40000000.boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
UsbPwrCtrlLibConfig_GetHWInfo Hardware Info is not available 
UsbPwrCtrlLib_Init Initialize Hardware Configuration Error[Device Error] 
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
UsbConfigLibOpenProtocols: PMI version (0x0)
UsbConfigInit: after setting role
UsbConfigInit: UsbConfigInit, not start on port: 0, mode 0
UsbConfigInit: after setting role
UsbConfigInit: UsbConfigInit, not start on port: 1, mode 0
UsbConfigPortsQueryConnectionChange: Failed to open Power Control USB protocol Status =  (0xE)
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
ButtonsDxeTest: Keypress SDAM data payload 1 
SoftSKUDxeInitialize: SoftSKU not supported for this chip
tz_armv8_smc_call failed, TzStatus = 0xFFFFFFFE,  SmcId = 0x32000101  
QseeResponse->result = 0xFFFFFFFF
Status = 0x7
QseeAppStartSyscall Failed 1 
MinidumpTALib:LoadImageFromPartition(mdcompress) failed: 0xLoad Error
MinidumpTADxe: Minidump TA loading failed.
Disp init wait [ 2037] 
-----------------------------
Platform Init  [ 2049] BDS
UEFI Ver   : 6.0.250730.BOOT.MXF.1.0-01055-LAHAINA-1
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
Platform           : IDP
Subtype            : 0
Boot Device        : UFS
Chip Name          : QCS6490
Chip Ver           : 1.0
Chip Serial Number : 0xF7384CF8
-----------------------------
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
QcomChargerApp:: QcomChargerApp_Entry Can not locate Charger Protocol = Not Found 
Failed to launch default charger app, status: Device Error
UEFI Total : 527 ms
POST Time      [ 2103] OS Loader
Loader Build Info: Aug  2 2025 23:02:44
VB: Non-secure device: Security State: (0xFFF3F)
VB: RWDeviceState: Succeed using devinfo!
boot from ufs, NumFilters = 1
ABL-Partition tsnv found
BlockSize = 4096, MediaId = 544433781
Read tsnv partition successfully:0x0
Total DDR Size: 0x000000017A400000 
Locate EFI_SOFTSKU_Protocol failed, Status = (0xE)
KeyPress:258, BootReason:0
Fastboot=0, Recovery:0
Booting from slot (_a)
Booting Into Mission Mode
Loading Image Start : 2121 ms
Loading Image Done : 2122 ms
Total Image Read size : 4096 Bytes
Load Image vbmeta_a total time: 1 ms 
Load Image vbmeta_system_a total time: 0 ms 
Load Image boot_a total time: 146 ms 
Load Image dtbo_a total time: 53 ms 
Load Image vendor_boot_a total time: 158 ms 
QseeResponse->result = 0xFFFFFFFF
Status = 0x7
QseeResponse->result = 0xFFFFFFFF
Status = 0x7
QseeResponse->result = 0xFFFFFFFF
Status = 0x7
VB2: Authenticate complete! boot state is: orange
VB2: boot state: orange(1)
Failed to get the graphics output protocol.
Failed to get the graphics output protocol.
Failed to get width or height
Failed to get the graphics output protocol.
Failed to get the graphics output protocol.
Unable to show verified menu on screen: Out of Resources
Device is unlocked, Skipping boot verification
QseeResponse->result = 0xFFFFFFFF
Status = 0x7
ALIGN_TO_128K_1 magic=0x564E5354
Hyp version: 1
Memory Base Address: 0x80000000
Override DTB: GetBlkIOHandles failed loading user_dtbo!
Apply Overlay total time: 330 ms 
TSNV: find node for id=40
oem SN: 
Unable to get Panel Config, Not Found
VB: Non-secure device: Security State: (0xFFF3F)
VB: RWDeviceState: Succeed using devinfo!
Cmdline: console=ttyMSM0,115200n8 androidboot.hardware=qcom androidboot.console=ttyMSM0 androidboot.memcg=1 lpm_levels.sleep_disabled=1 video=vfb:640x400,bpp=32,memsize=3072000 msm_rtb.filter=0x237 service_locator.enable=1 androidboot.usbcontroller=a60000
TSNV: find node for id=21
TSNV, failed to find id:11!
Failed to read bootinfo!
Cmdline: console=ttyMSM0,115200n8 androidboot.hardware=qcom androidboot.console=ttyMSM0 androidboot.memcg=1 lpm_levels.sleep_disabled=1 video=vfb:640x400,bpp=32,memsize=3072000 msm_rtb.filter=0x237 service_locator.enable=1 androidboot.usbcontroller=a600000.dwc3 swiotlb=0 loop.max_part=7 cgroup.memory=nokmem,nosocket pcie_ports=compat loop.max_part=7 iptable_raw.raw_before_defrag=1 ip6table_raw.raw_before_defrag=1 slub_debug=- buildvariant=userdebug  androidboot.verifiedbootstate=orange androidboot.keymaster=1 androidboot.vbmeta.device=PARTUUID=58e3bf41-c87f-2c66-4585-72cb998d12a8 androidboot.vbmeta.avb_version=1.0 androidboot.vbmeta.device_state=unlocked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=7808 androidboot.vbmeta.digest=e539399ebf9f2b4b5f5e8
RAM Partitions
Add Base: 0x0000000080000000 Available Length: 0x000000003A400000 
Add Base: 0x0000000100000000 Available Length: 0x0000000100000000 
Add Base: 0x00000000C0000000 Available Length: 0x0000000040000000 
Unable to get splash buffer info, Not Found
TSNV: find node for id=22
Turn off Uart
ERROR: Could not find uart node ...
PartialGoods Value: 0x0
Update Device Tree total time: 52 ms 

Shutting Down UEFI Boot Services: 8139 ms
Start EBS        [ 8139] 
BDS: LogFs sync skipped, Unsupported
App Log Flush : 114 ms
ScmArmV8ExitBootServicesHandler, Status = 0x0. 
Exit EBS        [ 8266] UEFI End
0       8.279138 Hypervisor UART is disabled!

[    0.000000][    T0] Booting Linux on physical CPU 0x0000000000 [0x412fd050]
[    0.000000][    T0] Linux version 5.4.278-qgki-debug (scm@bf5b14e4045c) (Android (6877366 based on r383902b1) clang version 11.0.2 (https://android.googlesource.com/toolchain/llvm-project b397f81060ce6d701042b782172ed13bee898b79), LLD 11.0.2 (https://android.googlesource.com/toolchain/llvm-project b397f81060ce6d701042b782172ed13bee898b79)) #1 SMP PREEMPT Sat Aug 2 23:10:17 CST 2025
[    0.000000][    T0] memblock_reserve: 0x8f9c8 setup_arch+0x64/0x200
[    0.000000][    T0] random: crng init done
[    0.000000][    T0] Machine model: Qualcomm Technologies, Inc. YUPIKP-IOT IDP
[    0.000000][    T0] earlycon: msm_geni_serial0 at MMIO 0x0000000000994000 (options '')
[    0.000000][    T0] printk: bootconsole [msm_geni_serial0] enabled
[    0.000000][    T0] efi: Getting EFI parameters from FDT:
[    0.000000][    T0] efi: UEFI not found.
[    0.000000][    T0] Memory limit set/overridden to 5028MB
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000ff000000, size 12 MiB
[    0.000000][    T0] OF: reserved mem: initialized node adsp_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000fe000000, size 16 MiB
[    0.000000][    T0] OF: reserved mem: initialized node user_contig_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000fc000000, size 32 MiB
[    0.000000][    T0] OF: reserved mem: initialized node linux,cma, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000f3400000, size 140 MiB
[    0.000000][    T0] OF: reserved mem: initialized node secure_display_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000f1800000, size 28 MiB
[    0.000000][    T0] OF: reserved mem: initialized node audio_cma_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created DMA memory pool at 0x00000000df700000, size 8 MiB
[    0.000000][    T0] OF: reserved mem: initialized node memshare_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000eec00000, size 44 MiB
[    0.000000][    T0] OF: reserved mem: initialized node mem_dump_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000edc00000, size 16 MiB
[    0.000000][    T0] OF: reserved mem: initialized node qseecom_ta_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000ec800000, size 20 MiB
[    0.000000][    T0] OF: reserved mem: initialized node qseecom_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000eb400000, size 20 MiB
[    0.000000][    T0] OF: reserved mem: initialized node cnss_wlan_region, compatible id shared-dma-pool
[    0.000000][    T0] Reserved memory: created CMA memory pool at 0x00000000e5000000, size 100 MiB
[    0.000000][    T0] OF: reserved mem: initialized node non_secure_display_region, compatible id shared-dma-pool
[    0.000000][    T0] psci: probing for conduit method from DT.
[    0.000000][    T0] psci: PSCIv1.1 detected in firmware.
[    0.000000][    T0] psci: Using standard PSCI v0.2 function IDs
[    0.000000][    T0] psci: MIGRATE_INFO_TYPE not supported.
[    0.000000][    T0] psci: SMC Calling Convention v1.0
[    0.000000][    T0] psci: OSI mode supported.
[    0.000000][    T0] psci: Switched to OSI mode.
[    0.000000][    T0] percpu: Embedded 32 pages/cpu s94056 r8192 d28824 u131072
[    0.000000][    T0] Detected VIPT I-cache on CPU0
[    0.000000][    T0] CPU features: detected: GIC system register CPU interface
[    0.000000][    T0] CPU features: kernel page table isolation forced OFF by kpti command line option
[    0.000000][    T0] CPU features: detected: Speculative Store Bypassing Safe (SSBS)
[    0.000000][    T0] Built 1 zonelists, mobility grouping on.  Total pages: 1111397
[    0.000000][    T0] Kernel command line: cgroup_disable=pressure log_buf_len=256K earlycon=msm_geni_serial,0x994000 rcupdate.rcu_expedited=1 rcu_nocbs=0-7 kpti=off console=ttyMSM0,115200n8 androidboot.hardware=qcom androidboot.console=ttyMSM0 androidboot.memcg=1 lpm_levels.sleep_disabled=1 video=vfb:640x400,bpp=32,memsize=3072000 msm_rtb.filter=0x237 service_locator.enable=1 androidboot.usbcontroller=a600000.dwc3 swiotlb=0 loop.max_part=7 cgroup.memory=nokmem,nosocket pcie_ports=compat loop.max_part=7 iptable_raw.raw_before_defrag=1 ip6table_raw.raw_before_defrag=1 slub_debug=- buildvariant=userdebug  androidboot.verifiedbootstate=orange androidboot.keymaster=1 androidboot.vbmeta.device=PARTUUID=58e3bf41-c87f-2c66-4585-72cb998d12a8 androidboot.vbmeta.avb_version=1.0 androidboot.vbmeta.device_state=unlocked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=7808 androidboot.vbmeta.digest=e539399ebf9f2b4b5f5e8ff5268bbe57e97db5eb1c607a0b172321ab72ebd9be androidboot.vbmeta.i
[    0.000000][    T0] cgroup: Disabling pressure control group feature
[    0.000000][    T0] printk: log_buf_len: 262144 bytes
[    0.000000][    T0] printk: early log buf free: 125512(95%)
[    0.000000][    T0] Dentry cache hash table entries: 1048576 (order: 11, 8388608 bytes, linear)
[    0.000000][    T0] Inode-cache hash table entries: 524288 (order: 10, 4194304 bytes, linear)
[    0.000000][    T0] mem auto-init: stack:all(zero), heap alloc:off, heap free:off
[    0.000000][    T0] Memory: 3864872K/4516156K available (23676K kernel code, 3580K rwdata, 15232K rodata, 5824K init, 7662K bss, 213012K reserved, 438272K cma-reserved)
[    0.000000][    T0] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=8, Nodes=1
[    0.000000][    T0] kmemleak: Kernel memory leak detector disabled
[    0.000000][    T0] rcu: Preemptible hierarchical RCU implementation.
[    0.000000][    T0] rcu: 	RCU dyntick-idle grace-period acceleration is enabled.
[    0.000000][    T0] rcu: 	RCU restricting CPUs from NR_CPUS=32 to nr_cpu_ids=8.
[    0.000000][    T0] 	All grace periods are expedited (rcu_expedited).
[    0.000000][    T0] 	Tasks RCU enabled.
[    0.000000][    T0] rcu: RCU calculated value of scheduler-enlistment delay is 25 jiffies.
[    0.000000][    T0] rcu: Adjusting geometry for rcu_fanout_leaf=16, nr_cpu_ids=8
[    0.000000][    T0] NR_IRQS: 64, nr_irqs: 64, preallocated irqs: 0
[    0.000000][    T0] GICv3: 988 SPIs implemented
[    0.000000][    T0] GICv3: 0 Extended SPIs implemented
[    0.000000][    T0] GICv3: Distributor has no Range Selector support
[    0.000000][    T0] GICv3: 16 PPIs implemented
[    0.000000][    T0] GICv3: no VLPI support, no direct LPI support
[    0.000000][    T0] rcu: 	Offload RCU callbacks from CPUs: 0-7.
[    0.000000][    T0] arch_timer: cp15 and mmio timer(s) running at 19.20MHz (virt/virt).
[    0.000000][    T0] clocksource: arch_sys_counter: mask: 0xffffffffffffff max_cycles: 0x46d987e47, max_idle_ns: 440795202767 ns
[    0.000003][    T0] sched_clock: 56 bits at 19MHz, resolution 52ns, wraps every 4398046511078ns
[    0.008936][    T0] clocksource: Switched to clocksource arch_sys_counter
[    0.021011][    T0] Console: colour dummy device 80x25
[    0.026436][    T0] Calibrating delay loop (skipped), value calculated using timer frequency.. 38.40 BogoMIPS (lpj=76800)
[    0.037711][    T0] pid_max: default: 32768 minimum: 301
[    0.043489][    T0] LSM: Security Framework initializing
[    0.049062][    T0] SELinux:  Initializing.
[    0.053726][    T0] Mount-cache hash table entries: 16384 (order: 5, 131072 bytes, linear)
[    0.062341][    T0] Mountpoint-cache hash table entries: 16384 (order: 5, 131072 bytes, linear)
[    0.075585][    T1] ASID allocator initialised with 32768 entries
[    0.082139][    T1] rcu: Hierarchical SRCU implementation.
[    0.096415][    T1] scm_mem_protection_init_do: SCM call failed
[    0.102549][    T1] EFI services will not be available.
[    0.109136][    T1] smp: Bringing up secondary CPUs ...
[    0.116259][    T0] Detected VIPT I-cache on CPU1
[    0.116377][    T0] CPU1: Booted secondary processor 0x0000000100 [0x412fd050]
[    0.118650][    T0] Detected VIPT I-cache on CPU2
[    0.118753][    T0] CPU2: Booted secondary processor 0x0000000200 [0x412fd050]
[    0.120722][    T0] Detected VIPT I-cache on CPU3
[    0.120816][    T0] CPU3: Booted secondary processor 0x0000000300 [0x412fd050]
[    0.124363][    T0] CPU features: detected: Spectre-BHB
[    0.124397][    T0] Detected PIPT I-cache on CPU4
[    0.124421][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_ISAR4_EL1. Boot CPU: 0x00000000011142, CPU4: 0x00000000010142
[    0.124432][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR0_EL1. Boot CPU: 0x00000010000131, CPU4: 0x00000010010131
[    0.124439][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR1_EL1. Boot CPU: 0x00000010011011, CPU4: 0x00000010010000
[    0.124445][    T0] CPU features: Unsupported CPU feature variation detected.
[    0.124615][    T0] CPU4: Booted secondary processor 0x0000000400 [0x411fd411]
[    0.127076][    T0] Detected PIPT I-cache on CPU5
[    0.127101][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_ISAR4_EL1. Boot CPU: 0x00000000011142, CPU5: 0x00000000010142
[    0.127111][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR0_EL1. Boot CPU: 0x00000010000131, CPU5: 0x00000010010131
[    0.127117][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR1_EL1. Boot CPU: 0x00000010011011, CPU5: 0x00000010010000
[    0.127292][    T0] CPU5: Booted secondary processor 0x0000000500 [0x411fd411]
[    0.129745][    T0] Detected PIPT I-cache on CPU6
[    0.129768][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_ISAR4_EL1. Boot CPU: 0x00000000011142, CPU6: 0x00000000010142
[    0.129779][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR0_EL1. Boot CPU: 0x00000010000131, CPU6: 0x00000010010131
[    0.129786][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR1_EL1. Boot CPU: 0x00000010011011, CPU6: 0x00000010010000
[    0.129962][    T0] CPU6: Booted secondary processor 0x0000000600 [0x411fd411]
[    0.133341][    T0] Detected PIPT I-cache on CPU7
[    0.133352][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_ISAR4_EL1. Boot CPU: 0x00000000011142, CPU7: 0x00000000010142
[    0.133356][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR0_EL1. Boot CPU: 0x00000010000131, CPU7: 0x00000010010131
[    0.133359][    T0] CPU features: SANITY CHECK: Unexpected variation in SYS_ID_PFR1_EL1. Boot CPU: 0x00000010011011, CPU7: 0x00000010010000
[    0.133445][    T0] CPU7: Booted secondary processor 0x0000000700 [0x411fd411]
[    0.133674][    T1] smp: Brought up 1 node, 8 CPUs
[    0.392341][    T1] SMP: Total of 8 processors activated.
[    0.398044][    T1] CPU features: detected: Privileged Access Never
[    0.404598][    T1] CPU features: detected: LSE atomic instructions
[    0.411084][    T1] CPU features: detected: User Access Override
[    0.417277][    T1] CPU features: detected: 32-bit EL0 Support
[    0.423297][    T1] CPU features: detected: Common not Private translations
[    0.430493][    T1] CPU features: detected: RAS Extension Support
[    0.436781][    T1] CPU features: detected: Data cache clean to the PoU not required for I/D coherence
[    0.446374][    T1] CPU features: detected: CRC32 instructions
[    0.566335][    T1] CPU: All CPU(s) started at EL1
[    0.571408][   T14] alternatives: patching kernel code
[    0.637241][    T1] allocated 56623104 bytes of page_ext
[    0.658266][    T1] Node 0, zone   Normal: page owner found early allocated 12660 pages
[    0.816626][    T1] Registered cp15_barrier emulation handler
[    0.822590][    T1] Registered setend emulation handler
[    0.828496][    T1] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 7645041785100000 ns
[    0.839296][    T1] futex hash table entries: 2048 (order: 5, 131072 bytes, linear)
[    0.911922][    T1] pinctrl core: initialized pinctrl subsystem
[    0.918651][    T1] 
[    0.920913][    T1] *************************************************************
[    0.928643][    T1] **     NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE    **
[    0.936346][    T1] **                                                         **
[    0.944061][    T1] **  IOMMU DebugFS SUPPORT HAS BEEN ENABLED IN THIS KERNEL  **
[    0.951764][    T1] **                                                         **
[    0.959477][    T1] ** This means that this kernel is built to expose internal **
[    0.967178][    T1] ** IOMMU data structures, which may compromise security on **
[    0.974892][    T1] ** your system.                                            **
[    0.982593][    T1] **                                                         **
[    0.990304][    T1] ** If you see this message and you are not debugging the   **
[    0.998006][    T1] ** kernel, report this immediately to your vendor!         **
[    1.005718][    T1] **                                                         **
[    1.013419][    T1] **     NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE    **
[    1.021131][    T1] *************************************************************
[    1.029295][    T1] NET: Registered protocol family 16
[    1.040220][    T1] DMA: preallocated 256 KiB pool for atomic allocations
[    1.047298][    T1] audit: initializing netlink subsys (disabled)
[    1.053944][   T65] audit: type=2000 audit(1.043:1): state=initialized audit_enabled=0 res=1
[    1.062710][    T1] cpuidle: using governor menu
[    1.067760][    T1] NET: Registered protocol family 42
[    1.073960][    T1] hw-breakpoint: found 6 breakpoint and 4 watchpoint registers.
[    1.082481][    T1] dbg save-restore supported by TZ
[    1.087757][    T1] Serial: AMBA PL011 UART driver
[    1.093446][    T1] DMA: preallocated 256 KiB pool for atomic allocations
[    1.101708][    T1] pstore: Registered ramoops as persistent store backend
[    1.108798][    T1] ramoops: using 0x200000@0xa9000000, ecc: 0
[    1.388130][    T1] rpmh_regulator_probe: ldoe2: could not find RPMh address for resource
[    1.397919][    T1] rpmh_regulator_probe: ldoe3: could not find RPMh address for resource
[    1.407679][    T1] rpmh_regulator_probe: ldoe4: could not find RPMh address for resource
[    1.417459][    T1] rpmh_regulator_probe: ldoe5: could not find RPMh address for resource
[    1.427196][    T1] rpmh_regulator_probe: ldoe6: could not find RPMh address for resource
[    1.442723][    T8] spmi spmi-0: PMIC arbiter version v5 (0x50020000)
[    1.516107][    T8] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    1.545683][    T1] msm_sps_probe: sps:sps is ready
[    1.550688][    T8] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    1.558420][    T8] LCDB: qpnp_lcdb_read: Failed to read from addr=0xfd46 rc=-5
[    1.565941][    T8] LCDB: is_lcdb_enabled: Failed to read ENABLE_CTL1 rc=-5
[    1.573125][    T8] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    1.580832][    T8] LCDB: qpnp_lcdb_read: Failed to read from addr=0xfd41 rc=-5
[    1.588362][    T8] LCDB: qpnp_lcdb_get_bst_voltage: Failed to reat BST voltage rc=-5
[    1.596416][    T8] LCDB: qpnp_lcdb_init_bst: Failed to get BST voltage rc=-5
[    1.603758][    T8] LCDB: qpnp_lcdb_hw_init: Failed to initialize BOOST rc=-5
[    1.611102][    T8] LCDB: qpnp_lcdb_regulator_probe: Failed to initialize LCDB module rc=-5
[    1.619706][    T8] qcom,qpnp-lcdb-regulator: probe of c440000.qcom,spmi:qcom,pm7325b@3:qcom,lcdb@fd00 failed with error -5
[    1.939019][    T1] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 222, hwirq 31
[    1.946749][    T1] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 90
[    1.954009][    T1] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 222 parent hwirq 90
[    2.458946][    T1] hh-watchdog hypervisor:qcom,hh-watchdog: qcom_wdt_dt_to_pdata scandump sizes property not correct
[    2.470229][    T1] hh-watchdog hypervisor:qcom,hh-watchdog: QCOM Apps Watchdog Initialized
[    2.487623][   T78] cryptomgr_test (78) used greatest stack depth: 14208 bytes left
[    2.497537][    T1] cryptd: max_cpu_qlen set to 1000
[    2.513074][   T13] cryptomgr_test (81): highest shadow stack usage: 112 bytes
[    2.513153][   T27] cryptomgr_test (78): highest shadow stack usage: 160 bytes
[    2.520335][    T1] qmp-aop-clk soc:qcom,aopcc: Registered clocks with AOP
[    2.537258][    T1] gcc-yupik 100000.clock-controller: Registered GCC clocks
[    2.547758][    T1] gpu_cc-yupik 3d90000.clock-controller: Registered GPU CC clocks
[    2.558426][    T1] video_cc-yupik aaf0000.clock-controller: Registered VIDEO CC clocks
[    2.567828][    T1] KPI: Bootloader start count = 69259
[    2.573222][    T1] KPI: Bootloader end count = 271443
[    2.578518][    T1] KPI: Bootloader load kernel count = 2861212322
[    2.584884][    T1] KPI: Kernel MPM timestamp = 409174
[    2.590180][    T1] KPI: Kernel MPM Clock frequency = 32768
[    2.597010][    T1] Minidump: Enabled with max number of regions 200
[    2.724215][    T1] Built 1 zonelists, mobility grouping on.  Total pages: 1071562
[    2.846105][    T1] mem-offline: Added memory blocks ranging from mem14 - mem15
[    2.862898][    T1] iommu: Default domain type: Translated 
[    2.871563][    T1] SCSI subsystem initialized
[    2.876298][    T1] usbcore: registered new interface driver usbfs
[    2.882698][    T1] usbcore: registered new interface driver hub
[    2.888915][    T1] usbcore: registered new device driver usb
[    2.895612][    T1] usb_phy_generic soc:usb_nop_phy: soc:usb_nop_phy supply vcc not found, using dummy regulator
[    2.907244][    T1] qcom,qpnp-power-on c440000.qcom,spmi:qcom,pmk8350@0:pon_hlos@1300: IRQ pmic-wd-bark not found
[    2.917889][    T1] input: qpnp_pon as /devices/platform/soc/c440000.qcom,spmi/spmi-0/spmi0-00/c440000.qcom,spmi:qcom,pmk8350@0:pon_hlos@1300/input/input0
[    2.932616][    T1] spmi spmi-0: disallowed SPMI write to sid=1, addr=0x0871
[    2.939891][    T1] qcom,qpnp-power-on c440000.qcom,spmi:qcom,pm7325@1:qcom,power-on@800: Register write failed, addr=0x0871, rc=-1
[    2.952053][    T1] qcom,qpnp-power-on: probe of c440000.qcom,spmi:qcom,pm7325@1:qcom,power-on@800 failed with error -1
[    2.964653][    T1] mc: Linux media interface: v0.10
[    2.969811][    T1] videodev: Linux video capture interface: v2.00
[    2.976266][    T1] thermal_sys: Registered thermal governor 'step_wise'
[    2.976269][    T1] thermal_sys: Registered thermal governor 'user_space'
[    2.983174][    T1] thermal_sys: Registered thermal governor 'power_allocator'
[    3.000708][    T1] EDAC MC: Ver: 3.0.0
[    3.016070][    T1] energy_model: Power domains created prior to em_debug_init
[    3.024072][    T1] energy_model: Power domains created prior to em_debug_init
[    3.031894][    T1] energy_model: Power domains created prior to em_debug_init
[    3.039516][   T62] core_ctl: Creating CPU group 0
[    3.044451][   T62] core_ctl: Init CPU0 state
[    3.048941][   T62] core_ctl: Init CPU1 state
[    3.053489][   T62] core_ctl: Init CPU2 state
[    3.057977][   T62] core_ctl: Init CPU3 state
[    3.062602][   T62] core_ctl: Creating CPU group 4
[    3.067567][   T62] core_ctl: Init CPU4 state
[    3.072053][   T62] core_ctl: Init CPU5 state
[    3.076537][   T62] core_ctl: Init CPU6 state
[    3.081449][   T62] core_ctl: Creating CPU group 7
[    3.083985][    T1] shmbridge is enabled
[    3.086381][   T62] core_ctl: Init CPU7 state
[    3.096114][    T1] IPA framework init
[    3.100493][    T1] dev-cpufreq: No tables parsed from DT.
[    3.106366][    T1] SDAM base=0x7100 size=128 registered successfully
[    3.113056][    T1] SDAM base=0x7400 size=128 registered successfully
[    3.119719][    T1] SDAM base=0x7c00 size=128 registered successfully
[    3.126384][    T1] SDAM base=0x7d00 size=128 registered successfully
[    3.133063][    T1] SDAM base=0x8400 size=128 registered successfully
[    3.139725][    T1] SDAM base=0x8500 size=128 registered successfully
[    3.146415][    T1] SDAM base=0x8600 size=128 registered successfully
[    3.153076][    T1] SDAM base=0x9d00 size=128 registered successfully
[    3.159912][    T1] Advanced Linux Sound Architecture Driver Initialized.
[    3.167564][    T1] IPA clients manager init
[    3.171968][    T1] ipa_usb driver init
[    3.176085][    T1] ipa_usb registered successfully
[    3.181116][    T1] exit: IPA_USB init success!
[    3.185777][    T1] ipa_wdi3 registered successfully
[    3.190879][    T1] ipa_gsb registered successfully
[    3.195904][    T1] ipa_uc_offload registered successfully
[    3.201538][    T1] ipa_mhi registered successfully
[    3.206553][    T1] ipa_wigig registered successfully
[    3.211920][    T1] Bluetooth: Core ver 2.22
[    3.216333][    T1] NET: Registered protocol family 31
[    3.221612][    T1] Bluetooth: HCI device and connection manager initialized
[    3.228866][    T1] Bluetooth: HCI socket layer initialized
[    3.234600][    T1] Bluetooth: L2CAP socket layer initialized
[    3.240513][    T1] Bluetooth: SCO socket layer initialized
[    3.246320][    T1] pcie:pcie_init.
[    3.252694][    T1] platform soc:qcom,ion:qcom,ion-heap@10: assigned reserved memory node secure_display_region
[    3.263179][    T1] platform soc:qcom,ion:qcom,ion-heap@19: assigned reserved memory node qseecom_ta_region
[    3.273294][    T1] platform soc:qcom,ion:qcom,ion-heap@26: assigned reserved memory node user_contig_region
[    3.283480][    T1] platform soc:qcom,ion:qcom,ion-heap@27: assigned reserved memory node qseecom_region
[    3.293323][    T1] platform soc:qcom,ion:qcom,ion-heap@5: assigned reserved memory node audio_cma_region
[    3.303248][    T1] platform soc:qcom,ion:qcom,ion-heap@3: assigned reserved memory node non_secure_display_region
[    3.314231][    T1] ION heap system created
[    3.318828][    T1] ION heap secure_heap created
[    3.339390][    T1] platform soc:qcom,ion:qcom,ion-heap@14: ion_secure_carveout: creating heap@0x81800000, size 0x1e00000
[    3.350705][    T1] ION heap secure_carveout created
[    3.355889][    T1] ION heap secure_display created
[    3.361007][    T1] ION heap qsecom_ta created
[    3.365656][    T1] ION heap user_contig created
[    3.370471][    T1] ION heap qsecom created
[    3.374820][    T1] ION heap audio_ml created
[    3.379344][    T1] ION heap display created
[    3.384959][    T1] clocksource: Switched to clocksource arch_sys_counter
[    3.426582][    T1] VFS: Disk quotas dquot_6.6.0
[    3.431366][    T1] VFS: Dquot-cache hash table entries: 512 (order 0, 4096 bytes)
[    3.442119][    T1] yupik-debugcc soc:debug-clock-controller@0: Registered debug measure clocks
[    3.452728][    T1] NET: Registered protocol family 2
[    3.458190][    T1] IP idents hash table entries: 131072 (order: 8, 1048576 bytes, linear)
[    3.467612][    T1] tcp_listen_portaddr_hash hash table entries: 4096 (order: 5, 163840 bytes, linear)
[    3.477331][    T1] TCP established hash table entries: 65536 (order: 7, 524288 bytes, linear)
[    3.486485][    T1] TCP bind hash table entries: 65536 (order: 9, 2097152 bytes, linear)
[    3.495299][    T1] TCP: Hash tables configured (established 65536 bind 65536)
[    3.502916][    T1] UDP hash table entries: 4096 (order: 6, 393216 bytes, linear)
[    3.510816][    T1] UDP-Lite hash table entries: 4096 (order: 6, 393216 bytes, linear)
[    3.519102][    T1] NET: Registered protocol family 1
[    3.524677][    T1] NET: Registered protocol family 44
[    3.529979][    T1] PCI: CLS 0 bytes, default 64
[    3.534845][    T1] Trying to unpack rootfs image as initramfs...
[    3.661800][    T1] Freeing initrd memory: 10324K
[    3.667685][    T1] hw perfevents: enabled with armv8_pmuv3 PMU driver, 7 counters available
[    3.682895][    T1] Initialise system trusted keyrings
[    3.688223][    T1] workingset: timestamp_bits=46 max_order=21 bucket_order=0
[    3.698291][    T1] fuse: init (API version 7.31)
[    3.707096][    T1] Key type asymmetric registered
[    3.712033][    T1] Asymmetric key parser 'x509' registered
[    3.717779][    T1] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 242)
[    3.726087][    T1] io scheduler mq-deadline registered
[    3.731463][    T1] io scheduler kyber registered
[    3.736349][    T1] io scheduler bfq registered
[    3.751001][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    3.758727][    T1] qcom-spmi-gpio c440000.qcom,spmi:qcom,pmr735a@4:pinctrl@8800: read 0x4 failed
[    3.767907][    T1] qcom-spmi-gpio: probe of c440000.qcom,spmi:qcom,pmr735a@4:pinctrl@8800 failed with error -5
[    3.778317][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    3.786006][    T1] qcom-spmi-gpio c440000.qcom,spmi:qcom,pm7325b@3:pinctrl@8800: read 0x4 failed
[    3.795116][    T1] qcom-spmi-gpio: probe of c440000.qcom,spmi:qcom,pm7325b@3:pinctrl@8800 failed with error -5
[    3.810215][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    3.817905][    T1] WLED: wled_configure: Error in reading WLED_CTRL_REVISION2 rc=-5
[    3.825864][    T1] qcom-spmi-wled c440000.qcom,spmi:qcom,pm7325b@3:qcom,wled@d800: wled configure failed rc:-22
[    3.836307][    T1] qcom-spmi-wled: probe of c440000.qcom,spmi:qcom,pm7325b@3:qcom,wled@d800 failed with error -22
[    3.851325][    T1] memshare soc:qcom,memshare:qcom,client_1: memshare: Continuing with allocation from CMA
[    3.861337][    T1] memshare soc:qcom,memshare:qcom,client_1: for memshare_GPS segments only will be dumped.
[    3.871680][    T1] memshare soc:qcom,memshare:qcom,client_2: memshare: Continuing with allocation from CMA
[    3.881681][    T1] memshare soc:qcom,memshare:qcom,client_2: for memshare_FTM segments only will be dumped.
[    3.892086][    T1] memshare soc:qcom,memshare:qcom,client_3: assigned reserved memory node memshare_region
[    3.902084][    T1] memshare soc:qcom,memshare:qcom,client_3: memshare: Memory allocation from shared DMA pool
[    3.912355][    T1] memshare soc:qcom,memshare:qcom,client_3: for memshare_DIAG segments only will be dumped.
[    3.927086][    T1] msm_hang_detect_probe: Missing cluster-id.
[    3.933109][    T1] msm_hang_detect_probe: Failed to get qcom,chd-percpu-info DT property
[    3.941507][    T1] msm_hang_detect: probe of soc:qcom,chd failed with error -22
[    3.949676][    T1] v0.15, id=498, ver=1.0, raw_id=408, raw_ver=0, hw_plat=34, hw_plat_ver=65536
[    3.949676][    T1]  accessory_chip=0, hw_plat_subtype=0, pmic_model=65583, pmic_die_revision=131072 foundry_id=1 serial_number=4147662072 num_pmics=3 chip_family=0x76 raw_device_family=0x6 raw_device_number=0x18 nproduct_id=0x42f num_clusters=0x1 ncluster_array_offset=0xb0 num_defective_parts=0xf ndefective_parts_array_offset=0xb4 nmodem_supported=0x0
[    3.994334][    T1] msm_mem_dump soc:mem_dump: assigned reserved memory node mem_dump_region
[    4.016718][    T1] MSM Memory Dump base table set up
[    4.021923][    T1] MSM Memory Dump apps data table set up
[    4.028548][    T1] etm save-restore supported by TZ
[    4.033746][    T1] etm save-restore supported by TZ
[    4.038940][    T1] etm save-restore supported by TZ
[    4.044139][    T1] etm save-restore supported by TZ
[    4.049343][    T1] etm save-restore supported by TZ
[    4.054557][    T1] etm save-restore supported by TZ
[    4.059790][    T1] etm save-restore supported by TZ
[    4.065027][    T1] etm save-restore supported by TZ
[    4.079871][    T1] cdsprm: Init successful
[    4.084488][    T1] 88e0000.qcom,msm-eud: ttyEUD0 at MMIO 0x0 (irq = 11, base_baud = 0) is a EUD UART
[    4.098734][    T1] rimps-memlat-mon 18590100.qcom,cpu0-rimps-l3-latmon: l2wb event missing for mon: -22
[    4.108478][    T1] rimps-memlat-mon 18590100.qcom,cpu0-rimps-l3-latmon: l2wb event missing for mon: -22
[    4.120076][    T1] rimps-log 18509c00.qcom,rimps_log: RIMPS logging initialized
[    4.134650][    T1] msm_geni_serial_init: Driver initialized
[    4.142348][    T1] adsprpc: fastrpc_get_dsp_status: cdsp node found with ret:1
[    4.150146][    T1] fastrpc soc:qcom,msm-adsprpc-mem: assigned reserved memory node adsp_region
[    4.159087][    T1] fastrpc soc:qcom,msm-adsprpc-mem: for adsp_rh segments only will be dumped.
[    4.168084][    T1] Info: adsprpc (6995): swapper/0: init_secure_vmid_list: secure VMID = 22
[    4.176747][    T1] Info: adsprpc (6995): swapper/0: init_secure_vmid_list: secure VMID = 37
[    4.185411][    T1] adsprpc: fastrpc_init_privileged_gids: privileged GID: 2908
[    4.192961][    T1] adsprpc: fastrpc_setup_service_locator: service location enabled for avs/audio (audio_pdr_adsprpc)
[    4.203958][    T1] adsprpc: fastrpc_setup_service_locator: service location enabled for tms/servreg (sensors_pdr_adsprpc)
[    4.219848][    T1] adsprpc: fastrpc_device_init: SSR notifier registered for adsp
[    4.227645][    T1] adsprpc: fastrpc_device_init: SSR notifier registered for modem
[    4.235534][    T1] adsprpc: fastrpc_device_init: SSR notifier registered for slpi
[    4.243312][    T1] adsprpc: fastrpc_device_init: SSR notifier registered for cdsp
[    4.272170][    T1] brd: module loaded
[    4.282851][    T1] loop: module loaded
[    4.286953][    T1] zram: Added device: zram0
[    4.291536][    T1] platform soc:qcom,ion:qcom,ion-heap@19: assigned reserved memory node qseecom_ta_region
[    4.301802][    T1] platform soc:qcom,ion:qcom,ion-heap@27: assigned reserved memory node qseecom_region
[    4.311625][    T1] platform soc:qcom,ion:qcom,ion-heap@26: assigned reserved memory node user_contig_region
[    4.321828][    T1] qseecom soc:qseecom@c1800000: assigned reserved memory node qseecom_region
[    4.330721][    T1] QSEECOM: qseecom_init_control: qseecom.qsee_version = 0x1402000
[    4.338622][    T1] QSEECOM: qseecom_retrieve_ce_data: Device does not support PFE
[    4.351827][    T1] spmi spmi-1: pmic_arb_debug_wait_for_done: transaction failed (0x3)
[    4.360066][    T1] spmi spmi-1: SPMI PMIC arbiter debug bus controller added
[    4.369225][    T1] tun: Universal TUN/TAP device driver, 1.6
[    4.375166][    T1] CAN device driver interface
[    4.379848][    T1] PPP generic driver version 2.4.2
[    4.384976][    T1] PPP BSD Compression module registered
[    4.390532][    T1] PPP Deflate Compression module registered
[    4.396463][    T1] PPP MPPE Compression module registered
[    4.402102][    T1] NET: Registered protocol family 24
[    4.407381][    T1] PPTP driver version 0.8.5
[    4.413214][    T1] CLD80211: Initializing
[    4.417899][    T1] usbcore: registered new interface driver r8152
[    4.424270][    T1] usbcore: registered new interface driver lan78xx
[    4.430822][    T1] usbcore: registered new interface driver asix
[    4.437104][    T1] usbcore: registered new interface driver ax_usb_nic
[    4.443924][    T1] usbcore: registered new interface driver cdc_ether
[    4.450648][    T1] usbcore: registered new interface driver qmi_wwan_q
[    4.457465][    T1] usbcore: registered new interface driver qmi_wwan
[    4.468434][    T1] msm_sharedmem: msm_sharedmem_probe: Device created for client 'rmtfs'
[    4.492371][    T1] ehci_hcd: USB 2.0 'Enhanced' Host Controller (EHCI) Driver
[    4.499807][    T1] ehci-pci: EHCI PCI platform driver
[    4.505107][    T1] ehci-platform: EHCI generic platform driver
[    4.512430][    T1] usbcore: registered new interface driver cdc_wdm
[    4.519180][    T1] usbcore: registered new interface driver uas
[    4.525378][    T1] usbcore: registered new interface driver usb-storage
[    4.532305][    T1] usbcore: registered new interface driver cp210x
[    4.538768][    T1] usbserial: USB Serial support registered for cp210x
[    4.545576][    T1] usbcore: registered new interface driver option
[    4.552021][    T1] usbserial: USB Serial support registered for GSM modem (1-port)
[    4.559903][    T1] usbcore: registered new interface driver usb_ehset_test
[    4.567070][    T1] usbcore: registered new interface driver lvs
[    4.574017][    T1] dummy_hcd dummy_hcd.0: USB Host+Gadget Emulator, driver 02 May 2005
[    4.582251][    T1] dummy_hcd dummy_hcd.0: Dummy host controller
[    4.588433][    T1] dummy_hcd dummy_hcd.0: new USB bus registered, assigned bus number 1
[    4.596965][    T1] hub 1-0:1.0: USB hub found
[    4.601569][    T1] hub 1-0:1.0: 1 port detected
[    4.608182][    T1] usbcore: registered new interface driver xpad
[    4.614474][    T1] usbcore: registered new interface driver usbtouchscreen
[    4.621662][    T1] pt_device_access_init: Parade TTSP Device Access Driver (Built TTDL.04.11.977092) rc = 0
[    4.633027][    T1] rtc-pm8xxx c440000.qcom,spmi:qcom,pmk8350@0:rtc@6100: registered as rtc0
[    4.641736][    T1] rtc-pm8xxx c440000.qcom,spmi:qcom,pmk8350@0:rtc@6100: setting system clock to 1970-01-01T00:00:15 UTC (15)
[    4.653936][    T1] i2c /dev entries driver
[    4.658799][    T1] synx: synx device initialization start
[    4.664475][    T1] synx: synx device initialization success
[    4.671068][    T1] usbcore: registered new interface driver uvcvideo
[    4.677684][    T1] USB Video Class driver (1.1.1)
[    4.682627][    T1] gspca_main: v2.14.0 registered
[    4.690450][    T1] qcom,gpio-pwm soc:gpio_pwm_fan: czx pwm:128
[    4.696550][    T1] czx clk_set_rate ret = 0.
[    4.701665][    T1] czx clk_prepare_enable ret=0.
[    4.707174][    T1] czx  set duty cycle ret=0
[    4.720441][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.728138][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4772 err:-5
[    4.736416][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.744106][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4786 err:-5
[    4.752128][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.759819][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4786 err:-5
[    4.768105][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.775796][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4786 err:-5
[    4.783819][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.791508][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4786 err:-5
[    4.799665][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.807347][    T1] spmi spmi-0: failed irqchip transaction on 5a
[    4.813647][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.821329][    T1] spmi spmi-0: failed irqchip transaction on 5a
[    4.827998][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.835691][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[    4.843699][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.851388][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[    4.859521][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.867203][    T1] spmi spmi-0: failed irqchip transaction on 5b
[    4.873503][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.881184][    T1] spmi spmi-0: failed irqchip transaction on 5b
[    4.887841][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.895543][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[    4.903530][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.911227][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[    4.919368][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.927052][    T1] spmi spmi-0: failed irqchip transaction on 5c
[    4.933352][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.941033][    T1] spmi spmi-0: failed irqchip transaction on 5c
[    4.947694][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.955397][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[    4.963368][    T1] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    4.971066][    T1] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[    4.981676][    T1] cpu_voltage_cooling:build_unified_table CPU1:4 CPU2:7
[    4.988666][    T1] cpu_voltage_cooling:build_unified_table freq1:2400000 freq2:2707200
[    4.996884][    T1] cpu_voltage_cooling:build_unified_table freq1:2131200 freq2:2707200
[    5.005111][    T1] cpu_voltage_cooling:build_unified_table freq1:2054400 freq2:2707200
[    5.013329][    T1] cpu_voltage_cooling:build_unified_table freq1:1900800 freq2:2707200
[    5.021554][    T1] cpu_voltage_cooling:build_unified_table freq1:1651200 freq2:1862400
[    5.029770][    T1] cpu_voltage_cooling:build_unified_table freq1:1516800 freq2:1766400
[    5.037993][    T1] cpu_voltage_cooling:build_unified_table freq1:1344000 freq2:1324800
[    5.046210][    T1] cpu_voltage_cooling:build_unified_table freq1:1228800 freq2:1056000
[    5.054434][    T1] cpu_voltage_cooling:build_unified_table freq1:940800 freq2:806400
[    5.062478][    T1] cpu_voltage_cooling:build_unified_table freq1:691200 freq2:806400
[    5.072799][    T1] device-mapper: uevent: version 1.0.3
[    5.078346][    T1] device-mapper: ioctl: 4.41.0-ioctl (2019-09-16) initialised: <EMAIL>
[    5.088199][    T1] EDAC DEVICE0: Giving out device to module soc:kryo-erp controller cache: DEV soc:kryo-erp (INTERRUPT)
[    5.100772][    T1] cpuidle: using governor qcom
[    5.107827][    T1] sdhci: Secure Digital Host Controller Interface driver
[    5.114908][    T1] sdhci: Copyright(c) Pierre Ossman
[    5.120104][    T1] sdhci-pltfm: SDHCI platform and OF driver helper
[    5.129870][    T1] led_qcom_clk_pwm soc:qcom_clk_led_irled: led qcom clk probe entry
[    5.138041][    T1] led_qcom_clk_pwm soc:qcom_clk_led_irled: led qcom clk probe finished
[    5.147449][    T1] qpnp_vibrator_ldo_probe: Vibrator LDO successfully registered: uV = 3000000, overdrive = disabled
[    5.159841][    T1] arm-scmi soc:qcom,scmi: SCMI Protocol v2.0 'Qualcomm:CPUCP' Firmware version 0x10000
[    5.170447][   T67] tz diag version is 2403000
[    5.171924][    T1] hidraw: raw HID events driver (C) Jiri Kosina
[    5.175262][   T67] encrypted qseelog enabled is 0
[    5.181882][    T1] usbcore: registered new interface driver usbhid
[    5.192714][    T1] usbhid: USB HID core driver
[    5.197633][    T1] ashmem: initialized
[    5.202797][    T1] hh_msgq: Registered client for label: 0
[    5.217540][    T1] qcom-llcc-pmu 9095000.llcc-pmu: Registered llcc_pmu, type: 9
[    5.226005][    T1] coresight-csr 6001000.csr: CSR initialized: coresight-csr
[    5.233421][    T1] coresight-csr 6b0f000.csr: CSR initialized: coresight-swao-csr
[    5.261619][    T1] coresight coresight-etm0: CPU0: ETM v4.2 initialized
[    5.268510][    T1] coresight-etm4x 7040000.etm: CPU0: (null) initialized
[    5.276062][    T1] coresight coresight-etm1: CPU1: ETM v4.2 initialized
[    5.282950][    T1] coresight-etm4x 7140000.etm: CPU1: (null) initialized
[    5.290486][    T1] coresight coresight-etm2: CPU2: ETM v4.2 initialized
[    5.297373][    T1] coresight-etm4x 7240000.etm: CPU2: (null) initialized
[    5.304888][    T1] coresight coresight-etm3: CPU3: ETM v4.2 initialized
[    5.311787][    T1] coresight-etm4x 7340000.etm: CPU3: (null) initialized
[    5.319301][    T1] coresight coresight-etm4: CPU4: ETM v4.2 initialized
[    5.326199][    T1] coresight-etm4x 7440000.etm: CPU4: (null) initialized
[    5.333712][    T1] coresight coresight-etm5: CPU5: ETM v4.2 initialized
[    5.340610][    T1] coresight-etm4x 7540000.etm: CPU5: (null) initialized
[    5.348126][    T1] coresight coresight-etm6: CPU6: ETM v4.2 initialized
[    5.355024][    T1] coresight-etm4x 7640000.etm: CPU6: (null) initialized
[    5.362527][    T1] coresight coresight-etm7: CPU7: ETM v4.2 initialized
[    5.369425][    T1] coresight-etm4x 7740000.etm: CPU7: (null) initialized
[    5.376525][    T1] coresight-stm 6002000.stm: coresight-stm : stm_register_device failed, probing deferred
[    5.403843][    T1] coresight-dummy soc:dummy_sink: Dummy device initialized
[    5.411362][    T1] coresight-dummy soc:tpdm@6b46000: Dummy device initialized
[    5.419217][    T1] coresight-dummy soc:tpdm@69810000: Dummy device initialized
[    5.427146][    T1] coresight-dummy soc:dummy_source: Dummy device initialized
[    5.434619][    T1] coresight-dummy soc:snoc: Uses obsolete Coresight DT bindings
[    5.442673][    T1] coresight-dummy soc:snoc: Dummy device initialized
[    5.450402][    T1] coresight-remote-etm soc:turing_etm0: Remote ETM initialized
[    5.458450][    T1] coresight-remote-etm soc:modem2_etm0: Remote ETM initialized
[    5.466501][    T1] coresight-remote-etm soc:modem_etm0: Remote ETM initialized
[    5.474480][    T1] coresight-remote-etm soc:wpss_etm0: Remote ETM initialized
[    5.482209][    T1] coresight-remote-etm soc:audio_etm0: Remote ETM initialized
[    5.490956][    T1] gnss: GNSS driver registered with major 498
[    5.498022][    T1] usbcore: registered new interface driver snd-usb-audio
[    5.507013][    T1] u32 classifier
[    5.510519][    T1]     input device check on
[    5.515018][    T1]     Actions configured
[    5.519921][    T1] xt_time: kernel timezone is -0000
[    5.525166][    T1] ipip: IPv4 and MPLS over IPv4 tunneling driver
[    5.531665][    T1] gre: GRE over IPv4 demultiplexor driver
[    5.537405][    T1] ip_gre: GRE over IPv4 tunneling driver
[    5.543371][    T1] IPv4 over IPsec tunneling driver
[    5.548728][    T1] iptable_raw: Enabling raw table before defrag
[    5.555123][    T1] Initializing XFRM netlink socket
[    5.560241][    T1] IPsec XFRM device driver
[    5.564826][    T1] NET: Registered protocol family 10
[    5.570787][    T1] Segment Routing with IPv6
[    5.575330][    T1] mip6: Mobile IPv6
[    5.579222][    T1] ip6table_raw: Enabling raw table before defrag
[    5.585761][    T1] sit: IPv6, IPv4 and MPLS over IPv4 tunneling driver
[    5.592897][    T1] ip6_gre: GRE over IPv6 tunneling driver
[    5.598786][    T1] NET: Registered protocol family 17
[    5.604080][    T1] NET: Registered protocol family 15
[    5.609418][    T1] can: controller area network core (rev ******** abi 9)
[    5.616537][    T1] NET: Registered protocol family 29
[    5.621821][    T1] can: raw protocol (rev ********)
[    5.626927][    T1] can: broadcast manager protocol (rev ******** t)
[    5.633457][    T1] can: netlink gateway (rev ********) max_hops=1
[    5.640000][    T1] Bluetooth: RFCOMM TTY layer initialized
[    5.645739][    T1] Bluetooth: RFCOMM socket layer initialized
[    5.651745][    T1] Bluetooth: RFCOMM ver 1.11
[    5.656324][    T1] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
[    5.663123][    T1] Bluetooth: HIDP socket layer initialized
[    5.668953][    T1] l2tp_core: L2TP core driver, V2.0
[    5.674149][    T1] l2tp_ppp: PPPoL2TP kernel driver, V2.0
[    5.679785][    T1] tipc: Activated (version 2.0.0)
[    5.684856][    T1] NET: Registered protocol family 30
[    5.690175][    T1] tipc: Started in single node mode
[    5.696010][  T226] qcom_haven_qrtr soc:qrtr-haven: failed to raise doorbell -11
[    5.696784][    T1] No device name found in device tree.
[    5.710374][    T1] registered taskstats version 1
[    5.715311][    T1] Loading compiled-in X.509 certificates
[    5.723755][    T1] Key type ._fscrypt registered
[    5.728615][    T1] Key type .fscrypt registered
[    5.728616][    T1] Key type fscrypt-provisioning registered
[    5.728675][    T1] pstore: Invalid compression size for deflate: 0
[    5.773552][    T1] msm-dcc 117f000.dcc_v2: DCC list passed 3
[    5.779740][    T1] msm-dcc 117f000.dcc_v2: All values written to enable.
[    5.786882][    T1] msm-dcc 117f000.dcc_v2: DCC list passed 4
[    5.793262][    T1] msm-dcc 117f000.dcc_v2: All values written to enable.
[    5.800575][    T1] msm-dcc 117f000.dcc_v2: DCC list passed 6
[    5.806920][    T1] msm-dcc 117f000.dcc_v2: All values written to enable.
[    5.818019][   T67] qnoc-yupik soc:interconnect: Registered YUPIK ICC
[    5.825449][   T67] qnoc-yupik 1500000.interconnect: Registered YUPIK ICC
[    5.833145][   T67] qnoc-yupik 1502000.interconnect: Registered YUPIK ICC
[    5.840374][   T67] qnoc-yupik 1580000.interconnect: Registered YUPIK ICC
[    5.847564][   T67] qnoc-yupik 1680000.interconnect: Registered YUPIK ICC
[    5.854822][   T67] qnoc-yupik 16e0000.interconnect: Registered YUPIK ICC
[    5.862088][   T67] qnoc-yupik 1700000.interconnect: Registered YUPIK ICC
[    5.869326][   T67] qnoc-yupik 1740000.interconnect: Registered YUPIK ICC
[    5.876453][   T67] qnoc-yupik 3c40000.interconnect: Registered YUPIK ICC
[    5.883574][   T67] qnoc-yupik 90e0000.interconnect: Registered YUPIK ICC
[    5.890880][   T67] qnoc-yupik 9100000.interconnect: Registered YUPIK ICC
[    5.898019][   T67] qnoc-yupik a0c0000.interconnect: Registered YUPIK ICC
[    5.915374][   T67] cam_cc-yupik ad00000.clock-controller: Registered CAM CC clocks
[    5.926825][   T67] disp_cc-yupik af00000.clock-controller: Registered DISP CC clocks
[    5.937354][   T67] cam_cc_titan_top_gdsc: supplied by pm8350c_s2_level
[    5.944531][   T67] cam_cc_bps_gdsc: supplied by pm8350c_s2_level
[    5.951175][   T67] cam_cc_ife_0_gdsc: supplied by pm8350c_s2_level
[    5.957997][   T67] cam_cc_ife_1_gdsc: supplied by pm8350c_s2_level
[    5.964802][   T67] cam_cc_ife_2_gdsc: supplied by pm8350c_s2_level
[    5.971601][   T67] cam_cc_ipe_0_gdsc: supplied by pm8350c_s2_level
[    5.978426][   T67] disp_cc_mdss_core_gdsc: supplied by pm8350c_s2_level
[    5.985824][   T67] gpu_cx_gdsc: supplied by pm8350c_s2_level
[    5.992197][   T67] gpu_gx_gdsc: supplied by pm8350c_s7_level
[    5.998485][   T67] video_cc_mvs0_gdsc: supplied by pm8350c_s2_level
[    6.005376][   T67] video_cc_mvsc_gdsc: supplied by pm8350c_s2_level
[    6.013487][   T67] coresight-stm 6002000.stm: STM32 initialized
[    6.023291][   T67] arm-smmu 3da0000.kgsl-smmu: missing tcu_testbus_version property
[    6.031651][   T67] arm-smmu 3da0000.kgsl-smmu: 	coherent table walk
[    6.038186][   T67] arm-smmu 3da0000.kgsl-smmu: 	stream matching with 6 register groups
[    6.053031][   T67] adreno-a6xx-gmu 3d6a000.qcom,gmu: Adding to iommu group 0
[    6.062143][   T67] kgsl-3d 3d00000.qcom,kgsl-3d0: bound 3d6a000.qcom,gmu (ops a6xx_gmu_component_ops)
[    6.074213][   T67] platform 3da0000.qcom,kgsl-iommu:gfx3d_user: Adding to iommu group 1
[    6.083933][   T67] platform 3da0000.qcom,kgsl-iommu:gfx3d_lpac: Adding to iommu group 2
[    6.093470][   T67] platform 3da0000.qcom,kgsl-iommu:gfx3d_secure: Adding to iommu group 3
[    6.105218][   T67] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    6.112908][   T67] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[    6.122220][   T67] msm_vidc:   err : ffffffff: .....: no device Registered
[    6.136724][   T67] arm-smmu 15000000.apps-smmu: missing tcu_testbus_version property
[    6.145835][   T67] arm-smmu 15000000.apps-smmu: 	coherent table walk
[    6.152459][   T67] arm-smmu 15000000.apps-smmu: 	stream matching with 77 register groups
[    6.173864][   T67] gpi_dma 900000.qcom,gpi-dma: Adding to iommu group 4
[    6.182771][   T67] gpi_dma a00000.qcom,gpi-dma: Adding to iommu group 5
[    6.191135][   T67] minidump-id not found for trustedvm
[    6.196563][   T67] subsys-pil-tz soc:qcom,trustedvm@d0800000: for trustedvm segments only will be dumped.
[    6.206535][   T67] subsys-pil-tz soc:qcom,trustedvm@d0800000: for md_trustedvm segments only will be dumped.
[    6.217003][   T67] minidump-id not found for yupik_ipa_fws
[    6.222763][   T67] subsys-pil-tz soc:qcom,ipa_fws: for yupik_ipa_fws segments only will be dumped.
[    6.232085][   T67] subsys-pil-tz soc:qcom,ipa_fws: for md_yupik_ipa_fws segments only will be dumped.
[    6.241910][   T67] minidump-id not found for a660_zap
[    6.247225][   T67] subsys-pil-tz soc:qcom,kgsl-hyp: for a660_zap segments only will be dumped.
[    6.256195][   T67] subsys-pil-tz soc:qcom,kgsl-hyp: for md_a660_zap segments only will be dumped.
[    6.266412][   T67] hh_msgq: Registered client for label: 1
[    6.273079][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb1: Adding to iommu group 6
[    6.283388][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb2: Adding to iommu group 7
[    6.293686][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb3: Adding to iommu group 8
[    6.303964][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb4: Adding to iommu group 9
[    6.314343][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb5: Adding to iommu group 10
[    6.324697][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb6: Adding to iommu group 11
[    6.335094][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb7: Adding to iommu group 12
[    6.345473][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb8: Adding to iommu group 13
[    6.356191][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb9: Adding to iommu group 14
[    6.366587][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb10: Adding to iommu group 15
[    6.377142][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb11: Adding to iommu group 16
[    6.387625][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb12: Adding to iommu group 17
[    6.398123][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb13: Adding to iommu group 18
[    6.408606][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb14: Adding to iommu group 19
[    6.419086][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb15: Adding to iommu group 20
[    6.429559][   T67] fastrpc soc:qcom,msm_fastrpc:qcom,msm_fastrpc_compute_cb16: Adding to iommu group 21
[    6.439765][   T67] iommu-debug soc:kgsl_iommu_test_device: Adding to iommu group 22
[    6.448344][   T67] iommu-debug soc:apps_iommu_test_device: Adding to iommu group 23
[    6.456803][   T67] iommu-debug soc:apps_iommu_coherent_test_device: Adding to iommu group 24
[    6.468331][   T67] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    6.476062][   T67] spmi-temp-alarm c440000.qcom,spmi:qcom,pmr735a@4:qcom,temp-alarm@a00: could not read type
[    6.486248][   T67] spmi-temp-alarm: probe of c440000.qcom,spmi:qcom,pmr735a@4:qcom,temp-alarm@a00 failed with error -5
[    6.497660][   T67] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[    6.505358][   T67] spmi-temp-alarm c440000.qcom,spmi:qcom,pm7325b@3:qcom,temp-alarm@a00: could not read type
[    6.515538][   T67] spmi-temp-alarm: probe of c440000.qcom,spmi:qcom,pm7325b@3:qcom,temp-alarm@a00 failed with error -5
[    7.025347][   T67] ADC channel pm7325b_usb_conn_therm EOC took 489 ms
[    7.032066][   T67] ADC channel pm7325b_usb_conn_therm unexpected conversion fault
[    7.040345][   T67] adc-tm conversion request handshake timed out
[    7.046611][   T67] Error enabling adc-tm with -110
[    7.051636][   T67] thermal thermal_zone85: Failed to set trips: -110
[    7.560971][   T67] Reading ADC channel pm7325b_chg_skin_therm timed out
[    7.568356][   T67] adc-tm conversion request handshake timed out
[    7.574625][   T67] Error enabling adc-tm with -110
[    7.579681][   T67] thermal thermal_zone86: Failed to set trips: -110
[    7.589141][   T67] qce 1de0000.qcedev: Adding to iommu group 25
[    7.595672][   T67] QCE50: __qce_get_device_tree_data: CE operating frequency is not defined, setting to default 100MHZ
[    7.607061][   T67] qce 1de0000.qcedev: QTI Crypto 5.6.0 device found @0x1de0000
[    7.614890][   T67] sps_register_bam_device: sps:BAM 0x0000000001dc4000 is registered
[    7.623037][   T67] sps_bam_enable: sps:BAM 0x0000000001dc4000 (va:0xffffffc018540000) enabled: ver:0x27, number of pipes:16
[    7.634749][   T67] QCE50: qce_sps_init:  QTI MSM CE-BAM at 0x0000000001dc4000 irq 9
[    7.643677][   T67] qce 1de0000.qcedev:qcom_cedev_ns_cb: Adding to iommu group 26
[    7.652576][   T67] qce 1de0000.qcedev:qcom_cedev_s_cb: Adding to iommu group 27
[    7.661043][   T67] qcrypto 1de0000.qcrypto: Adding to iommu group 28
[    7.667971][   T67] QCE50: __qce_get_device_tree_data: CE operating frequency is not defined, setting to default 100MHZ
[    7.679474][   T67] qcrypto 1de0000.qcrypto: QTI Crypto 5.6.0 device found @0x1de0000
[    7.687742][   T67] QCE50: qce_sps_init:  QTI MSM CE-BAM at 0x0000000001dc4000 irq 9
[    7.696075][   T67] qcrypto 1de0000.qcrypto: qcrypto-ecb-aes
[    7.702164][   T67] qcrypto 1de0000.qcrypto: qcrypto-cbc-aes
[    7.708238][   T67] qcrypto 1de0000.qcrypto: qcrypto-ctr-aes
[    7.714310][   T67] qcrypto 1de0000.qcrypto: qcrypto-ecb-des
[    7.720424][   T67] qcrypto 1de0000.qcrypto: qcrypto-cbc-des
[    7.726490][   T67] qcrypto 1de0000.qcrypto: qcrypto-ecb-3des
[    7.732641][   T67] qcrypto 1de0000.qcrypto: qcrypto-cbc-3des
[    7.738798][   T67] qcrypto 1de0000.qcrypto: qcrypto-xts-aes
[    7.744870][   T67] qcrypto 1de0000.qcrypto: qcrypto-sha1
[    7.750661][   T67] qcrypto 1de0000.qcrypto: qcrypto-sha256
[    7.756668][   T67] qcrypto 1de0000.qcrypto: qcrypto-aead-hmac-sha1-cbc-aes
[    7.764087][   T67] qcrypto 1de0000.qcrypto: qcrypto-aead-hmac-sha1-cbc-des
[    7.771505][   T67] qcrypto 1de0000.qcrypto: qcrypto-aead-hmac-sha1-cbc-3des
[    7.778988][   T67] qcrypto 1de0000.qcrypto: qcrypto-aead-hmac-sha256-cbc-aes
[    7.786562][   T67] qcrypto 1de0000.qcrypto: qcrypto-aead-hmac-sha256-cbc-des
[    7.794139][   T67] qcrypto 1de0000.qcrypto: qcrypto-aead-hmac-sha256-cbc-3des
[    7.801818][   T67] qcrypto 1de0000.qcrypto: qcrypto-hmac-sha1
[    7.808066][   T67] qcrypto 1de0000.qcrypto: qcrypto-hmac-sha256
[    7.814485][   T67] qcrypto 1de0000.qcrypto: qcrypto-aes-ccm
[    7.820557][   T67] qcrypto 1de0000.qcrypto: qcrypto-rfc4309-aes-ccm
[    7.828263][   T67] uaudio-qmi soc:usb_audio_qmi_dev: Adding to iommu group 29
[    7.840372][   T67] icnss2 17a10040.qcom,wcn6750: Adding to iommu group 30
[    7.847458][   T67] icnss2: Recursive recovery allowed for WLAN
[    7.853884][   T67] icnss2 17a10040.qcom,wcn6750: for wcss_msa0 segments only will be dumped.
[    7.862678][   T67] icnss2 17a10040.qcom,wcn6750: for wcnss_phyareg segments only will be dumped.
[    7.871822][   T67] icnss2 17a10040.qcom,wcn6750: for wcnss_phydbg segments only will be dumped.
[    7.880881][   T67] icnss2 17a10040.qcom,wcn6750: for wcnss_wmac0reg segments only will be dumped.
[    7.890113][   T67] icnss2 17a10040.qcom,wcn6750: for wcnss_WCSSDBG segments only will be dumped.
[    7.899246][   T67] icnss2 17a10040.qcom,wcn6750: for wcnss_PHYAPDMEM segments only will be dumped.
[    7.908658][   T67] cnss_genl: genl_register_family fail: -17
[    7.914573][   T67] icnss2: ICNSS genl init failed -17
[    7.919893][   T67] icnss2: Platform driver probed successfully
[    7.931404][   T67] ufshcd-qcom 1d84000.ufshc: Adding to iommu group 31
[    7.938629][   T67] ufshcd-qcom 1d84000.ufshc: ufshcd_populate_vreg: unable to find vdd-hba-max-microamp
[    7.948405][   T67] ufshcd-qcom 1d84000.ufshc: ufshcd_populate_vreg: Unable to find vccq2-supply regulator, assuming enabled
[    7.960824][   T67] ufshcd-qcom 1d84000.ufshc: -- Registered bus voting! (0) --
[    7.968538][   T67] ufshcd-qcom 1d84000.ufshc: ufs_qcom_parse_reg_info: Unable to find qcom,vccq-parent-supply regulator, assuming enabled
[    7.982602][   T67] scsi host0: ufshcd
[    7.986612][   T67] crypto_qti_disable: vops ice data is invalid
[    7.999719][  T233] ufshcd-qcom 1d84000.ufshc: *** This is drivers/scsi/ufs/ufshcd.c ***
[    8.008593][   T67] qupv3_geni_se 9c0000.qcom,qupv3_0_geni_se: Adding to iommu group 32
[    8.017883][   T67] qupv3_geni_se ac0000.qcom,qupv3_1_geni_se: Adding to iommu group 33
[    8.020867][  T233] ufshcd-qcom 1d84000.ufshc: ufshcd_print_pwr_info:[RX, TX]: gear=[1, 1], lane[1, 1], pwr[SLOWAUTO_MODE, SLOWAUTO_MODE], rate = 0
[    8.026831][   T67] msm-dwc3 a600000.ssusb: Adding to iommu group 34
[    8.047223][   T67] dwc3 a600000.dwc3: changing max_speed on rev 00000000
[    8.057807][   T67] msm-dwc3 a600000.ssusb: no extcon provide
[    8.063873][    T8] msm-dwc3 a600000.ssusb: Could not get usb psy
[    8.064649][   T67] msm-dwc3 8c00000.hsusb: Adding to iommu group 35
[    8.074755][    T8] msm-dwc3 a600000.ssusb: DWC3 exited from low power mode
[    8.077313][  T234] sdhci_msm 8804000.sdhci: Adding to iommu group 36
[    8.090772][  T234] sdhci_msm 8804000.sdhci: Got CD GPIO
[    8.091561][   T67] msm-dwc3 8c00000.hsusb: No interconnects found.
[    8.096333][  T234] sdhci_msm 8804000.sdhci: core_reset unavailable,err = -2
[    8.103733][   T67] msm-dwc3 8c00000.hsusb: no extcon provide
[    8.110009][  T234] sdhci_msm 8804000.sdhci: TCXO clk not present (-2)
[    8.117016][   T67] coresight-tmc 6048000.tmc: Adding to iommu group 37
[    8.118385][  T313] msm-dwc3 8c00000.hsusb: DWC3 exited from low power mode
[    8.121046][  T233] ufshcd-qcom 1d84000.ufshc: ufshcd_print_pwr_info:[RX, TX]: gear=[1, 1], lane[1, 1], pwr[SLOWAUTO_MODE, SLOWAUTO_MODE], rate = 0
[    8.150980][   T67] sps_register_bam_device: sps:BAM 0x0000000006064000 is registered
[    8.152450][    T8] sdhci_msm 7c4000.sdhci: Adding to iommu group 38
[    8.155553][  T234] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 283, hwirq 91
[    8.155557][  T234] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 76
[    8.155563][  T234] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 283 parent hwirq 76
[    8.155896][  T234] mmc1: SDHCI controller on 8804000.sdhci [8804000.sdhci] using ADMA 64-bit
[    8.155901][  T234] sdhci_msm 8804000.sdhci: IRQ sdiowakeup_irq not found
[    8.162141][   T67] msm_geni_serial 994000.qcom,qup_uart: IRQ index 1 not found
[    8.166137][    T8] sdhci_msm 7c4000.sdhci: TCXO clk not present (-2)
[    8.166233][  T313] xhci-hcd xhci-hcd.2.auto: xHCI Host Controller
[    8.166255][  T313] xhci-hcd xhci-hcd.2.auto: new USB bus registered, assigned bus number 2
[    8.167850][  T313] xhci-hcd xhci-hcd.2.auto: hcc params 0x0220fe65 hci version 0x110 quirks 0x0000000000010010
[    8.167886][  T313] xhci-hcd xhci-hcd.2.auto: irq 282, io mem 0x08c00000
[    8.168175][  T313] xhci-hcd xhci-hcd.2.auto: xHCI Host Controller
[    8.168187][  T313] xhci-hcd xhci-hcd.2.auto: new USB bus registered, assigned bus number 3
[    8.168201][  T313] xhci-hcd xhci-hcd.2.auto: Host supports USB 3.0 SuperSpeed
[    8.169280][  T313] hub 2-0:1.0: USB hub found
[    8.169391][  T313] hub 2-0:1.0: 1 port detected
[    8.170144][  T313] usb usb3: We don't know the algorithms for LPM for this host, disabling LPM.
[    8.170888][  T313] hub 3-0:1.0: USB hub found
[    8.170948][  T313] hub 3-0:1.0: config failed, hub doesn't have any ports! (err -19)
[    8.173282][   T67] msm_geni_serial 994000.qcom,qup_uart: No wakeup IRQ configured
[    8.173321][   T67] msm_geni_serial 994000.qcom,qup_uart: Serial port0 added.FifoSize 64 is_console1
[    8.176588][  T233] ufshcd-qcom 1d84000.ufshc: ufshcd_print_pwr_info:[RX, TX]: gear=[3, 3], lane[2, 2], pwr[FAST MODE, FAST MODE], rate = 2
[    8.176674][  T233] ufshcd-qcom 1d84000.ufshc: ufshcd_find_max_sup_active_icc_level: Regulator capability was not set, actvIccLevel=0
[    8.176822][  T233] ufshcd-qcom 1d84000.ufshc: ufshcd_wb_config: Write Booster Configured
[    8.177482][  T233] scsi 0:0:0:49488: Well-known LUN    SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.178235][  T233] scsi 0:0:0:49488: Attached scsi generic sg0 type 30
[    8.178802][  T233] scsi 0:0:0:49476: Well-known LUN    SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.179473][  T233] scsi 0:0:0:49476: Attached scsi generic sg1 type 30
[    8.179970][  T233] scsi 0:0:0:49456: Well-known LUN    SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.180637][  T233] scsi 0:0:0:49456: Attached scsi generic sg2 type 30
[    8.181667][    T8] mmc0: CQHCI version 5.10
[    8.189673][   T67] msm_geni_serial 994000.qcom,qup_uart: msm_geni_serial_get_ver_info:HW version 805306368
[    8.200419][  T233] scsi 0:0:0:0: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.205368][   T67] msm_geni_serial 994000.qcom,qup_uart: gsi_mode:0
[    8.205384][   T67] 994000.qcom,qup_uart: ttyMSM0 at MMIO 0x994000 (irq = 215, base_baud = 0) is a MSM
[    8.213612][    T8] mmc0: SDHCI controller on 7c4000.sdhci [7c4000.sdhci] using ADMA 64-bit
[    8.220465][  T233] sd 0:0:0:0: Attached scsi generic sg3 type 0
[    8.220673][    C0] sd 0:0:0:0: Power-on or device reset occurred
[    8.221784][  T313] sd 0:0:0:0: [sda] 27547648 4096-byte logical blocks: (113 GB/105 GiB)
[    8.221933][  T313] sd 0:0:0:0: [sda] Write Protect is off
[    8.222229][  T313] sd 0:0:0:0: [sda] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.222390][  T313] sd 0:0:0:0: [sda] Optimal transfer size 524288 bytes
[    8.226083][    T8] sdhci_msm 7c4000.sdhci: mmc0: CQE init: success
[    8.235635][  T233] scsi 0:0:0:1: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.238857][  T313]  sda: sda1 sda2 sda3 sda4 sda5 sda6 sda7 sda8 sda9 sda10 sda11
[    8.242822][  T313] sd 0:0:0:0: [sda] Attached SCSI disk
[    8.245073][    T8] sdhci_msm 7c4000.sdhci: IRQ sdiowakeup_irq not found
[    8.252807][  T233] sd 0:0:0:1: Attached scsi generic sg4 type 0
[    8.253040][    C0] sd 0:0:0:1: Power-on or device reset occurred
[    8.254676][  T313] sd 0:0:0:1: [sdb] 2048 4096-byte logical blocks: (8.39 MB/8.00 MiB)
[    8.254828][  T313] sd 0:0:0:1: [sdb] Write Protect is off
[    8.255259][  T313] sd 0:0:0:1: [sdb] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.255405][  T313] sd 0:0:0:1: [sdb] Optimal transfer size 524288 bytes
[    8.417007][   T31] usb 2-1: new high-speed USB device number 2 using xhci-hcd
[    8.420090][  T233] scsi 0:0:0:2: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.431845][  T313]  sdb: sdb1 sdb2
[    8.434808][  T233] sd 0:0:0:2: Attached scsi generic sg5 type 0
[    8.434953][    C0] sd 0:0:0:2: Power-on or device reset occurred
[    8.436091][  T325] sd 0:0:0:2: [sdc] 2048 4096-byte logical blocks: (8.39 MB/8.00 MiB)
[    8.436237][  T325] sd 0:0:0:2: [sdc] Write Protect is off
[    8.436527][  T325] sd 0:0:0:2: [sdc] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.436675][  T325] sd 0:0:0:2: [sdc] Optimal transfer size 524288 bytes
[    8.448065][  T313] sd 0:0:0:1: [sdb] Attached SCSI disk
[    8.452771][  T233] scsi 0:0:0:3: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.455618][  T325]  sdc: sdc1 sdc2
[    8.458431][  T325] sd 0:0:0:2: [sdc] Attached SCSI disk
[    8.659831][   T67] printk: console [ttyMSM0] enabled
[    8.659831][   T67] printk: console [ttyMSM0] enabled
[    8.661217][  T233] sd 0:0:0:3: Attached scsi generic sg6 type 0
[    8.661217][  T233] sd 0:0:0:3: Attached scsi generic sg6 type 0
[    8.661455][    C0] sd 0:0:0:3: Power-on or device reset occurred
[    8.661455][    C0] sd 0:0:0:3: Power-on or device reset occurred
[    8.662631][    T8] sd 0:0:0:3: [sdd] 8192 4096-byte logical blocks: (33.6 MB/32.0 MiB)
[    8.662631][    T8] sd 0:0:0:3: [sdd] 8192 4096-byte logical blocks: (33.6 MB/32.0 MiB)
[    8.662785][    T8] sd 0:0:0:3: [sdd] Write Protect is off
[    8.662785][    T8] sd 0:0:0:3: [sdd] Write Protect is off
[    8.663100][    T8] sd 0:0:0:3: [sdd] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.663100][    T8] sd 0:0:0:3: [sdd] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.663253][    T8] sd 0:0:0:3: [sdd] Optimal transfer size 524288 bytes
[    8.663253][    T8] sd 0:0:0:3: [sdd] Optimal transfer size 524288 bytes
[    8.670254][   T67] printk: bootconsole [msm_geni_serial0] disabled
[    8.670254][   T67] printk: bootconsole [msm_geni_serial0] disabled
[    8.684338][  T233] scsi 0:0:0:4: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.684338][  T233] scsi 0:0:0:4: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.687163][    T8]  sdd: sdd1 sdd2 sdd3
[    8.687163][    T8]  sdd: sdd1 sdd2 sdd3
[    8.690354][    T8] sd 0:0:0:3: [sdd] Attached SCSI disk
[    8.690354][    T8] sd 0:0:0:3: [sdd] Attached SCSI disk
[    8.745747][   T31] usb 2-1: New USB device found, VID=2c7c, PID=0125 
[    8.745747][   T31] usb 2-1: New USB device found, VID=2c7c, PID=0125 
[    8.756889][  T233] sd 0:0:0:4: Attached scsi generic sg7 type 0
[    8.756889][  T233] sd 0:0:0:4: Attached scsi generic sg7 type 0
[    8.757161][    C0] sd 0:0:0:4: Power-on or device reset occurred
[    8.757161][    C0] sd 0:0:0:4: Power-on or device reset occurred
[    8.758320][  T325] sd 0:0:0:4: [sde] 1572864 4096-byte logical blocks: (6.44 GB/6.00 GiB)
[    8.758320][  T325] sd 0:0:0:4: [sde] 1572864 4096-byte logical blocks: (6.44 GB/6.00 GiB)
[    8.758477][  T325] sd 0:0:0:4: [sde] Write Protect is off
[    8.758477][  T325] sd 0:0:0:4: [sde] Write Protect is off
[    8.758776][  T325] sd 0:0:0:4: [sde] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.758776][  T325] sd 0:0:0:4: [sde] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.758925][  T325] sd 0:0:0:4: [sde] Optimal transfer size 524288 bytes
[    8.758925][  T325] sd 0:0:0:4: [sde] Optimal transfer size 524288 bytes
[    8.768811][   T31] option 2-1:1.0: GSM modem (1-port) converter detected
[    8.768811][   T31] option 2-1:1.0: GSM modem (1-port) converter detected
[    8.788289][  T233] scsi 0:0:0:5: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.788289][  T233] scsi 0:0:0:5: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.792098][  T325]  sde: sde1 sde2 sde3 sde4 sde5 sde6 sde7 sde8 sde9 sde10 sde11 sde12 sde13 sde14 sde15 sde16 sde17 sde18 sde19 sde20 sde21 sde22 sde23 sde24 sde25 sde26 sde27 sde28 sde29 sde30 sde31 sde32 sde33 sde34 sde35 sde36 sde37 sde38 sde39 sde40 sde41 sde42 sde43 sde44 sde45 sde46 sde47 sde48 sde49 sde50 sde51 sde52 sde53 sde54 sde55 sde56 sde57 sde58 sde59 sde60 sde61 sde62 sde63 sde64 sde65 sde66 sde67
[    8.792098][  T325]  sde: sde1 sde2 sde3 sde4 sde5 sde6 sde7 sde8 sde9 sde10 sde11 sde12 sde13 sde14 sde15 sde16 sde17 sde18 sde19 sde20 sde21 sde22 sde23 sde24 sde25 sde26 sde27 sde28 sde29 sde30 sde31 sde32 sde33 sde34 sde35 sde36 sde37 sde38 sde39 sde40 sde41 sde42 sde43 sde44 sde45 sde46 sde47 sde48 sde49 sde50 sde51 sde52 sde53 sde54 sde55 sde56 sde57 sde58 sde59 sde60 sde61 sde62 sde63 sde64 sde65 sde66 sde67
[    8.794917][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB0
[    8.794917][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB0
[    8.800838][  T325] sd 0:0:0:4: [sde] Attached SCSI disk
[    8.800838][  T325] sd 0:0:0:4: [sde] Attached SCSI disk
[    8.805816][  T233] sd 0:0:0:5: Attached scsi generic sg8 type 0
[    8.805816][  T233] sd 0:0:0:5: Attached scsi generic sg8 type 0
[    8.805982][    C0] sd 0:0:0:5: Power-on or device reset occurred
[    8.805982][    C0] sd 0:0:0:5: Power-on or device reset occurred
[    8.806900][  T325] sd 0:0:0:5: [sdf] 8192 4096-byte logical blocks: (33.6 MB/32.0 MiB)
[    8.806900][  T325] sd 0:0:0:5: [sdf] 8192 4096-byte logical blocks: (33.6 MB/32.0 MiB)
[    8.807024][  T325] sd 0:0:0:5: [sdf] Write Protect is off
[    8.807024][  T325] sd 0:0:0:5: [sdf] Write Protect is off
[    8.807263][  T325] sd 0:0:0:5: [sdf] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.807263][  T325] sd 0:0:0:5: [sdf] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.807382][  T325] sd 0:0:0:5: [sdf] Optimal transfer size 524288 bytes
[    8.807382][  T325] sd 0:0:0:5: [sdf] Optimal transfer size 524288 bytes
[    8.819580][   T31] option 2-1:1.1: GSM modem (1-port) converter detected
[    8.819580][   T31] option 2-1:1.1: GSM modem (1-port) converter detected
[    8.832654][  T233] scsi 0:0:0:6: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.832654][  T233] scsi 0:0:0:6: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.835417][  T325]  sdf: sdf1 sdf2 sdf3 sdf4 sdf5
[    8.835417][  T325]  sdf: sdf1 sdf2 sdf3 sdf4 sdf5
[    8.838717][  T325] sd 0:0:0:5: [sdf] Attached SCSI disk
[    8.838717][  T325] sd 0:0:0:5: [sdf] Attached SCSI disk
[    8.844129][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB1
[    8.844129][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB1
[    8.861640][  T233] sd 0:0:0:6: Attached scsi generic sg9 type 0
[    8.861640][  T233] sd 0:0:0:6: Attached scsi generic sg9 type 0
[    8.861839][    C0] sd 0:0:0:6: Power-on or device reset occurred
[    8.861839][    C0] sd 0:0:0:6: Power-on or device reset occurred
[    8.862925][  T325] sd 0:0:0:6: [sdg] 1048576 4096-byte logical blocks: (4.29 GB/4.00 GiB)
[    8.862925][  T325] sd 0:0:0:6: [sdg] 1048576 4096-byte logical blocks: (4.29 GB/4.00 GiB)
[    8.863071][  T325] sd 0:0:0:6: [sdg] Write Protect is off
[    8.863071][  T325] sd 0:0:0:6: [sdg] Write Protect is off
[    8.863354][  T325] sd 0:0:0:6: [sdg] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.863354][  T325] sd 0:0:0:6: [sdg] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.863502][  T325] sd 0:0:0:6: [sdg] Optimal transfer size 524288 bytes
[    8.863502][  T325] sd 0:0:0:6: [sdg] Optimal transfer size 524288 bytes
[    8.872805][   T31] option 2-1:1.2: GSM modem (1-port) converter detected
[    8.872805][   T31] option 2-1:1.2: GSM modem (1-port) converter detected
[    8.892785][  T233] scsi 0:0:0:7: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.892785][  T233] scsi 0:0:0:7: Direct-Access     SAMSUNG  KM2L9001CM-B518  0700 PQ: 0 ANSI: 6
[    8.897386][  T325] sd 0:0:0:6: [sdg] Attached SCSI disk
[    8.897386][  T325] sd 0:0:0:6: [sdg] Attached SCSI disk
[    8.905226][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB2
[    8.905226][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB2
[    8.920268][  T233] sd 0:0:0:7: Attached scsi generic sg10 type 0
[    8.920268][  T233] sd 0:0:0:7: Attached scsi generic sg10 type 0
[    8.920470][    C0] sd 0:0:0:7: Power-on or device reset occurred
[    8.920470][    C0] sd 0:0:0:7: Power-on or device reset occurred
[    8.921668][  T234] sd 0:0:0:7: [sdh] 1048576 4096-byte logical blocks: (4.29 GB/4.00 GiB)
[    8.921668][  T234] sd 0:0:0:7: [sdh] 1048576 4096-byte logical blocks: (4.29 GB/4.00 GiB)
[    8.921812][  T234] sd 0:0:0:7: [sdh] Write Protect is off
[    8.921812][  T234] sd 0:0:0:7: [sdh] Write Protect is off
[    8.922097][  T234] sd 0:0:0:7: [sdh] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.922097][  T234] sd 0:0:0:7: [sdh] Write cache: enabled, read cache: enabled, supports DPO and FUA
[    8.922242][  T234] sd 0:0:0:7: [sdh] Optimal transfer size 524288 bytes
[    8.922242][  T234] sd 0:0:0:7: [sdh] Optimal transfer size 524288 bytes
[    8.937630][   T31] option 2-1:1.3: GSM modem (1-port) converter detected
[    8.937630][   T31] option 2-1:1.3: GSM modem (1-port) converter detected
[    9.018536][  T234] sd 0:0:0:7: [sdh] Attached SCSI disk
[    9.018536][  T234] sd 0:0:0:7: [sdh] Attached SCSI disk
[    9.028031][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB3
[    9.028031][   T31] usb 2-1: GSM modem (1-port) converter now attached to ttyUSB3
[    9.465321][   T67] msm_geni_serial 980000.qcom,qup_uart: IRQ index 1 not found
[    9.472846][   T67] msm_geni_serial 980000.qcom,qup_uart: No wakeup IRQ configured
[    9.480724][   T67] msm_geni_serial 980000.qcom,qup_uart: Serial port1 added.FifoSize 64 is_console0
[    9.481054][   T31] qmi_wwan_q 2-1:1.4: cdc-wdm0: USB WDM device
[    9.490690][   T67] msm_geni_serial 980000.qcom,qup_uart: msm_geni_serial_get_ver_info:HW version 805306368
[    9.497474][   T31] qmi_wwan_q 2-1:1.4: Quectel Android work on RawIP mode
[    9.506339][   T67] msm_geni_serial 980000.qcom,qup_uart: gsi_mode:0
[    9.506507][   T67] 980000.qcom,qup_uart: ttyHS1 at MMIO 0x980000 (irq = 216, base_baud = 0) is a MSM
[    9.515461][   T31] qmi_wwan_q 2-1:1.4: rx_urb_size = 1520
[    9.521400][   T67] i2c_geni 984000.i2c: Multi-EE usecase
[    9.530131][   T31] qmi_wwan_q 2-1:1.4 wwan0: register 'qmi_wwan_q' at usb-xhci-hcd.2.auto-1, WWAN/QMI Raw IP device, 8e:ab:26:81:0b:35
[    9.535142][   T67] i2c_geni 984000.i2c: Bus frequency is set to 400000Hz.
[    9.535728][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.566034][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.571697][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.577383][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.583045][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.588705][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.594385][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.600054][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.605715][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.611393][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.617739][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.623220][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.629248][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.639363][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.644854][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.650868][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.661003][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.666506][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.672521][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.682646][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.688120][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.694147][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.704070][   T67] PM8008: pm8008_chip_probe: Failed to register chip enable regulator rc=-107
[    9.713031][   T67] qcom,pm8008-chip: probe of 984000.i2c:pm8008i@8:pm8008-chip@900 failed with error -107
[    9.723436][   T67] I2C PMIC: i2c_pmic_probe: I2C PMIC probe successful
[    9.730936][   T67] I2C PMIC: i2c_pmic_probe: I2C PMIC probe successful
[    9.738592][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.744103][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.750138][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.760262][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.765774][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.771831][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.781942][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.787436][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.793450][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.803584][   T67] i2c_geni 984000.i2c: i2c error :-107
[    9.809065][   T67] PM8008: pm8008_read: failed to read 0x0950
[    9.815078][   T67] PM8008: _pm8008_chip_is_enabled: qcom,pm8008-chip-en: failed to get chip state rc=-107
[    9.825009][   T67] PM8008: pm8008_chip_probe: Failed to register chip enable regulator rc=-107
[    9.834001][   T67] qcom,pm8008-chip: probe of 984000.i2c:pm8008j@c:pm8008-chip@900 failed with error -107
[    9.844368][   T67] I2C PMIC: i2c_pmic_probe: I2C PMIC probe successful
[    9.851837][   T67] I2C PMIC: i2c_pmic_probe: I2C PMIC probe successful
[    9.858845][   T67] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 299, hwirq 54
[    9.866605][   T67] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 117
[    9.873979][   T67] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 299 parent hwirq 117
[    9.883433][   T67] rtc-rx8900 4-0032: Update was detected
[    9.891254][   T67] rtc-rx8900 4-0032: registered as rtc1
[    9.896817][   T67] rtc-rx8900 4-0032: IRQ 299 supplied
[    9.902632][   T67] i2c_geni 984000.i2c: I2C probed
[    9.908321][   T67] i2c_geni 988000.i2c: Bus frequency is set to 400000Hz.
[    9.915828][   T67] i2c_geni 988000.i2c: I2C probed
[    9.921428][   T67] i2c_geni 98c000.i2c: Bus frequency is set to 400000Hz.
[    9.928901][   T67] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 300, hwirq 78
[    9.936592][   T67] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 105
[    9.943929][   T67] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 300 parent hwirq 105
[    9.954492][   T67] sgm7220_config_init: sgm7220 config initatial finished! reg08:0x00, reg09:0x20, reg0A:0x20, reg45:0x00
[    9.965825][   T67] sgm7220_parse_dt
[    9.969511][   T67] sgm7220_parse_dt select_gpio:429
[    9.974988][   T67] sgm7220_driver 3-0047: sgm7220 CC logic probe finished!
[    9.982169][   T67] i2c_geni 98c000.i2c: I2C probed
[    9.988206][   T67] spi_geni 990000.spi: spi_geni_probe: completed
[    9.995460][   T67] msm_geni_serial 99c000.qcom,qup_uart: Wakeup byte 0xfd
[   10.002694][   T67] msm_geni_serial 99c000.qcom,qup_uart: Serial port0 added.FifoSize 64 is_console0
[   10.012503][   T67] msm_geni_serial 99c000.qcom,qup_uart: msm_geni_serial_get_ver_info:HW version 805306368
[   10.022512][   T67] msm_geni_serial 99c000.qcom,qup_uart: gsi_mode:0
[   10.029130][   T67] 99c000.qcom,qup_uart: ttyHS0 at MMIO 0x99c000 (irq = 221, base_baud = 0) is a MSM
[   10.038827][   T67] qupv3_geni_se 9c0000.qcom,qupv3_0_geni_se: geni_se_add_ab_ib: 99c000.qcom,qup_uart: list add already done
[   10.051034][   T67] msm_geni_serial a80000.qcom,qup_uart: IRQ index 1 not found
[   10.058549][   T67] msm_geni_serial a80000.qcom,qup_uart: No wakeup IRQ configured
[   10.066357][   T67] msm_geni_serial a80000.qcom,qup_uart: No Shutdown config specified
[   10.074559][   T67] msm_geni_serial a80000.qcom,qup_uart: Serial port2 added.FifoSize 64 is_console0
[   10.084383][   T67] msm_geni_serial a80000.qcom,qup_uart: msm_geni_serial_get_ver_info:HW version 805306368
[   10.094394][   T67] msm_geni_serial a80000.qcom,qup_uart: gsi_mode:0
[   10.101101][   T67] a80000.qcom,qup_uart: ttyHS2 at MMIO 0xa80000 (irq = 235, base_baud = 0) is a MSM
[   10.111810][   T67] i2c_geni a84000.i2c: Bus frequency is set to 1000000Hz.
[   10.119342][   T67] i2c i2c-5: Failed to register i2c client sn-nci at 0x28 (-16)
[   10.127033][   T67] i2c i2c-5: of_i2c: Failure registering /soc/i2c@a84000/nq@28
[   10.134629][   T67] i2c i2c-5: Failed to create I2C device for /soc/i2c@a84000/nq@28
[   10.142600][   T67] i2c_geni a84000.i2c: I2C probed
[   10.148217][   T67] msm_geni_serial a88000.qcom,qup_uart: IRQ index 1 not found
[   10.155748][   T67] msm_geni_serial a88000.qcom,qup_uart: No wakeup IRQ configured
[   10.163612][   T67] msm_geni_serial a88000.qcom,qup_uart: Serial port3 added.FifoSize 64 is_console0
[   10.173505][   T67] msm_geni_serial a88000.qcom,qup_uart: msm_geni_serial_get_ver_info:HW version 805306368
[   10.183507][   T67] msm_geni_serial a88000.qcom,qup_uart: gsi_mode:0
[   10.190208][   T67] a88000.qcom,qup_uart: ttyHS3 at MMIO 0xa88000 (irq = 237, base_baud = 0) is a MSM
[   10.201321][   T67] spi_geni a90000.spi: spi_geni_probe: completed
[   10.208237][   T67] i2c_geni a94000.i2c: Bus frequency is set to 400000Hz.
[   10.215707][   T67] i2c_geni a94000.i2c: I2C probed
[   10.221140][   T67] minidump-id not found for venus
[   10.227128][   T67] msm_vidc_v4l2 aa00000.qcom,vidc:non_secure_cb: Adding to iommu group 39
[   10.236696][   T67] msm_vidc_v4l2 aa00000.qcom,vidc:secure_non_pixel_cb: Adding to iommu group 40
[   10.246829][   T67] msm_vidc_v4l2 aa00000.qcom,vidc:secure_bitstream_cb: Adding to iommu group 41
[   10.256816][   T67] msm_vidc_v4l2 aa00000.qcom,vidc:secure_pixel_cb: Adding to iommu group 42
[   10.269746][    T1] gpio gpiochip2: (c440000.qcom,spmi:qcom,pm7325@1:pinctrl@8800): allocate IRQ 301, hwirq 5
[   10.279981][    T1] gpio gpiochip2: (c440000.qcom,spmi:qcom,pm7325@1:pinctrl@8800): found parent hwirq 141
[   10.289912][    T1] gpio gpiochip2: (c440000.qcom,spmi:qcom,pm7325@1:pinctrl@8800): alloc_irqs_parent for 301 parent hwirq 141
[   10.301763][    T1] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 302, hwirq 121
[   10.309556][    T1] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 138
[   10.316899][    T1] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 302 parent hwirq 138
[   10.326129][    T1] input: gpio-keys as /devices/platform/soc/soc:gpio_keys/input/input1
[   10.335205][    T1] [NVT-ts] nvt_driver_init 3338: start
[   10.340702][    T1] [NVT-ts] nvt_driver_init 3346: finished
[   10.347616][    T1] bimc-bwmon: Zone thres larger than hw limit: hi:0 med:4000 lo:0
[   10.356316][    T1] bimc-bwmon: Zone thres larger than hw limit: hi:0 med:4000 lo:0
[   10.365187][    T1] bimc-bwmon: Zone thres larger than hw limit: hi:0 med:4000 lo:0
[   10.379872][    T1] RNDIS_IPA module is loaded.
[   10.384562][    T1] cfg80211: Loading compiled-in X.509 certificates for regulatory database
[   10.393897][    T1] cfg80211: Loaded X.509 cert 'sforshee: 00b28ddf47aef9cea7'
[   10.401427][    T1] cfg80211: Loaded X.509 cert 'wens: 61c038651aabdcf94bd0ac7ff06c7248db18c600'
[   10.410520][   T56] platform regulatory.0: Direct firmware load for regulatory.db failed with error -2
[   10.420089][   T56] platform regulatory.0: Falling back to sysfs fallback for: regulatory.db
[   10.420217][   T27] cryptomgr_probe (349): highest shadow stack usage: 168 bytes
[   10.425058][    T1] clk: Disabling unused clocks
[   10.425863][    T1] gpu_cc-yupik 3d90000.clock-controller: sync-state
[   10.425926][    T1] video_cc-yupik aaf0000.clock-controller: sync-state
[   10.426055][    T1] ALSA device list:
[   10.426068][    T1]   No soundcards found.
[   10.465104][    T1] Freeing unused kernel memory: 5824K
[   10.489182][    T1] Run /init as init process
[   10.502859][    T1] init: init first stage started!
[   10.508298][    T1] init: Loading module /lib/modules/panel-lcd-st7789vi.ko with args ""
[   10.517320][    T1] st7789vi spi0.0: st7789vi_probe: Allocated 262144 bytes (64 pages) at virtual 0xffffff8d0ec40000
[   10.677336][    T1] spi_geni 990000.spi: tx_fifo 16 rx_fifo 16 tx_width 32
[   10.835140][    T1] get_disable_lcd_state: disable_lcd = 0
[   10.841245][    T1] init: Loaded kernel module /lib/modules/panel-lcd-st7789vi.ko
[   10.849417][    T1] init: Loading module /lib/modules/msm_drm.ko with args ""
[   10.879716][    T1] [drm:sde_power_parse_ib_votes:474]: error reading min core ib vote. rc=-22, np=3f685098
[   10.889739][    T1] [drm:sde_power_parse_ib_votes:482]: error reading min llcc ib vote. rc=-22
[   10.898578][    T1] [drm:sde_power_parse_ib_votes:490]: error reading min dram ib vote. rc=-22
[   10.907641][    T1] [sde_rsc_hw:rsc_hw_init_v3:537]: sde rsc init successfully done
[   10.915526][    T1] [sde_rsc:sde_rsc_probe:1799]: sde rsc index:0 probed successfully
[   10.924497][    T1] dsi_phy ae94400.qcom,mdss_dsi_phy0: ae94400.qcom,mdss_dsi_phy0 supply gdsc not found, using dummy regulator
[   10.936610][    T1] [drm:dsi_pll_init [msm_drm]] [msm-dsi-info]: DSI_PLL_0: DSI pll label = dsi_pll_5nm
[   10.946372][    T1] [drm:dsi_pll_init [msm_drm]] [msm-dsi-info]: DSI_PLL_0: PLL SSC enabled
[   10.954964][    T1] dsi_pll_init: PLL base=ffffffc015609900
[   10.960991][    T1] dsi_pll_5nm_get_gdsc_status: reg:0xf822f800 status:1
[   10.972945][    T1] vco_5nm_recalc_rate: PLL not enabled
[   10.978453][    T1] vco_5nm_recalc_rate: handoff_resources false
[   10.985431][    T1] dsi_pll_5nm_get_gdsc_status: reg:0xf822f800 status:1
[   10.997401][    T1] vco_5nm_recalc_rate: PLL not enabled
[   11.002865][    T1] vco_5nm_recalc_rate: handoff_resources false
[   11.010164][    T1] dsi_pll_clock_register_5nm: Registered DSI PLL ndx=0, clocks successfully
[   11.019470][    T1] [drm:dsi_phy_driver_probe [msm_drm]] [msm-dsi-info]: DSI_0: Probe successful
[   11.032115][  T325] OF: graph: no port node found in /soc/qcom,dsi-display-primary
[   11.040539][  T325] [drm:dsi_ctrl_get [msm_drm]] *ERROR* [msm-dsi-error]: inv: Device with of node not found rc=-517
[   11.051552][  T325] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: failed to get dsi controller, rc=-517
[   11.062820][  T325] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: [(null)] failed to initialize resources, rc=-517
[   11.075061][  T325] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: device init failed, rc=-517
[   11.085922][    T1] OF: graph: no port node found in /soc/qcom,dsi-display-primary
[   11.093802][    T1] [drm:dsi_ctrl_get [msm_drm]] *ERROR* [msm-dsi-error]: inv: Device with of node not found rc=-517
[   11.104704][    T1] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: failed to get dsi controller, rc=-517
[   11.115967][    T1] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: [(null)] failed to initialize resources, rc=-517
[   11.128200][    T1] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: device init failed, rc=-517
[   11.140656][    T1] msmdrm_smmu soc:qcom,smmu_sde_unsec_cb: Adding to iommu group 43
[   11.143360][   T67] OF: graph: no port node found in /soc/qcom,dsi-display-primary
[   11.148702][    T1] [drm] probing device qcom,smmu_sde_unsec
[   11.157181][   T67] [drm:dsi_ctrl_get [msm_drm]] *ERROR* [msm-dsi-error]: inv: Device with of node not found rc=-517
[   11.162267][    T1] [drm] Created domain mdp_ns, secure=0
[   11.162909][    T1] msmdrm_smmu soc:qcom,smmu_sde_sec_cb: Adding to iommu group 44
[   11.173306][   T67] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: failed to get dsi controller, rc=-517
[   11.178734][    T1] [drm] probing device qcom,smmu_sde_sec
[   11.186547][   T67] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: [(null)] failed to initialize resources, rc=-517
[   11.197778][    T1] [drm] Created domain mdp_s, secure=1
[   11.203519][   T67] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: device init failed, rc=-517
[   11.219073][    T1] [drm:sde_dbg_init:2392] evtlog_status: enable:11, panic:1, dump:2
[   11.222755][   T67] OF: graph: no port node found in /soc/qcom,dsi-display-primary
[   11.233414][    T1] init: Loaded kernel module /lib/modules/msm_drm.ko
[   11.239437][   T67] [drm:dsi_ctrl_get [msm_drm]] *ERROR* [msm-dsi-error]: inv: Device with of node not found rc=-517
[   11.239541][   T67] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: failed to get dsi controller, rc=-517
[   11.247584][    T1] init: Switching root to '/first_stage_ramdisk'
[   11.254016][   T67] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: [(null)] failed to initialize resources, rc=-517
[   11.254087][   T67] [drm:dsi_display_dev_probe [msm_drm]] *ERROR* [msm-dsi-error]: device init failed, rc=-517
[   11.265275][    T1] init: [libfs_mgr]dt_fstab: Skip disabled entry for partition vendor
[   11.277455][   T67] [drm:dsi_ctrl_dev_probe [msm_drm]] [msm-dsi-info]: dsi-ctrl-0: Probe successful
[   11.282374][    T1] init: [libfs_mgr]ReadFstabFromDt(): failed to read fstab from dt
[   11.295402][   T67] OF: graph: no port node found in /soc/qcom,dsi-display-primary
[   11.304906][    T1] init: [libfs_mgr]dt_fstab: Skip disabled entry for partition vendor
[   11.313032][   T67] [drm] [msm-dsi-warn]: [virtualdisplay st7789vi 240p video mode dsi panel] fallback to default te-pin-select
[   11.322862][    T1] init: Using Android DT directory /proc/device-tree/firmware/android/
[   11.330374][   T67] [drm:dsi_panel_parse_gpios [msm_drm]] [msm-dsi-info]: skip panel power, unparse gpios
[   11.389258][   T31] sd 0:0:0:0: [sda] Synchronizing SCSI cache
[   11.397015][   T67] msm_drm ae00000.qcom,mdss_mdp: bound soc:qcom,smmu_sde_unsec_cb (ops msm_smmu_comp_ops [msm_drm])
[   11.400268][    T1] e2fsck: e2fsck 1.45.4 (23-Sep-2019)
[   11.408074][   T67] msm_drm ae00000.qcom,mdss_mdp: bound soc:qcom,smmu_sde_sec_cb (ops msm_smmu_comp_ops [msm_drm])
[   11.413336][    T1] e2fsck: /dev/block/by-name/metadata: recovering journal
[   11.424142][   T67] msm_drm ae00000.qcom,mdss_mdp: bound soc:qcom,wb-display@0 (ops sde_wb_comp_ops [msm_drm])
[   11.426521][  T362] e2fsck (362) used greatest stack depth: 12160 bytes left
[   11.431208][    T1] e2fsck: Pass 1: Checking inodes, blocks, and sizes
[   11.441906][   T67] [drm:dsi_display_bind [msm_drm]] [msm-dsi-info]: Successfully bind display panel 'qcom,mdss_dsi_virtualdisplay_st7789vi_240p_video'
[   11.448732][    T1] e2fsck: Pass 2: Checking directory structure
[   11.448736][    T1] e2fsck: Pass 3: Checking directory connectivity
[   11.455484][   T67] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 303, hwirq 80
[   11.469374][    T1] e2fsck: Pass 4: Checking reference counts
[   11.469378][    T1] e2fsck: Pass 5: Checking group summary information
[   11.475558][   T67] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 118
[   11.482004][    T1] e2fsck: Free blocks count wrong (2518, counted=2519).
[   11.489707][   T67] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 303 parent hwirq 118
[   11.495616][    T1] e2fsck: Fix? yes
[   11.502538][   T67] msm_drm ae00000.qcom,mdss_mdp: bound soc:qcom,dsi-display-primary (ops dsi_display_comp_ops [msm_drm])
[   11.509683][    T1] e2fsck: 
[   11.516736][   T67] msm_drm ae00000.qcom,mdss_mdp: bound af20000.qcom,sde_rscc (ops sde_rsc_comp_ops [msm_drm])
[   11.528990][    T1] EXT4-fs (sda9): mounted filesystem with ordered data mode. Opts: discard
[   11.529612][   T67] msm_drm ae00000.qcom,mdss_mdp: bound ae90000.qcom,dp_display (ops dp_display_comp_ops [msm_drm])
[   11.550461][    T1] device-mapper: verity: sha256 using implementation "sha256-ce"
[   11.554180][   T67] [drm] mapped mdp address space @ffffffc01a500000
[   11.568144][    T1] EXT4-fs (dm-5): mounting with "discard" option, but the device does not support discard
[   11.574754][   T67] [drm:_sde_kms_hw_init_blocks:4682] sde hardware revision:0x70020000
[   11.581470][    T1] EXT4-fs (dm-5): mounted filesystem without journal. Opts: barrier=1,discard
[   11.586443][    T1] device-mapper: verity: sha256 using implementation "sha256-ce"
[   11.588331][   T67] [drm:msm_smmu_new [msm_drm]] *ERROR* unable to find domain 1 compat: qcom,smmu_sde_nrt_unsec
[   11.602883][    T1] EXT4-fs (dm-6): mounting with "discard" option, but the device does not support discard
[   11.606435][   T67] [drm:msm_smmu_new [msm_drm]] *ERROR* unable to find domain 3 compat: qcom,smmu_sde_nrt_sec
[   11.615211][    T1] EXT4-fs (dm-6): mounted filesystem without journal. Opts: barrier=1,discard
[   11.616209][    T1] device-mapper: verity: sha256 using implementation "sha256-ce"
[   11.621115][   T58] e2fsck (362): highest shadow stack usage: 344 bytes
[   11.636446][   T67] [drm:dsi_panel_get_mode [msm_drm]] [msm-dsi-info]: default topology: lm: 1 comp_enc:0 intf: 1
[   11.648316][    T1] EXT4-fs (dm-7): mounting with "discard" option, but the device does not support discard
[   11.654084][   T67] [drm:dp_parser_get_vreg][msm-dp-warn][67  ]no supply entry present: ???
[   11.662710][    T1] EXT4-fs (dm-7): mounted filesystem without journal. Opts: barrier=1,discard
[   11.663780][    T1] device-mapper: verity: sha1 using implementation "sha1-ce"
[   11.670516][   T67] [drm:dp_parser_get_vreg][msm-dp-warn][67  ]no supply entry present: ???
[   11.682461][    T1] EXT4-fs (dm-8): mounting with "discard" option, but the device does not support discard
[   11.687916][   T67] [drm:dp_parser_get_vreg][msm-dp-warn][67  ]no supply entry present: ???
[   11.697918][    T1] EXT4-fs (dm-8): mounted filesystem without journal. Opts: barrier=1,discard
[   11.698898][    T1] device-mapper: verity: sha1 using implementation "sha1-ce"
[   11.706542][   T67] [drm:dp_parser_get_vreg][msm-dp-warn][67  ]no supply entry present: qcom,pll-supply-entries
[   11.719820][    T1] EXT4-fs (dm-9): mounting with "discard" option, but the device does not support discard
[   11.724021][   T67] [drm:dp_pll_get][msm-dp-info][67  ]revision=DP_PLL_7NM, ssc_en=0, bonding_en=0
[   11.731466][    T1] EXT4-fs (dm-9): mounted filesystem without journal. Opts: barrier=1,discard
[   11.766653][    T1] printk: init: 7 output lines suppressed due to ratelimiting
[   11.777280][   T67] [drm:dp_power_clk_init][msm-dp-err][67  ]Unable to get DP link clk RCG: -2
[   11.786863][    T1] printk: init: 40 output lines suppressed due to ratelimiting
[   11.796055][   T67] [drm:dp_power_clk_init][msm-dp-err][67  ]Unable to get DP link parent: -2
[   11.838645][   T67] [drm:dp_display_initialize_hdcp][msm-dp-info][67  ]HDCP 1.3 initialized
[   11.847651][   T67] [drm:dp_display_initialize_hdcp][msm-dp-info][67  ]HDCP 2.2 initialized
[   11.857602][   T67] hh_msgq: Registered client for label: 2
[   11.864453][   T67] [drm] Supports vblank timestamp caching Rev 2 (21.10.2013).
[   11.871979][   T67] [drm] No driver support for vblank timestamp query.
[   11.879486][   T67] [drm] Initialized msm_drm 1.4.0 20130625 for ae00000.qcom,mdss_mdp on minor 0
[   11.888624][   T67] [drm] cont_splash feature not enabled
[   11.895271][   T67] disp_cc-yupik af00000.clock-controller: sync-state
[   11.932134][    T1] init: [libfs_mgr]dt_fstab: Skip disabled entry for partition vendor
[   11.946382][    T1] init: Opening SELinux policy
[   11.952757][    T1] init: Falling back to standard signature check. TODO implementent support for fsverity SEPolicy.
[   11.963677][    T1] init: Error: Apex SEPolicy failed signature check
[   11.970342][    T1] init: Loading APEX Sepolicy from /system/etc/selinux/apex/SEPolicy.zip
[   11.978956][    T1] init: Failed to open package /system/etc/selinux/apex/SEPolicy.zip: No such file or directory
[   11.991498][    T1] init: /system/etc/selinux/plat_sepolicy_and_mapping.sha256 and /odm/etc/selinux/precompiled_sepolicy.plat_sepolicy_and_mapping.sha256 differ
[   12.006236][    T1] init: Compiling SELinux policy
[   12.011349][    T8] (virq:irq_count)- 217:18543 3:3759 299:3681 47:930 57:289 220:61 282:55 54:53 219:17 74:13 
[   12.021802][    T8] (cpu:irq_count)- 0:24404 1:257 2:547 3:353 4:198 5:85 6:110 7:1493 
[   12.030103][    T8] (ipi:irq_count)- 0:5868 1:499 2:0 3:0 4:0 5:919 6:0 
[   12.121148][  T336] sd 0:0:0:5: [sdf] Synchronizing SCSI cache
[   12.127274][   T64] sd 0:0:0:2: [sdc] Synchronizing SCSI cache
[   12.133532][   T70] sd 0:0:0:6: [sdg] Synchronizing SCSI cache
[   12.139688][  T352] sd 0:0:0:3: [sdd] Synchronizing SCSI cache
[   12.145155][  T131] sd 0:0:0:7: [sdh] Synchronizing SCSI cache
[   13.416224][  T400] secilc (400) used greatest stack depth: 11136 bytes left
[   13.416337][    T1] init: /system/bin/secilc: linker: Warning: failed to find generated linker configuration from "/linkerconfig/ld.config.txt"
[   13.436778][    T1] init: /system/bin/secilc: WARNING: linker: Warning: failed to find generated linker configuration from "/linkerconfig/ld.config.txt"
[   13.473133][   T58] secilc (400): highest shadow stack usage: 352 bytes
[   13.474799][    T1] SELinux:  policy capability network_peer_controls=1
[   13.486774][    T1] SELinux:  policy capability open_perms=1
[   13.492601][    T1] SELinux:  policy capability extended_socket_class=1
[   13.499410][    T1] SELinux:  policy capability always_check_network=0
[   13.506121][    T1] SELinux:  policy capability cgroup_seclabel=0
[   13.512391][    T1] SELinux:  policy capability nnp_nosuid_transition=1
[   13.697468][   T65] audit: type=1403 audit(24.551:2): auid=4294967295 ses=4294967295 lsm=selinux res=1
[   13.707092][   T65] audit: type=1404 audit(24.555:3): enforcing=1 old_enforcing=0 auid=4294967295 ses=4294967295 enabled=1 old-enabled=1 lsm=selinux res=1
[   13.709525][    T1] printk: init: 5 output lines suppressed due to ratelimiting
[   13.736766][    T1] init: init second stage started!
[   13.767493][    T1] init: Using Android DT directory /proc/device-tree/firmware/android/
[   13.779926][    T1] init: Couldn't load property file '/system_dlkm/etc/build.prop': open() failed: No such file or directory: No such file or directory
[   13.797537][    T1] init: Do not have permissions to set 'persist.backup.ntpServer' to '0.pool.ntp.org' in property file '/vendor/build.prop': SELinux permission check failed
[   13.813590][    T1] init: Overriding previous property 'dalvik.vm.heapsize':'36m' with new value '512m'
[   13.823817][    T1] init: Do not have permissions to set 'persist.vendor.camera.AEthresholdMax' to '330' in property file '/vendor/build.prop': SELinux permission check failed
[   13.839896][    T1] init: Do not have permissions to set 'persist.vendor.camera.AEthresholdMin' to '300' in property file '/vendor/build.prop': SELinux permission check failed
[   13.855969][    T1] init: Do not have permissions to set 'persist.vendor.camera.LuxindexWeighting' to '0.1' in property file '/vendor/build.prop': SELinux permission check failed
[   13.872327][    T1] init: Do not have permissions to set 'persist.vendor.camera.DayNightEnable' to '1' in property file '/vendor/build.prop': SELinux permission check failed
[   13.888224][    T1] init: Do not have permissions to set 'persist.vendor.camera.NumFramesToDelaySampling' to '30' in property file '/vendor/build.prop': SELinux permission check failed
[   13.989148][   T65] audit: type=1107 audit(24.843:4): pid=1 uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { read } for property=init.svc.ota_uart_force pid=0 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:init_service_status_private_prop:s0 tclass=file permissive=0'
[   13.993976][    T1] cgroup2: Unknown parameter 'memory_recursiveprot'
[   14.024711][    T1] cgroup1: Unknown subsys name 'schedtune'
[   14.063351][  T403] linkerconfig: Unable to access VNDK APEX at path: /apex/com.android.vndk.v30: No such file or directory
[   14.074929][  T403] linkerconfig: Unable to access VNDK APEX at path: /apex/com.android.vndk.v33: No such file or directory
[   14.122609][  T404] ueventd: ueventd started!
[   14.129449][  T404] selinux: SELinux: Loaded file_contexts
[   14.135114][  T404] selinux: 
[   14.138444][  T404] ueventd: Parsing file /system/etc/ueventd.rc...
[   14.145209][  T404] ueventd: Added '/vendor/etc/ueventd.rc' to import list
[   14.152291][  T404] ueventd: Added '/odm/etc/ueventd.rc' to import list
[   14.159292][  T404] ueventd: Parsing file /vendor/etc/ueventd.rc...
[   14.161396][  T405] apexd: Bootstrap subcommand detected
[   14.165766][  T404] ueventd: Unable to read config file '/vendor/etc/ueventd.rc': open() failed: No such file or directory
[   14.171285][  T405] apexd: ActivateFlattenedApex
[   14.182593][  T404] ueventd: Parsing file /odm/etc/ueventd.rc...
[   14.187449][  T405] apexd: Scanning /system/apex
[   14.193547][  T404] ueventd: Unable to read config file '/odm/etc/ueventd.rc': open() failed: No such file or directory
[   14.200336][  T405] apexd: Bind mounting /system/apex/com.android.adbd onto /apex/com.android.adbd
[   14.219699][  T405] apexd: Bind mounting /system/apex/com.android.adservices onto /apex/com.android.adservices
[   14.231400][  T405] apexd: Bind mounting /system/apex/com.android.appsearch onto /apex/com.android.appsearch
[   14.243078][  T405] apexd: Bind mounting /system/apex/com.android.art onto /apex/com.android.art
[   14.253371][  T405] apexd: Bind mounting /system/apex/com.android.cellbroadcast onto /apex/com.android.cellbroadcast
[   14.265481][  T405] apexd: Bind mounting /system/apex/com.android.conscrypt onto /apex/com.android.conscrypt
[   14.276632][  T405] apexd: Bind mounting /system/apex/com.android.extservices onto /apex/com.android.extservices
[   14.308517][  T405] printk: apexd: 26 output lines suppressed due to ratelimiting
[   14.316360][  T405] apexd (405) used greatest stack depth: 10864 bytes left
[   14.361557][   T52] apexd (405): highest shadow stack usage: 400 bytes
[   14.402009][   T65] audit: type=1400 audit(25.255:5): avc:  denied  { mounton } for  pid=1 comm="init" path="/sys/kernel/tracing" dev="tracefs" ino=1 scontext=u:r:init:s0 tcontext=u:object_r:debugfs_tracing_debug:s0 tclass=dir permissive=0
[   14.743936][  T425] rmnet_ctl: loading out-of-tree module taints kernel.
[   14.770946][  T425] DATARMNET163e93649e(): Starting rmnet SHS module 9731 
[   14.799278][  T430] msm-cdc-pinctrl soc:wsa_spkr_en1_pinctrl: msm_cdc_pinctrl_probe: Cannot get aud_active pinctrl state:-19
[   14.820387][  T430] msm-cdc-pinctrl soc:wsa_spkr_en2_pinctrl: msm_cdc_pinctrl_probe: Cannot get aud_active pinctrl state:-19
[   14.834562][  T432] tc956x_init_module
[   14.834734][  T432] tc956x_init_module
[   14.868146][  T441] DATARMNETb1c42acd9c(): rmnet_offload initializing
[   14.876572][  T436] qcom_llcc_edac qcom_llcc_edac: No ECC IRQ; defaulting to polling mode
[   14.887113][  T436] EDAC DEVICE1: Giving out device to module qcom_llcc_edac controller llcc: DEV qcom_llcc_edac (POLLED)
[   14.927611][  T443] rs232_pwr_en soc:rs232_pwr_en: RS232_PWR_EN control driver probed successfully
[   14.938446][  T444] bt-debug-gpio not provided in devicetree
[   14.948691][  T444] xo-reset-gpio not provided in devicetree
[   14.954928][  T444] bt_dt_parse_clk_info: clocks is not provided in device tree
[   14.962484][  T444] bt_power_populate_dt_pinfo: clock not provided in device tree
[   14.971804][  T444] No TCS CMD entry found in DTSI
[   14.976774][  T444] bt_power_probe: Failed to get TCS table info
[   14.983225][  T474] tsnv tsnv_init register TSNV Driver v0.01
[   14.993099][  T435] q6lsm_init: panic_on_timeout debugfs flag is created
[   15.006457][  T475] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 305, hwirq 81
[   15.017769][  T475] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 119
[   15.036301][  T475] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 305 parent hwirq 119
[   15.078302][  T475] [CHSC] function = semi_touch_power_ctrl         , line = 145 : vdd power up...
[   15.095163][  T461] ngd_msm_ctrl 3ac0000.slim: Adding to iommu group 45
[   15.101293][  T475] [CHSC] function = semi_touch_get_rotation       , line = 71  : rotation set to 270 degrees
[   15.131428][  T475] input: chsc_cap_touch as /devices/platform/soc/a94000.i2c/i2c-6/6-002e/input/input2
[   15.145295][  T520] nfc_i2c_dev_init: Loading NXP NFC I2C driver
[   15.166069][  T373] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 308, hwirq 41
[   15.174342][  T475] [CHSC] function = semi_touch_reset              , line = 14  : set status before reset tp...
[   15.177036][  T454] gpio_info probe succeed
[   15.182871][  T373] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 98
[   15.196730][  T373] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 308 parent hwirq 98
[   15.205902][  T373] nfc_parse_dt: irq 377
[   15.210078][  T373] nfc_parse_dt: 377, 374, 376
[   15.210585][  T512] [Awinic]aw882xx_i2c_init: aw882xx driver version v1.11.0
[   15.214891][  T373] nfc_i2c_dev_probe: requesting IRQ 308
[   15.222005][  T512] LINUX_VERSION_CODE = 328959
[   15.222007][  T512] KERNEL_VERSION(4, 19, 1) = 267009
[   15.222352][  T475] [CHSC] function = semi_touch_reset              , line = 26  : set status pointing...
[   15.225531][  T512] [Awinic]aw882xx_i2c_probe: enter addr=0x34
[   15.225558][  T512] [Awinic][1-0034]aw882xx_parse_gpio_dt: no reset gpio provided, will not HW reset device
[   15.225587][  T512] [Awinic][1-0034]aw882xx_parse_gpio_dt: irq gpio provided ok.
[   15.225598][  T512] [Awinic][1-0034]aw882xx_parse_gpio_dt: no power-gpio provided.
[   15.225607][  T512] [Awinic][1-0034]aw882xx_parse_dt: dc-flag = 0
[   15.225615][  T512] [Awinic][1-0034]aw882xx_parse_dt: read sync flag failed,default phase sync off
[   15.225829][  T512] aw882xx_smartpa 1-0034: 1-0034 supply vcc_i2c not found, using dummy regulator
[   15.228248][  T512] [Awinic][1-0034]aw882xx_hw_reset: enter
[   15.233381][  T475] [CHSC] function = semi_touch_start_up_check     , line = 495 : retry-0, firmware is not ready
[   15.237685][  T512] [Awinic][1-0034]aw882xx_hw_reset: has no reset gpio
[   15.257008][  T484] msm_cam_smmu soc:qcom,cam_smmu:msm_cam_smmu_ife: Adding to iommu group 46
[   15.261746][  T475] [CHSC] function = semi_touch_start_up_check     , line = 495 : retry-1, firmware is not ready
[   15.265267][  T512] [Awinic][1-0034]aw882xx_read_chipid: aw882xx 2113 detected
[   15.269161][  T373] nfc_i2c_dev_probe: probing nfc i2c successfully
[   15.274997][  T484] msm_cam_smmu soc:qcom,cam_smmu:msm_cam_smmu_jpeg: Adding to iommu group 47
[   15.277887][  T475] [CHSC] function = semi_touch_start_up_check     , line = 495 : retry-2, firmware is not ready
[   15.284233][  T512] [Awinic][1-0034]aw882xx_device_parse_topo_id_dt: tx-topo-id: 0x1000ff00, rx-topo-id: 0x1000ff01
[   15.287116][  T484] msm_cam_smmu soc:qcom,cam_smmu:msm_cam_smmu_icp: Adding to iommu group 48
[   15.290628][  T459] wlan: Loading driver v2.0.8.34Y +TIMER_MANAGER +MEMORY_DEBUG +PANIC_ON_BUG; cld:; cmn:; dev:qca6750
[   15.293903][  T475] [CHSC] function = semi_touch_start_up_check     , line = 495 : retry-3, firmware is not ready
[   15.294431][  T512] [Awinic][1-0034]aw882xx_device_parse_port_id_dt: tx-port-id: 0x1007, rx-port-id: 0x1006
[   15.294435][  T512] [Awinic][1-0034]aw_cali_parse_dt: cali-check get failed ,default turn off
[   15.294437][  T512] [Awinic][1-0034]aw_cali_parse_dt: cali check :disable
[   15.294440][  T512] [Awinic][1-0034]aw_cali_parse_dt: cali function in use
[   15.304056][  T496] [NXP-P61] :  Entry : p61_dev_init
[   15.304549][  T484] msm_cam_smmu soc:qcom,cam_smmu:msm_cam_smmu_cpas_cdm: Adding to iommu group 49
[   15.305289][  T484] msm_cam_smmu soc:qcom,cam_smmu:msm_cam_smmu_lrme: Adding to iommu group 50
[   15.309431][  T512] [Awinic][1-0034]aw_cali_attr_init: enter
[   15.309851][  T430] gcc-yupik 100000.clock-controller: sync-state
[   15.310391][  T459] wlan_hdd_state wlan major(488) initialized
[   15.310480][  T430] ICC interconnect state synced
[   15.313957][  T475] [CHSC] function = semi_touch_start_up_check     , line = 495 : retry-4, firmware is not ready
[   15.320756][  T496] [NXP-P61] :  p61_probe chip select : 0 , bus number = 1 
[   15.326983][  T512] [Awinic][1-0034]aw_cali_parse_re_dt: re min: 4000, re max: 30000
[   15.331374][  T430] cam_cc-yupik ad00000.clock-controller: sync-state
[   15.332060][  T484] CAM_INFO: CAM-CRM: cam_req_mgr_component_master_bind: 871 All probes done, binding slave components
[   15.332307][  T484] synx: registered bind ops type 0 for cam_sync
[   15.332377][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam-sync (ops cam_sync_component_ops [camera])
[   15.332474][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu (ops cam_smmu_component_ops [camera])
[   15.332544][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu:msm_cam_smmu_ife (ops cam_smmu_cb_component_ops [camera])
[   15.332607][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu:msm_cam_smmu_jpeg (ops cam_smmu_cb_component_ops [camera])
[   15.332656][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu:msm_cam_icp_fw (ops cam_smmu_fw_dev_component_ops [camera])
[   15.332684][  T484] CAM_INFO: CAM-SMMU: cam_smmu_get_memory_regions_info: 4026 [icp] : Discard region specified [0xdff00000 0xe0200000] in [0x10c00000 0xfef00000]
[   15.332753][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu:msm_cam_smmu_icp (ops cam_smmu_cb_component_ops [camera])
[   15.332815][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu:msm_cam_smmu_cpas_cdm (ops cam_smmu_cb_component_ops [camera])
[   15.332868][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu:msm_cam_smmu_secure (ops cam_smmu_cb_component_ops [camera])
[   15.332948][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam_smmu:msm_cam_smmu_lrme (ops cam_smmu_cb_component_ops [camera])
[   15.333617][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 519 feature 0x4 addr 0x7801fc, mask 0x10, shift 0x4 type 0x0 hw_map 0x4
[   15.333625][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 542 fuse_addr 0x7801fc, fuse_val 70000
[   15.333627][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 602 feature 0x4 enable=1 hw_map=0x4
[   15.333635][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 519 feature 0x6 addr 0x7801fc, mask 0x20, shift 0x5 type 0x0 hw_map 0x10
[   15.333641][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 602 feature 0x6 enable=1 hw_map=0x10
[   15.333647][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 519 feature 0x7 addr 0x7801fc, mask 0x40, shift 0x6 type 0x0 hw_map 0xff
[   15.333651][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 602 feature 0x7 enable=1 hw_map=0xff
[   15.333657][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 519 feature 0x0 addr 0x7801fc, mask 0x80, shift 0x7 type 0x0 hw_map 0xff
[   15.333662][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 602 feature 0x0 enable=1 hw_map=0xff
[   15.333668][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 519 feature 0x8 addr 0x7801fc, mask 0x100, shift 0x8 type 0x0 hw_map 0x10
[   15.333673][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 602 feature 0x8 enable=1 hw_map=0x10
[   15.333679][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 519 feature 0x9 addr 0x7801a4, mask 0x10000000, shift 0x1c type 0x0 hw_map 0xff
[   15.333683][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 542 fuse_addr 0x7801a4, fuse_val 20000000
[   15.333685][  T484] CAM_INFO: CAM-CPAS: cam_cpas_get_hw_features: 602 feature 0x9 enable=1 hw_map=0xff
[   15.335335][  T484] CAM_INFO: CAM-CPAS: cam_cpas_hw_get_hw_info: 1861 fuse info->num_fuses 2
[   15.335441][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac40000.qcom,cam-cpas (ops cam_cpas_dev_component_ops [camera])
[   15.335714][  T496] p61_parse_dt: errorno = 0
[   15.335789][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam-cdm-intf (ops cam_cdm_intf_component_ops [camera])
[   15.336645][  T475] [CHSC] function = semi_touch_bootup_update_check, line = 550 : check if firmware need update, product vid_pid = 0xa0002101, upd vid_pid = 0x29001e02, boot check = 1
[   15.338263][  T475] [CHSC] function = semi_touch_mode_init          , line = 420 : set status pointing...
[   15.339993][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac48000.qcom,cpas-cdm0 (ops cam_hw_cdm_component_ops [camera])
[   15.340500][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acb3000.qcom,csid0 (ops cam_ife_csid_component_ops [camera])
[   15.340958][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acba000.qcom,csid1 (ops cam_ife_csid_component_ops [camera])
[   15.341425][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acc1000.qcom,csid2 (ops cam_ife_csid_component_ops [camera])
[   15.341746][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acc8000.qcom,csid-lite0 (ops cam_ife_csid_component_ops [camera])
[   15.342047][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound accf000.qcom,csid-lite1 (ops cam_ife_csid_component_ops [camera])
[   15.343754][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acaf000.qcom,vfe0 (ops cam_vfe_component_ops [camera])
[   15.345061][  T475] [CHSC] function = semi_touch_resolution_adaption, line = 359 : resolution = (240, 320)
[   15.345068][  T475] [CHSC] function = semi_touch_resolution_adaption, line = 362 : vkey_num = 0
[   15.345528][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acb6000.qcom,vfe1 (ops cam_vfe_component_ops [camera])
[   15.345620][  T475] [CHSC] function = semi_touch_esd_check_prepare  , line = 693 : open esd function...
[   15.345647][  T475] [CHSC] function = semi_touch_work_done          , line = 462 : register drm notify, active = f31b498
[   15.345655][  T475] [CHSC] function = semi_touch_probe              , line = 535 : probe finished(result:0) driver ver(v3.8.1)
[   15.346430][  T512] [Awinic][1-0034]aw882xx_monitor_init: enter
[   15.348425][  T475] modprobe (475) used greatest stack depth: 10704 bytes left
[   15.353858][  T496] [NXP-P61] :  Entry : p61_hw_setup
[   15.353865][  T496] [NXP-P61] :  Exit : p61_hw_setup
[   15.360508][  T512] [Awinic][1-0034]aw_parse_spin_dts: spin-mode get failed, spin switch off, spin_mode:0
[   15.369192][  T496] [NXP-P61] :  p61_probe: device tree set 'nxp,sn1x0-i2c' as eSE power controller
[   15.369271][  T496] [NXP-P61] :  Exit : p61_probe
[   15.380110][  T512] [Awinic][1-0034]aw882xx_spin_init: aw_spin_kcontrol_st:0
[   15.431687][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acbd000.qcom,vfe2 (ops cam_vfe_component_ops [camera])
[   15.440367][    T7] sd 0:0:0:4: [sde] Synchronizing SCSI cache
[   15.440386][  T512] gpio gpiochip0: (f000000.pinctrl): allocate IRQ 309, hwirq 11
[   15.440392][  T512] gpio gpiochip0: (f000000.pinctrl): found parent hwirq 93
[   15.440403][  T512] gpio gpiochip0: (f000000.pinctrl): alloc_irqs_parent for 309 parent hwirq 93
[   15.440707][  T512] aw882xx_i2c_probe, aw_componet_codec_register =========
[   15.440711][  T512] aw_componet_codec_register, occhi------------Enter----------------
[   15.440713][  T512] aw_componet_codec_register, occhi------------memcpy----------------
[   15.440720][  T512] [Awinic][1-0034]aw882xx_dai_drv_append_suffix: dai name [aw882xx-aif-1-34]
[   15.440722][  T512] [Awinic][1-0034]aw882xx_dai_drv_append_suffix: pstream_name name [Speaker_Playback-1-34]
[   15.440724][  T512] [Awinic][1-0034]aw882xx_dai_drv_append_suffix: cstream_name name [Speaker_Capture-1-34]
[   15.440726][  T512] aw_componet_codec_register, occhi------------register_codec----------------
[   15.440733][  T512] [Awinic][1-0034]aw_componet_codec_register: success to register aw882xx: 0
[   15.440735][  T512] [Awinic][1-0034]aw882xx_i2c_probe: codec register success
[   15.440757][  T512] [Awinic][1-0034]aw882xx_i2c_probe: dev_cnt 1
[   15.448204][  T512] modprobe (512) used greatest stack depth: 10576 bytes left
[   15.455416][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound acc4000.qcom,vfe-lite0 (ops cam_vfe_component_ops [camera])
[   15.461440][   T33] modprobe (475): highest shadow stack usage: 456 bytes
[   15.470323][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound accb000.qcom,vfe-lite1 (ops cam_vfe_component_ops [camera])
[   15.895139][   T37] sd 0:0:0:1: [sdb] Synchronizing SCSI cache
[   15.904488][  T484] CAM_ERR: CAM-ISP: cam_vfe_hw_init: 228 inval param
[   16.261997][  T484] CAM_ERR: CAM-ISP: cam_vfe_hw_init: 228 inval param
[   16.268728][  T484] CAM_ERR: CAM-ISP: cam_ife_hw_mgr_init: 8497 no valid IFE TPG HW
[   16.276952][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam-isp (ops cam_isp_dev_component_ops [camera])
[   16.287939][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam-res-mgr (ops cam_res_mgr_component_ops [camera])
[   16.301125][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4a000.qcom,cci0 (ops cam_cci_component_ops [camera])
[   16.313417][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4b000.qcom,cci1 (ops cam_cci_component_ops [camera])
[   16.326274][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ace0000.qcom,csiphy0 (ops cam_csiphy_component_ops [camera])
[   16.340837][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ace2000.qcom,csiphy1 (ops cam_csiphy_component_ops [camera])
[   16.353520][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ace4000.qcom,csiphy2 (ops cam_csiphy_component_ops [camera])
[   16.365952][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ace6000.qcom,csiphy3 (ops cam_csiphy_component_ops [camera])
[   16.377779][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ace8000.qcom,csiphy4 (ops cam_csiphy_component_ops [camera])
[   16.390957][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4a000.qcom,cci0:qcom,cam-sensor0 (ops cam_sensor_component_ops [camera])
[   16.404197][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4a000.qcom,cci0:qcom,cam-sensor2 (ops cam_sensor_component_ops [camera])
[   16.417274][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4a000.qcom,cci0:qcom,cam-sensor3 (ops cam_sensor_component_ops [camera])
[   16.430302][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4b000.qcom,cci1:qcom,cam-sensor1 (ops cam_sensor_component_ops [camera])
[   16.443332][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4a000.qcom,cci0:qcom,eeprom0 (ops cam_eeprom_component_ops [camera])
[   16.456028][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4b000.qcom,cci1:qcom,eeprom1 (ops cam_eeprom_component_ops [camera])
[   16.468487][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac00000.qcom,a5 (ops cam_a5_component_ops [camera])
[   16.479154][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac87000.qcom,ipe0 (ops cam_ipe_component_ops [camera])
[   16.490082][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac6f000.qcom,bps (ops cam_bps_component_ops [camera])
[   16.502614][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam-icp (ops cam_icp_component_ops [camera])
[   16.513549][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac4e000.qcom,jpegenc (ops cam_jpeg_enc_component_ops [camera])
[   16.525197][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac52000.qcom,jpegdma (ops cam_jpeg_dma_component_ops [camera])
[   16.537743][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam-jpeg (ops cam_jpeg_dev_component_ops [camera])
[   16.548985][  T484] cam_lrme_hw ac6b000.qcom,lrme: ac6b000.qcom,lrme supply camss-vdd not found, using dummy regulator
[   16.561703][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound ac6b000.qcom,lrme (ops cam_lrme_hw_dev_component_ops [camera])
[   16.574122][  T484] cam_req_mgr soc:qcom,cam-req-mgr: bound soc:qcom,cam-lrme (ops cam_lrme_component_ops [camera])
[   16.590449][  T484] CAM_INFO: CAM-UTIL: camera_init: 293 Spectra camera driver initialized
[   16.602009][  T484] modprobe (484) used greatest stack depth: 7808 bytes left
[   16.633353][   T58] modprobe (484): highest shadow stack usage: 504 bytes
[   16.674009][  T402] init: wait for '/sys/devices/soc0/soc_id' took 0ms
[   16.681228][   T65] audit: type=1400 audit(27.535:6): avc:  denied  { mounton } for  pid=1 comm="init" path="/sys/kernel/tracing" dev="tracefs" ino=1 scontext=u:r:init:s0 tcontext=u:object_r:debugfs_tracing_debug:s0 tclass=dir permissive=0
[   16.812046][    T1] Registered swp emulation handler
[   16.849760][  T576] logd.auditd: start
[   16.853653][  T576] logd.klogd: 16844717857
[   16.866333][  T576] logd: Loaded bug_map file: /system_ext/etc/selinux/bug_map
[   16.874132][  T576] logd: Loaded bug_map file: /vendor/etc/selinux/selinux_denial_metadata
[   16.882886][  T576] logd: Ignored bug_map definition in /system/etc/selinux/bug_map: 'dnsmasq netd fifo_file b/77868789', (scontext, tcontext, tclass) denial combination is already tagged with bug metadata 'b/77868789'
[   16.902848][  T576] logd: Ignored bug_map definition in /system/etc/selinux/bug_map: 'dnsmasq netd unix_stream_socket b/77868789', (scontext, tcontext, tclass) denial combination is already tagged with bug metadata 'b/77868789'
[   16.923566][  T576] logd: Ignored bug_map definition in /system/etc/selinux/bug_map: 'gmscore_app system_data_file dir b/146166941', (scontext, tcontext, tclass) denial combination is already tagged with bug metadata 'b/146166941'
[   16.944546][  T576] logd: Ignored bug_map definition in /system/etc/selinux/bug_map: 'init app_data_file file b/77873135', (scontext, tcontext, tclass) denial combination is already tagged with bug metadata 'b/77873135'
[   16.964537][  T576] logd: Ignored bug_map definition in /system/etc/selinux/bug_map: 'init cache_file blk_file b/77873135', (scontext, tcontext, tclass) denial combination is already tagged with bug metadata 'b/77873135'
[   16.984614][  T576] logd: Ignored bug_map definition in /system/etc/selinux/bug_map: 'init logpersist file b/77873135', (scontext, tcontext, tclass) denial combination is already tagged with bug metadata 'b/77873135'
console:/ $ [   17.226935][  T402] init: wait for '/dev/block/platform/soc/1d84000.ufshc' took 0ms
[   17.373310][  T402] init: wait for '/dev/qseecom' took 0ms
[   17.400830][    T1] EXT4-fs (sda2): Ignoring removed nomblk_io_submit option
[   17.416462][    T1] EXT4-fs (sda2): recovery complete
[   17.422008][    T1] EXT4-fs (sda2): mounted filesystem with ordered data mode. Opts: errors=remount-ro,nomblk_io_submit
[   17.450224][    T1] e2fsck: e2fsck 1.46.2 (28-Feb-2021)
[   17.455663][    T1] e2fsck: Pass 1: Checking inodes, blocks, and sizes
[   17.462411][    T1] e2fsck: Pass 2: Checking directory structure
[   17.468611][    T1] e2fsck: Pass 3: Checking directory connectivity
[   17.475081][    T1] e2fsck: Pass 4: Checking reference counts
[   17.480995][    T1] e2fsck: Pass 5: Checking group summary information
[   17.487718][    T1] e2fsck: /dev/block/bootdevice/by-name/persist: 291/8192 files (0.3% non-contiguous), 1644/8192 blocks
[   17.502869][    T1] EXT4-fs (sda2): mounted filesystem with ordered data mode. Opts: barrier=1
[   17.515974][    T1] EXT4-fs (sde9): mounted filesystem with ordered data mode. Opts: barrier=1
[   17.534911][    T1] fsck_msdos: ** /dev/block/bootdevice/by-name/qmcs
[   17.541611][    T1] fsck_msdos: ** Phase 1 - Read FAT and checking connectivity
[   17.549146][    T1] fsck_msdos: ** Phase 2 - Checking Directories
[   17.557236][    T1] FAT-fs (sde23): Volume was not properly unmounted. Some data may be corrupt. Please run fsck.
[   17.746809][  T634] fsck.f2fs: Info: Fix the reported corruption.
[   17.753160][  T634] fsck.f2fs: Info: not exist /proc/version!
[   17.759088][  T634] fsck.f2fs: Info: Segments per section = 1
[   17.765012][  T634] fsck.f2fs: Info: Sections per zone = 1
[   17.770671][  T634] fsck.f2fs: Info: sector size = 4096
[   17.776074][  T634] fsck.f2fs: Info: total sectors = 22765011 (88925 MB)
[   17.782976][  T634] fsck.f2fs: Info: MKFS version
[   17.787835][  T634] fsck.f2fs:   "6.8.0-57-generic #59-Ubuntu SMP PREEMPT_DYNAMIC Sat Mar 15 17:40:59 UTC 2025"
[   17.798296][  T634] fsck.f2fs: Info: FSCK version
[   17.803164][  T634] fsck.f2fs:   from "5.4.278-qgki-debug"
[   17.810473][  T634] F2FS-fs (dm-10): Using encoding defined by superblock: utf8-12.1.0 with flags 0x0
[   17.870623][  T634] F2FS-fs (dm-10): recover_inode: ino = 149a, name = <encrypted>, inline = 21
[   17.880396][  T634] F2FS-fs (dm-10): recover_dentry: ino = 149a, name = <encrypted>, dir = d1, err = 0
[   17.889992][  T634] F2FS-fs (dm-10): recover_data: ino = 149a (i_size: recover) recovered = 2, err = 0
[   17.899581][  T634] F2FS-fs (dm-10): recover_inode: ino = 149c, name = <encrypted>, inline = 21
[   17.908538][  T634] F2FS-fs (dm-10): recover_dentry: ino = 149c, name = <encrypted>, dir = d1, err = 0
[   17.918119][  T634] F2FS-fs (dm-10): recover_data: ino = 149c (i_size: recover) recovered = 2, err = 0
[   17.927680][  T634] F2FS-fs (dm-10): recover_inode: ino = 14a0, name = <encrypted>, inline = 21
[   17.936642][  T634] F2FS-fs (dm-10): recover_dentry: ino = 14a0, name = <encrypted>, dir = d1, err = 0
[   17.946212][  T634] F2FS-fs (dm-10): recover_data: ino = 14a0 (i_size: recover) recovered = 2, err = 0
[   17.955785][  T634] F2FS-fs (dm-10): recover_inode: ino = 14a1, name = <encrypted>, inline = 21
[   17.964744][  T634] F2FS-fs (dm-10): recover_dentry: ino = 14a1, name = <encrypted>, dir = d1, err = 0
[   17.974339][  T634] F2FS-fs (dm-10): recover_data: ino = 14a1 (i_size: recover) recovered = 2, err = 0
[   17.983907][  T634] F2FS-fs (dm-10): recover_inode: ino = 14a2, name = <encrypted>, inline = 21
[   17.992864][  T634] F2FS-fs (dm-10): recover_dentry: ino = 14a2, name = <encrypted>, dir = d1, err = 0
[   18.002450][  T634] F2FS-fs (dm-10): recover_data: ino = 14a2 (i_size: recover) recovered = 2, err = 0
[   18.014270][  T634] F2FS-fs (dm-10): checkpoint: version = 43d21485
[   18.029269][  T634] F2FS-fs (dm-10): Mounted with checkpoint version = 43d21485
[   18.100686][    T1] fscrypt: AES-256-CTS-CBC using implementation "cts-cbc-aes-ce"
[   18.142745][  T634] vold: keystore2 Keystore earlyBootEnded returned service specific error: -68
[   18.174088][  T696] apexd: This device does not support updatable APEX. Exiting
[   18.181664][  T696] apexd: Marking APEXd as activated
[   18.388501][  T707] apexd: This device does not support updatable APEX. Exiting
[   18.396050][  T707] apexd: Marking APEXd as ready
[   18.460505][  T402] init: Top-level directory needs encryption action, eg mkdir /data/vendor <mode> <uid> <gid> encryption=Require
[   18.472777][  T402] init: Verified that /data/vendor has the encryption policy 9294129d211e8f33eef001070ebb2500 v2 modes 1/4 flags 0xa
[   18.522878][  T719] tsnv nv_initialize successfully!
[   18.568042][  T735] LibBpfLoader: Section bpfloader_min_ver value is 2 [0x2]
[   18.575351][  T735] LibBpfLoader: Section bpfloader_max_ver value is 65536 [0x10000]
[   18.583352][  T735] LibBpfLoader: Section size_of_bpf_map_def value is 116 [0x74]
[   18.591105][  T735] LibBpfLoader: Section size_of_bpf_prog_def value is 92 [0x5c]
[   18.598803][  T735] LibBpfLoader: BpfLoader version 0x00013 processing ELF object /apex/com.android.tethering/etc/bpf/test.o with ver [0x00002,0x10000)
[   18.782319][  T735] printk: bpfloader: 1610 output lines suppressed due to ratelimiting
[   18.790795][    T1] init: Service 'bpfloader' (pid 735) exited with status 0 waiting took 0.251000 seconds
[   18.800724][    T1] init: Sending signal 9 to service 'bpfloader' (pid 735) process group...
[   18.809519][    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 735 in 0ms
[   18.818807][    T1] init: processing action (ro.crypto.state=encrypted && ro.crypto.type=file && zygote-start) from (/system/etc/init/hw/init.rc:1063)
[   18.832699][    T1] init: start_waiting_for_property("odsign.verification.done", "1"): already set
[   18.842136][    T1] init: starting service 'update_verifier_nonencrypted'...
[   18.851843][    T1] init: SVC_EXEC service 'update_verifier_nonencrypted' pid 762 (uid 0 gid 2001+1 context default) started; waiting...
[   18.861868][  T762] update_verifier: Started with arg 1: nonencrypted
[   18.872565][  T762] update_verifier: Booting slot 0: isSlotMarkedSuccessful=1
[   18.879920][  T762] update_verifier: Leaving update_verifier.
[   18.886818][    T1] init: Service 'update_verifier_nonencrypted' (pid 762) exited with status 0 waiting took 0.036000 seconds
[   18.898437][    T1] init: Sending signal 9 to service 'update_verifier_nonencrypted' (pid 762) process group...
[   18.908905][    T1] libprocessgroup: Successfully killed process cgroup uid 0 pid 762 in 0ms
[   18.933933][  T351] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.mdt failed with error -2
[   18.934016][  T402] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.mdt failed with error -2
[   18.944214][  T351] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.mdt
[   18.954223][  T402] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.mdt
[   18.958702][  T402] subsys-pil-tz a300000.qcom,turing: cdsp: loading from 0x0000000088f00000 to 0x000000008ad00000
[   18.967095][  T351] subsys-pil-tz 3700000.qcom,lpass: adsp: loading from 0x0000000086700000 to 0x0000000088f00000
[   18.993990][   T56] cfg80211: failed to load regulatory.db
[   19.002854][   T68] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b02 failed with error -2
[   19.003020][  T185] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b03 failed with error -2
[   19.013541][   T68] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b02
[   19.023290][  T185] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b03
[   19.023455][  T775] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b04 failed with error -2
[   19.024186][  T776] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b05 failed with error -2
[   19.024196][  T776] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b05
[   19.024669][  T777] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b06 failed with error -2
[   19.024676][  T777] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b06
[   19.024908][  T778] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b07 failed with error -2
[   19.024915][  T778] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b07
[   19.025506][  T779] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b08 failed with error -2
[   19.025514][  T779] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b08
[   19.026828][  T781] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b09 failed with error -2
[   19.026837][  T781] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b09
[   19.027212][  T780] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b10 failed with error -2
[   19.027220][  T780] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b10
[   19.027309][  T782] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b11 failed with error -2
[   19.027314][  T782] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b11
[   19.028134][  T783] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b12 failed with error -2
[   19.028142][  T783] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b12
[   19.028495][  T785] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b13 failed with error -2
[   19.028615][  T785] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b13
[   19.029000][  T784] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b14 failed with error -2
[   19.029008][  T784] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b14
[   19.029418][  T786] subsys-pil-tz a300000.qcom,turing: Direct firmware load for cdsp.b15 failed with error -2
[   19.029421][  T786] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b15
[   19.034866][  T787] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b02 failed with error -2
[   19.037795][  T776] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b03 failed with error -2
[   19.037798][  T776] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b03
[   19.041996][  T775] subsys-pil-tz a300000.qcom,turing: Falling back to sysfs fallback for: cdsp.b04
[   19.044062][  T789] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b04 failed with error -2
[   19.044071][  T789] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b04
[   19.044575][  T792] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b05 failed with error -2
[   19.044583][  T792] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b05
[   19.046246][  T793] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b06 failed with error -2
[   19.046254][  T793] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b06
[   19.047334][  T794] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b07 failed with error -2
[   19.047342][  T794] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b07
[   19.047762][  T777] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b08 failed with error -2
[   19.047770][  T777] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b08
[   19.050913][  T185] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b09 failed with error -2
[   19.050916][  T185] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b09
[   19.051209][  T797] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b10 failed with error -2
[   19.051212][  T797] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b10
[   19.051661][  T795] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b11 failed with error -2
[   19.051664][  T795] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b11
[   19.052046][  T798] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b12 failed with error -2
[   19.052049][  T798] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b12
[   19.052084][  T787] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b02
[   19.053100][  T778] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b13 failed with error -2
[   19.059885][  T779] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b14 failed with error -2
[   19.059888][  T779] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b14
[   19.071966][  T781] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b15 failed with error -2
[   19.073262][  T799] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b16 failed with error -2
[   19.073271][  T799] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b16
[   19.073504][  T803] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b17 failed with error -2
[   19.073507][  T803] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b17
[   19.074557][  T804] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b18 failed with error -2
[   19.074560][  T804] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b18
[   19.075186][  T805] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b19 failed with error -2
[   19.075189][  T805] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b19
[   19.076068][  T806] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b20 failed with error -2
[   19.076071][  T806] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b20
[   19.076591][  T807] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b21 failed with error -2
[   19.076594][  T807] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b21
[   19.077187][  T809] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b22 failed with error -2
[   19.077195][  T809] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b22
[   19.077608][  T810] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b23 failed with error -2
[   19.077610][  T810] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b23
[   19.077770][  T811] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b24 failed with error -2
[   19.077773][  T811] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b24
[   19.077921][  T812] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b25 failed with error -2
[   19.077924][  T812] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b25
[   19.078063][  T813] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b26 failed with error -2
[   19.078066][  T813] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b26
[   19.078417][  T814] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b27 failed with error -2
[   19.078420][  T814] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b27
[   19.079236][  T815] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b28 failed with error -2
[   19.079239][  T815] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b28
[   19.080009][  T816] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b29 failed with error -2
[   19.080206][  T816] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b29
[   19.080457][  T818] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b30 failed with error -2
[   19.080460][  T818] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b30
[   19.080826][  T817] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b31 failed with error -2
[   19.080829][  T817] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b31
[   19.081341][  T819] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b32 failed with error -2
[   19.081349][  T819] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b32
[   19.081559][  T820] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b33 failed with error -2
[   19.081567][  T820] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b33
[   19.081802][  T821] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b34 failed with error -2
[   19.081809][  T821] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b34
[   19.081866][  T778] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b13
[   19.082195][  T822] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b35 failed with error -2
[   19.082203][  T822] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b35
[   19.082271][  T823] subsys-pil-tz 3700000.qcom,lpass: Direct firmware load for adsp.b36 failed with error -2
[   19.082275][  T823] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b36
[   19.091167][  T781] subsys-pil-tz 3700000.qcom,lpass: Falling back to sysfs fallback for: adsp.b15
[   19.125995][  T844] ueventd: firmware: loading 'adsp.b16' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b16'
[   19.127724][  T845] ueventd: firmware: loading 'adsp.b17' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b17'
[   19.130091][  T846] ueventd: firmware: loading 'adsp.b18' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b18'
[   19.132202][  T845] ueventd: loading /devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b17 took 4ms
[   19.132226][  T847] ueventd: firmware: loading 'adsp.b19' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b19'
[   19.134111][  T848] ueventd: firmware: loading 'adsp.b20' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b20'
[   19.136042][  T849] ueventd: firmware: loading 'adsp.b21' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b21'
[   19.137842][  T850] ueventd: firmware: loading 'adsp.b22' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b22'
[   19.139744][  T851] ueventd: firmware: loading 'adsp.b23' for '/devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b23'
[   19.141387][  T851] ueventd: loading /devices/platform/soc/3700000.qcom,lpass/firmware/adsp.b23 took 1ms
[   19.154058][  T402] subsys-pil-tz a300000.qcom,turing: cdsp: Brought out of reset
[   19.165607][  T179] subsys-pil-tz a300000.qcom,turing: cdsp: Power/Clock ready interrupt received
[   19.169312][  T402] subsys-pil-tz: subsys_powerup(): pil_boot is successful from cdsp and waiting for error ready
[   19.275291][  T351] subsys-pil-tz 3700000.qcom,lpass: adsp: Brought out of reset
[   19.277007][  T179] subsys-pil-tz: subsys_err_ready_intr_handler(): Subsystem error monitoring/handling services are up fromcdsp
[   19.286958][  T351] subsys-pil-tz: subsys_powerup(): pil_boot is successful from adsp and waiting for error ready
[   19.296716][  T402] adsprpc: fastrpc_restart_notifier_cb: cdsp subsystem is up
[   19.300329][  T202] qcom_smd_qrtr_probe:Entered
[   19.300883][  T202] qcom_smd_qrtr_probe:SMD QRTR driver probed
[   19.303435][  T816] sysmon-qmi: ssctl_new_server: Connection established between QMI handle and cdsp's SSCTL service
[   19.303450][  T816] coresight-remote-etm soc:turing_etm0: Connection established between QMI handle and 13 service
[   19.305910][  T202] Info: adsprpc (5392): kworker/0:1H: fastrpc_rpmsg_probe: opened rpmsg channel for cdsp
[   19.325008][  T178] subsys-pil-tz: subsys_err_ready_intr_handler(): Subsystem error monitoring/handling services are up fromadsp
[   19.325021][  T178] subsys-pil-tz 3700000.qcom,lpass: adsp: Power/Clock ready interrupt received
[   19.325315][  T207] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   19.335763][  T351] adsprpc: fastrpc_restart_notifier_cb: adsp subsystem is up
[   19.344439][  T207] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[   19.345852][  T202] Info: adsprpc (5392): kworker/0:1H: fastrpc_rpmsg_probe: opened rpmsg channel for adsp
[   19.346825][  T202] apr_tal_rpmsg soc:adsp.apr_audio_svc.-1.-1: apr_tal_rpmsg_probe: Channel[apr_audio_svc] state[Up]
[   19.349833][  T202] qcom_smd_qrtr_probe:Entered
[   19.350476][  T202] qcom_smd_qrtr_probe:SMD QRTR driver probed
[   19.353663][  T816] sysmon-qmi: ssctl_new_server: Connection established between QMI handle and adsp's SSCTL service
[   19.353876][  T816] coresight-remote-etm soc:audio_etm0: Connection established between QMI handle and 5 service
[   19.465515][   T37] binder: undelivered transaction 540, process died.
[   19.470294][  T207] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   19.651624][  T351] subsys-pil-tz soc:qcom,wpss@8a00000: Direct firmware load for wpss.mdt failed with error -2
[   19.653992][  T207] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[   19.664615][  T351] subsys-pil-tz soc:qcom,wpss@8a00000: Falling back to sysfs fallback for: wpss.mdt
[   19.673282][  T207] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   20.314956][  T207] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[   20.315066][  T351] subsys-pil-tz soc:qcom,wpss@8a00000: wpss: loading from 0x000000009ae00000 to 0x000000009c700000
[   20.342404][  T816] subsys-pil-tz soc:qcom,wpss@8a00000: Direct firmware load for wpss.b02 failed with error -2
[   20.342409][  T781] subsys-pil-tz soc:qcom,wpss@8a00000: Direct firmware load for wpss.b03 failed with error -2
[   20.345232][  T778] subsys-pil-tz soc:qcom,wpss@8a00000: Direct firmware load for wpss.b04 failed with error -2
[   20.345236][  T778] subsys-pil-tz soc:qcom,wpss@8a00000: Falling back to sysfs fallback for: wpss.b04
[   20.352865][  T823] subsys-pil-tz soc:qcom,wpss@8a00000: Direct firmware load for wpss.b05 failed with error -2
[   20.352915][  T822] subsys-pil-tz soc:qcom,wpss@8a00000: Direct firmware load for wpss.b06 failed with error -2
[   20.352918][  T822] subsys-pil-tz soc:qcom,wpss@8a00000: Falling back to sysfs fallback for: wpss.b06
[   20.352975][  T816] subsys-pil-tz soc:qcom,wpss@8a00000: Falling back to sysfs fallback for: wpss.b02
[   20.353029][  T820] subsys-pil-tz soc:qcom,wpss@8a00000: Direct firmware load for wpss.b07 failed with error -2
[   20.353032][  T820] subsys-pil-tz soc:qcom,wpss@8a00000: Falling back to sysfs fallback for: wpss.b07
[   20.363255][  T781] subsys-pil-tz soc:qcom,wpss@8a00000: Falling back to sysfs fallback for: wpss.b03
[   20.373558][  T823] subsys-pil-tz soc:qcom,wpss@8a00000: Falling back to sysfs fallback for: wpss.b05
[   20.648744][  T822] servloc: service_locator_new_server: Connection established with the Service locator
[   20.648768][   T62] servloc: init_service_locator: Service locator initialized
[   20.667377][  T129] adsprpc: fastrpc_get_service_location_notify: PDR notifier for adsp registered for msm/adsp/sensor_pd (sensors_pdr_adsprpc)
[   20.667447][  T781] service-notifier: service_notifier_new_server: Connection established between QMI handle and 74 service
[   20.685338][  T140] adsprpc: fastrpc_get_service_location_notify: PDR notifier for adsp registered for msm/adsp/audio_pd (audio_pdr_adsprpc)
[   20.685386][   T31] audio_notifier_reg_service: service PDR_ADSP is in use
[   20.685406][  T822] service-notifier: service_notifier_new_server: Connection established between QMI handle and 74 service
[   20.705373][  T325] adsprpc: fastrpc_pdr_notifier_cb: msm/adsp/audio_pd (audio_pdr_adsprpc) is up on adsp
[   20.733624][  T325] apr_adsp_up: Q6 is Up
[   20.741723][  T234] adsprpc: fastrpc_pdr_notifier_cb: msm/adsp/sensor_pd (sensors_pdr_adsprpc) is up on adsp
[   20.742417][    T7] msm-audio-ion soc:qcom,msm-audio-apr:qcom,msm-audio-ion: Adding to iommu group 51
[   20.753335][   T64] sps_bam_enable: sps:BAM 0x0000000003a84000 (va:0xffffffc01aca0000) enabled: ver:0x19, number of pipes:15
[   20.753494][ T1098] rmt_storage:INFO:check_support_using_libmdm: Modem subsystem found on target!
[   20.756803][ T1098] rmt_storage:INFO:main: Done with init now waiting for messages!
[   20.905544][    T7] qcom-lpi-pinctrl soc:qcom,msm-audio-apr:qcom,q6core-audio:lpi_pinctrl@33c0000: snd_event_notify: No snd dev entry found
[   20.936704][  T338] msm_cdc_pinctrl_get_gpiodata: device node is null
[   20.959667][  T338] msm_cdc_pinctrl_get_gpiodata: device node is null
[   20.973549][ T1048] nfc_i2c_dev_read: f_flags has nonblock. try again
[   21.065069][ T1014] Reading ADC channel pm7325_quiet_therm timed out
[   21.609218][ T1014] Reading ADC channel pm7325_quiet_therm timed out
[   22.147091][  T234] aw_cali_get_read_cali_re:channel:0 open /mnt/vendor/persist/factory/audio/aw_cali.bin failed!
[   22.174058][  T234] msm-dai-fe soc:qcom,msm-dai-fe: ASoC: no sink widget found for AUXPCM_DL_HL
[   22.183541][  T234] msm-dai-fe soc:qcom,msm-dai-fe: ASoC: Failed to add route AUXPCM_HOSTLESS Playback -> direct -> AUXPCM_DL_HL
[   22.195560][  T234] msm-dai-fe soc:qcom,msm-dai-fe: ASoC: no source widget found for AUXPCM_UL_HL
[   22.204856][  T234] msm-dai-fe soc:qcom,msm-dai-fe: ASoC: Failed to add route AUXPCM_UL_HL -> direct -> AUXPCM_HOSTLESS Capture
[   22.601857][  T234] bolero-codec soc:qcom,msm-audio-apr:qcom,q6core-audio:bolero-cdc: ASoC: unknown pin WSA_SPK1 OUT
[   22.612814][  T234] bolero-codec soc:qcom,msm-audio-apr:qcom,q6core-audio:bolero-cdc: ASoC: unknown pin WSA_SPK2 OUT
[   22.623726][  T234] bolero-codec soc:qcom,msm-audio-apr:qcom,q6core-audio:bolero-cdc: ASoC: unknown pin WSA AIF VI
[   22.634535][  T234] bolero-codec soc:qcom,msm-audio-apr:qcom,q6core-audio:bolero-cdc: ASoC: unknown pin VIINPUT_WSA
[   23.686261][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   23.695628][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   23.704617][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   23.713989][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   23.723012][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   23.732278][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   23.741347][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   23.750688][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   23.759932][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   23.769292][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   23.778323][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   23.787762][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   23.801384][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_HDMI) ctld (0) stream (0)
[   23.805576][    T1] init: Control message: Could not find 'aidl/SurfaceFlingerAIDL' for ctl.interface_start from pid: 578 (/system/bin/servicemanager)
[   23.811996][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   23.836239][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   23.846648][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   23.857442][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   23.867758][  T906] msm-ext-disp-audio-codec-rx soc:qcom,msm-ext-disp:qcom,msm-ext-disp-audio-codec-rx: msm_ext_disp_audio_type_get: cable_status() or get_intf_id is NULL
[   23.883801][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   23.894123][  T906] msm-ext-disp-audio-codec-rx soc:qcom,msm-ext-disp:qcom,msm-ext-disp-audio-codec-rx: msm_ext_disp_audio_type_get: cable_status() or get_intf_id is NULL
[   23.910165][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_HDMI) ctld (0) stream (0)
[   23.920656][  T906] msm-ext-disp-audio-codec-rx soc:qcom,msm-ext-disp:qcom,msm-ext-disp-audio-codec-rx: msm_ext_disp_audio_type_get: cable_status() or get_intf_id is NULL
[   23.970029][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   23.978844][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.003200][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.012089][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.025988][  T906] msm_pcm_volume_ctl_get substream not found
[   24.032219][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.041158][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   24.063001][    T1] init: Control message: Could not find 'android.hardware.health@2.1::IHealth/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   24.068877][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.087665][  T393] [CHSC] function = semi_touch_reset              , line = 14  : set status before reset tp...
[   24.087839][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.128373][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.137257][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.164773][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.173634][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.213015][  T393] [CHSC] function = semi_touch_reset_and_detect   , line = 463 : set status pointing...
[   24.222912][  T393] [CHSC] function = semi_touch_resume_entry       , line = 625 : tpd_resume...
[   24.244326][  T906] msm_pcm_volume_ctl_get substream not found
[   24.250608][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.256434][    T1] init: Control message: Could not find 'android.frameworks.cameraservice.service@2.1::ICameraService/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   24.259479][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   24.290117][  T906] msm_pcm_volume_ctl_get substream not found
[   24.296419][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.305299][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   24.318109][  T906] msm_pcm_volume_ctl_get substream not found
[   24.324426][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   24.333341][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   25.357823][ T1617] smcinvoke: process_tzcb_req: server is defunct, state= 1 tzhandle = -2147483627
[   25.367245][ T1617] smcinvoke: process_tzcb_req: server invalid, res: -90
[   25.394614][    C0] qrtr: Modem QMI Readiness RX cmd:0x2 node[0x0]
[   25.548818][  T202] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   25.568753][  T202] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[   25.592848][  T202] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   25.603121][  T202] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[   25.611300][  T202] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   25.619308][  T202] bcl_pmic5:bcl_read_register Error reading register 0x4708 err:-5
[   25.718982][  T451] msm-dwc3 a600000.ssusb: Could not get usb psy
[   25.841413][   T69] msm-dwc3 a600000.ssusb: Could not get usb psy
[   25.897094][ T1835] Reading ADC channel pm7325_quiet_therm timed out
[   25.920513][  T451] msm-dwc3 a600000.ssusb: Could not get usb psy
[   26.158352][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   26.167698][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   26.176728][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   26.186034][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   26.194945][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   26.204181][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   26.213226][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   26.222515][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   26.231555][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   26.240776][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   26.249745][  T906] msm_voice_source_tracking_get: Error getting Source Tracking Params, err=-22
[   26.258972][  T906] msm_voice_source_tracking_get: Error getting FNN ST Params, err=-22
[   26.272127][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_HDMI) ctld (0) stream (0)
[   26.282719][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   26.293095][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   26.303477][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   26.314134][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   26.324440][  T906] msm-ext-disp-audio-codec-rx soc:qcom,msm-ext-disp:qcom,msm-ext-disp-audio-codec-rx: msm_ext_disp_audio_type_get: cable_status() or get_intf_id is NULL
[   26.340430][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_DP) ctld (0) stream (0)
[   26.350731][  T906] msm-ext-disp-audio-codec-rx soc:qcom,msm-ext-disp:qcom,msm-ext-disp-audio-codec-rx: msm_ext_disp_audio_type_get: cable_status() or get_intf_id is NULL
[   26.366720][  T906] msm_ext_disp_update_audio_ops: Display not found (EXT_DISPLAY_TYPE_HDMI) ctld (0) stream (0)
[   26.377195][  T906] msm-ext-disp-audio-codec-rx soc:qcom,msm-ext-disp:qcom,msm-ext-disp-audio-codec-rx: msm_ext_disp_audio_type_get: cable_status() or get_intf_id is NULL
[   26.409077][ T1835] Reading ADC channel pm7325_quiet_therm timed out
[   26.431110][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.440031][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.464267][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.473267][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.486889][  T906] msm_pcm_volume_ctl_get substream not found
[   26.493150][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.501935][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   26.528330][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.537161][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.566845][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.575734][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.602905][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.611724][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.680619][  T906] msm_pcm_volume_ctl_get substream not found
[   26.686854][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.695705][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   26.708656][  T906] msm_pcm_volume_ctl_get substream not found
[   26.714955][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.723902][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   26.736693][  T906] msm_pcm_volume_ctl_get substream not found
[   26.742987][  T906] msm_pcm_soft_volume_ctl_get substream runtime or private_data not found
[   26.751836][  T906] msm_pcm_soft_volume_ctl_get substream not found
[   28.644502][  T315] icnss2_qmi: DMS QMI connection not established
[   28.691430][  T315] [kworke][0x2c2976d9][21:41:30.511349] wlan: [315:E:TXRX] hif_print_hal_shadow_register_cfg: num_config 30
[   28.703232][  T315] icnss2_qmi: DMS QMI connection not established
[   29.049789][  T315] wlan_pld:pld_is_ipa_offload_disabled:3268:: Not supported on type 6
[   29.058420][  T315] [kworke][0x2c94fb28][21:41:30.878339] wlan: [315:E:QDF] cds_get_context: Module ID 66 context is Null
[   29.076219][  T315] [kworke][0x2c9a31eb][21:41:30.896136] wlan: [315:E:QDF] cds_get_context: Module ID 66 context is Null
[   29.090959][  T315] [kworke][0x2c9e8356][21:41:30.910875] wlan: [315:F:WMA] WMA --> wmi_unified_attach - success
[   29.112361][  T315] [kworke][0x2ca4c874][21:41:30.932277] wlan: [315:E:QDF] htc_wait_target: Target Ready! TX resource : 1 size:2176, MaxMsgsPerHTCBundle = 1
[   29.126901][  T315] [kworke][0x2ca90b3f][21:41:30.946821] wlan: [315:E:QDF] htc_setup_target_buffer_assignments: SVS Index : 1 TX : 0x100 : alloc:1
[   29.140534][  T315] [kworke][0x2cad09bd][21:41:30.960454] wlan: [315:E:DP] dp_srng_get_str_from_hal_ring_type: Invalid ring type
[   29.162405][  T315] [kworke][0x2cb371ee][21:41:30.982323] wlan: [315:E:DP] dp_rx_fst_attach: FST setup params FT size 128, hash_mask 0x7f, skid_length 2
[   29.232843][  T374] [kworke][0x2cc814c9][21:41:31.052761] wlan: [374:E:QDF] qdf_mem_malloc_debug: Cannot malloc 0 bytes @ wma_rx_service_ready_event:6022
[   29.247003][  T374] [kworke][0x2ccc3acc][21:41:31.066921] wlan: [374:E:QDF] qdf_mem_malloc_debug: Cannot malloc 0 bytes @ policy_mgr_init_dbs_hw_mode:771
[   29.261689][  T374] [kworke][0x2cd08856][21:41:31.081608] wlan: [374:E:WMA] wma_update_supported_bands: wrong supported band
[   29.273438][  T374] [kworke][0x2cd3f976][21:41:31.093357] wlan: [374:E:TIF] init_deinit_populate_twt_cap_ext2: Extraction of twt capability failed
[   29.287081][  T374] [kworke][0x2cd7f8ba][21:41:31.107000] wlan: [374:E:QDF] copy_fw_abi_version_tlv: copy_fw_abi_version_tlv: INIT_CMD version: 1, 0, 0x5f414351, 0x4c4d, 0x0, 0x0
[   29.410946][  T374] [kworke][0x2cfc4283][21:41:31.230864] wlan: [374:E:QDF] ready_extract_init_status_tlv: ready_extract_init_status_tlv:0
[   29.423837][  T374] [kworke][0x2d000964][21:41:31.243756] wlan: [374:E:QDF] cds_get_context: Module ID 65 context is Null
[   29.435348][  T374] [kworke][0x2d03688d][21:41:31.255265] wlan: [374:F:DP] cdp_ipa_set_uc_tx_partition_base invalid instance
[   29.449170][ T1014] Reading ADC channel pm7325_quiet_therm timed out
[   29.455971][  T374] [kworke][0x2d09737b][21:41:31.275890] wlan: [374:E:QDF] dp_peer_map_attach_wifi3: dp_peer_map_attach_wifi3 max_peers 24, max_ast_index: 80
[   29.455971][  T374] 
[   29.474551][  T315] [kworke][0x2d0ee4d1][21:41:31.294468] wlan: [315:E:WMI] send_action_oui_cmd_tlv: Invalid action id
[   29.485788][  T315] [kworke][0x2d122fbe][21:41:31.305707] wlan: [315:E:QDF] qdf_mem_malloc_debug: Cannot malloc 0 bytes @ hdd_init_channel_avoidance:12022
[   29.500572][  T315] [kworke][0x2d168487][21:41:31.320491] wlan: [315:E:TXRX] dp_rxdma_ring_config: DBS disabled, max_mac_rings 1
[   29.512519][  T315] [kworke][0x2d1a0484][21:41:31.332438] wlan: [315:E:TXRX] dp_rxdma_ring_config: pdev_id 0 max_mac_rings 1
[   29.524133][  T315] [kworke][0x2d1d6b8c][21:41:31.344051] wlan: [315:E:TXRX] dp_rxdma_ring_config: mac_id 0
[   29.535669][  T315] [kworke][0x2d20cca0][21:41:31.355586] wlan: [315:E:DP] dp_rx_flow_send_fst_fw_setup: FST params after CMEM update FT size 128, hash_mask 0x7f
[   29.552771][  T315] [kworke][0x2d25cee1][21:41:31.372682] wlan: [315:E:REGULATORY] reg_freq_width_to_chan_op_class: no op class for frequency 5825
[   29.585289][  T315] cnss_utils: WLAN MAC address is not set, type 0
[   30.045759][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.122245][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.141354][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.158126][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.201957][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.220449][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.275359][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.299520][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.330058][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.388331][    T1] init: Control message: Could not find 'vendor.qti.hardware.iop@2.0::IIop/default' for ctl.interface_start from pid: 579 (/system/bin/hwservicemanager)
[   30.801070][ T2803] bt_ioctl: BT chip state is already: 0 no change
[   33.314632][ T3395] audit: rate limit exceeded
[   33.546117][ T3179] Reading ADC channel pm7325_quiet_therm timed out
[   33.869838][ T3696] aw_cali_get_read_cali_re:channel:0 open /mnt/vendor/persist/factory/audio/aw_cali.bin failed!
[   33.921526][ T3696] msm_adsp_init_mixer_ctl_adm_pp_event_queue: failed to get kctl.
[   33.933467][ T3696] send_afe_cal_type: No cal sent for cal_index 0, port_id = 0x1006! ret -22
[   33.952703][ T3696] q6asm_find_cal_by_buf_number: Can't find ASM Cal for cal_index 2 app_type 69937 buffer_number 8
[   34.000834][   T67] [Awinic]aw_check_dsp_ready: rx topo id is 0x0
[   34.061127][ T3338] Reading ADC channel pm7325_quiet_therm timed out
[   34.280098][ T1048] nfc_i2c_dev_read: f_flags has nonblock. try again
[   34.311092][ T3851] i2c_geni a84000.i2c: i2c error :-107
[   34.569941][ T3179] Reading ADC channel pm7325_quiet_therm timed out
[   35.081124][ T3338] Reading ADC channel pm7325_quiet_therm timed out
[   35.486004][ T3851] i2c_geni a84000.i2c: i2c error :-107
[   35.657141][ T3179] Reading ADC channel pm7325_quiet_therm timed out
[   37.866650][ T3851] i2c_geni a84000.i2c: i2c error :-107
[   38.998133][ T3685] msm_adsp_clean_mixer_ctl_adm_pp_event_queue: failed to get kctl.
[   40.555209][   T69] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   40.563074][   T69] qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
[   40.571256][   T69] qpnp_vibrator_play_on: vibration enable failed, ret=-5
[   40.690048][ T2144] [schedu][0x39e4ae96][21:41:42.509959] wlan: [2144:E:SYS] Processing SYS MC STOP
[   40.935276][   T69] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   40.943174][   T69] qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
[   40.951504][   T69] qpnp_vibrator_play_on: vibration enable failed, ret=-5
[   45.781977][    T7] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   45.790181][    T7] qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
[   45.801329][    T7] qpnp_vibrator_play_on: vibration enable failed, ret=-5
[   46.252038][   T70] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   46.261610][   T70] qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
[   46.271265][   T70] qpnp_vibrator_play_on: vibration enable failed, ret=-5
[   47.420172][   T64] spmi spmi-0: pmic_arb_wait_for_done: transaction failed (0x3)
[   47.428518][   T64] qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
[   47.437116][   T64] qpnp_vibrator_play_on: vibration enable failed, ret=-5
[   75.631803][  T178] subsys-pil-tz: subsys_err_fatal_intr_handler(): Fatal error on adsp!
[   75.640417][  T178] subsys-pil-tz: log_failure_reason(): adsp subsystem failure reason: err_qdi.c:1122:EX:sensor_process:0x3:SNS_SEE_I_0:0x10000014:PC=0xb216b320:LR=0xb216a1e8.
[   75.763882][   T64] Kernel panic - not syncing: subsys-restart: Resetting the SoC - adsp crashed.
[   75.773012][   T64] CPU: 1 PID: 64 Comm: kworker/1:1 Tainted: G S         O      5.4.278-qgki-debug #1
[   75.782562][   T64] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   75.790344][   T64] Workqueue: events device_restart_work_hdlr
[   75.796336][   T64] Call trace:
[   75.799569][   T64]  dump_backtrace.cfi_jt+0x0/0x4
[   75.804496][   T64]  show_stack+0x18/0x24
[   75.808620][   T64]  dump_stack+0xf0/0x150
[   75.812839][   T64]  panic+0x1b4/0x424
[   75.816699][   T64]  subsys_parse_devicetree+0x0/0x344
[   75.821981][   T64]  process_one_work+0x2cc/0x558
[   75.826812][   T64]  worker_thread+0x2ac/0x508
[   75.831385][   T64]  kthread+0x160/0x170
[   75.835423][   T64]  ret_from_fork+0x10/0x18
[   75.839817][   T64] SMP: stopping secondary CPUs
[   75.844589][    C0] CPU0: stopping
[   75.848105][    C0] CPU: 0 PID: 0 Comm: swapper/0 Tainted: G S         O      5.4.278-qgki-debug #1
[   75.857385][    C0] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   75.865155][    C0] pstate: 20c00005 (nzCv daif +PAN +UAO)
[   75.870797][    C0] pc : lpm_cpuidle_enter+0x4b8/0x4f4
[   75.876072][    C0] lr : lpm_cpuidle_enter+0x4b0/0x4f4
[   75.881357][    C0] sp : ffffffef99263df0
[   75.885480][    C0] x29: ffffffef99263e00 x28: ffffffef98e3b028 
[   75.891646][    C0] x27: ffffffef992786b8 x26: ffffffef97d85a18 
[   75.897822][    C0] x25: ffffff8ceb1c3c40 x24: 0000000000000000 
[   75.903989][    C0] x23: ffffffef99370d00 x22: ffffff8d13b3a880 
[   75.910159][    C0] x21: ffffff8d13b3af50 x20: ffffff8d13b37080 
[   75.916325][    C0] x19: 0000000000000000 x18: ffffffef99373048 
[   75.922491][    C0] x17: 0000000005f5e100 x16: 0000000000000001 
[   75.928667][    C0] x15: 0000000000000022 x14: ffffff8d0b7a5080 
[   75.934839][    C0] x13: 0000000000008d7e x12: 0000000000000001 
[   75.941006][    C0] x11: f74df9690a991f00 x10: f74df9690a991f00 
[   75.947179][    C0] x9 : ffffffef97572424 x8 : 00000000000000e0 
[   75.953355][    C0] x7 : 0000000000000000 x6 : ffffff8d0b88338c 
[   75.959521][    C0] x5 : 0000000000000000 x4 : 0000000000000002 
[   75.965687][    C0] x3 : ffffffef99263d50 x2 : ffffffef97d72fb8 
[   75.971854][    C0] x1 : ffffffef99263d50 x0 : ffffffef97572424 
[   75.978023][    C0] CPU: 0 PID: 0 Comm: swapper/0 Tainted: G S         O      5.4.278-qgki-debug #1
[   75.987301][    C0] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   75.995069][    C0] Call trace:
[   75.998302][    C0]  dump_backtrace.cfi_jt+0x0/0x4
[   76.003228][    C0]  show_stack+0x18/0x24
[   76.007350][    C0]  dump_stack+0xf0/0x150
[   76.011566][    C0]  local_cpu_stop+0xb8/0xc4
[   76.016048][    C0]  trace_ipi_entry_rcuidle+0x0/0x14c
[   76.021335][    C0]  gic_handle_irq+0x148/0x168
[   76.025987][    C0]  el1_irq+0x108/0x200
[   76.030020][    C0]  lpm_cpuidle_enter+0x4b8/0x4f4
[   76.034946][    C0]  cpuidle_enter_state+0x11c/0x2e8
[   76.040048][    C0]  cpuidle_enter+0x38/0x50
[   76.044444][    C0]  cpuidle_idle_call+0x144/0x2c8
[   76.049368][    C0]  do_idle.llvm.15809742245808813570+0xb4/0x110
[   76.055623][    C0]  cpu_startup_entry+0x24/0x28
[   76.060370][    C0]  kernel_init+0x0/0x2a0
[   76.064586][    C0]  start_kernel+0x0/0x400
[   76.068889][    C0]  start_kernel+0x388/0x400
[   76.073374][    C3] CPU3: stopping
[   76.076882][    C3] CPU: 3 PID: 805 Comm: kworker/u17:25 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.086792][    C3] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.094566][    C3] Workqueue: dwc_wq dwc3_bh_work.cfi_jt
[   76.100109][    C3] pstate: 20c00005 (nzCv daif +PAN +UAO)
[   76.105748][    C3] pc : _raw_spin_unlock_irqrestore+0x38/0x68
[   76.111743][    C3] lr : _raw_spin_unlock_irqrestore+0x34/0x68
[   76.117736][    C3] sp : ffffffc01e3ebd20
[   76.121859][    C3] x29: ffffffc01e3ebd20 x28: ffffffef97d7e3c8 
[   76.128034][    C3] x27: ffffff8cef9731c0 x26: ffffff8cfd590000 
[   76.134200][    C3] x25: 0000000010307405 x24: ffffff8d1034e080 
[   76.140366][    C3] x23: ffffff8d1034e1e8 x22: 0000000000000000 
[   76.146533][    C3] x21: ffffff8d1034e1d0 x20: 00000011a7df22ed 
[   76.152699][    C3] x19: 0000000000000000 x18: ffffffc01e3dd028 
[   76.158865][    C3] x17: 00002c3029706b20 x16: 0000000000000000 
[   76.165030][    C3] x15: 0000000000000061 x14: ffffff8d08738100 
[   76.171206][    C3] x13: 0000000000000004 x12: 0000000000000001 
[   76.177370][    C3] x11: f74df9690a991f00 x10: f74df9690a991f00 
[   76.183535][    C3] x9 : ffffffef97320ee8 x8 : 0000000100000201 
[   76.189700][    C3] x7 : 0000000000000000 x6 : ffffff8d090c66e0 
[   76.195876][    C3] x5 : 0000000000000000 x4 : 0000000000000204 
[   76.202041][    C3] x3 : ffffffc01e3ebc80 x2 : ffffffef97d72fb8 
[   76.208207][    C3] x1 : ffffffc01e3ebc80 x0 : ffffffef97320ee8 
[   76.214384][    C3] CPU: 3 PID: 805 Comm: kworker/u17:25 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.224293][    C3] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.232061][    C3] Workqueue: dwc_wq dwc3_bh_work.cfi_jt
[   76.237604][    C3] Call trace:
[   76.240835][    C3]  dump_backtrace.cfi_jt+0x0/0x4
[   76.245759][    C3]  show_stack+0x18/0x24
[   76.249878][    C3]  dump_stack+0xf0/0x150
[   76.254092][    C3]  local_cpu_stop+0xb8/0xc4
[   76.258574][    C3]  trace_ipi_entry_rcuidle+0x0/0x14c
[   76.263860][    C3]  gic_handle_irq+0x148/0x168
[   76.268521][    C3]  el1_irq+0x108/0x200
[   76.272554][    C3]  _raw_spin_unlock_irqrestore+0x38/0x68
[   76.278191][    C3]  dwc3_bh_work+0x74/0x100
[   76.282584][    C3]  process_one_work+0x2cc/0x558
[   76.287418][    C3]  worker_thread+0x2ac/0x508
[   76.291990][    C3]  kthread+0x160/0x170
[   76.296023][    C3]  ret_from_fork+0x10/0x18
[   76.300418][    C2] CPU2: stopping
[   76.303925][    C2] CPU: 2 PID: 0 Comm: swapper/2 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.313204][    C2] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.320972][    C2] pstate: 20c00005 (nzCv daif +PAN +UAO)
[   76.326610][    C2] pc : lpm_cpuidle_enter+0x4b8/0x4f4
[   76.331894][    C2] lr : lpm_cpuidle_enter+0x4b0/0x4f4
[   76.337179][    C2] sp : ffffffc010223e30
[   76.341302][    C2] x29: ffffffc010223e40 x28: ffffffef98e3b028 
[   76.347479][    C2] x27: ffffffef992786b8 x26: ffffffef97d85a18 
[   76.353655][    C2] x25: ffffff8ceb1c3c40 x24: 0000000000000000 
[   76.359831][    C2] x23: ffffff8c5ff79080 x22: ffffff8d13b3a880 
[   76.365996][    C2] x21: ffffff8d13b3af50 x20: ffffff8d13b37080 
[   76.372162][    C2] x19: 0000000000000001 x18: ffffffc0100e5038 
[   76.378328][    C2] x17: 0000000005f5e100 x16: 0000000000000001 
[   76.384504][    C2] x15: 0000000000000022 x14: ffffff8d096b0580 
[   76.390680][    C2] x13: 0000000000008d7e x12: 0000000000000001 
[   76.396846][    C2] x11: f74df9690a991f00 x10: f74df9690a991f00 
[   76.403022][    C2] x9 : ffffffef97572424 x8 : 00000000000000e0 
[   76.409187][    C2] x7 : 0000000000000000 x6 : 0000000000300000 
[   76.415363][    C2] x5 : 0000000000000000 x4 : 0000000000000001 
[   76.421539][    C2] x3 : ffffffc010223d90 x2 : ffffffef97d72fb8 
[   76.427703][    C2] x1 : ffffffc010223d90 x0 : ffffffef97572424 
[   76.433880][    C2] CPU: 2 PID: 0 Comm: swapper/2 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.443159][    C2] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.450927][    C2] Call trace:
[   76.454158][    C2]  dump_backtrace.cfi_jt+0x0/0x4
[   76.459082][    C2]  show_stack+0x18/0x24
[   76.463204][    C2]  dump_stack+0xf0/0x150
[   76.467417][    C2]  local_cpu_stop+0xb8/0xc4
[   76.471901][    C2]  trace_ipi_entry_rcuidle+0x0/0x14c
[   76.477183][    C2]  gic_handle_irq+0x148/0x168
[   76.481836][    C2]  el1_irq+0x108/0x200
[   76.485869][    C2]  lpm_cpuidle_enter+0x4b8/0x4f4
[   76.490794][    C2]  cpuidle_enter_state+0x11c/0x2e8
[   76.495898][    C2]  cpuidle_enter+0x38/0x50
[   76.500292][    C2]  cpuidle_idle_call+0x144/0x2c8
[   76.505214][    C2]  do_idle.llvm.15809742245808813570+0xb4/0x110
[   76.511470][    C2]  cpu_startup_entry+0x24/0x28
[   76.516213][    C2]  __cpu_disable+0x0/0xb4
[   76.520518][    C7] CPU7: stopping
[   76.524025][    C7] CPU: 7 PID: 0 Comm: swapper/7 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.533312][    C7] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.541086][    C7] pstate: 20c00005 (nzCv daif +PAN +UAO)
[   76.546726][    C7] pc : lpm_cpuidle_enter+0x4b8/0x4f4
[   76.552010][    C7] lr : lpm_cpuidle_enter+0x4b0/0x4f4
[   76.557291][    C7] sp : ffffffc01024be30
[   76.561412][    C7] x29: ffffffc01024be40 x28: ffffffef98e3b028 
[   76.567582][    C7] x27: ffffffef992786b8 x26: ffffffef97d85a18 
[   76.573750][    C7] x25: ffffff8ceb1c3c40 x24: 0000000000000000 
[   76.579918][    C7] x23: ffffff8c5ff7d280 x22: ffffff8d13b3a880 
[   76.586087][    C7] x21: ffffff8d13b3af50 x20: ffffff8d13b31c80 
[   76.592256][    C7] x19: 0000000000000002 x18: ffffffc0100fb038 
[   76.598424][    C7] x17: 0000000000000001 x16: 0000000000000000 
[   76.604593][    C7] x15: 0000000000000022 x14: ffffff8d085a0400 
[   76.610762][    C7] x13: 0000000000008d7e x12: 0000000000000001 
[   76.616931][    C7] x11: f74df9690a991f00 x10: f74df9690a991f00 
[   76.623101][    C7] x9 : ffffffef97572424 x8 : 00000000000000e0 
[   76.629270][    C7] x7 : 0000000000000000 x6 : 0000000000300000 
[   76.635439][    C7] x5 : 0000000000000000 x4 : 0000000000000001 
[   76.641608][    C7] x3 : ffffffc01024bd90 x2 : ffffffef97d72fb8 
[   76.647777][    C7] x1 : ffffffc01024bd90 x0 : ffffffef97572424 
[   76.653948][    C7] CPU: 7 PID: 0 Comm: swapper/7 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.663233][    C7] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.671006][    C7] Call trace:
[   76.674242][    C7]  dump_backtrace.cfi_jt+0x0/0x4
[   76.679173][    C7]  show_stack+0x18/0x24
[   76.683296][    C7]  dump_stack+0xf0/0x150
[   76.687509][    C7]  local_cpu_stop+0xb8/0xc4
[   76.691983][    C7]  trace_ipi_entry_rcuidle+0x0/0x14c
[   76.697267][    C7]  gic_handle_irq+0x148/0x168
[   76.701924][    C7]  el1_irq+0x108/0x200
[   76.705956][    C7]  lpm_cpuidle_enter+0x4b8/0x4f4
[   76.710876][    C7]  cpuidle_enter_state+0x11c/0x2e8
[   76.715976][    C7]  cpuidle_enter+0x38/0x50
[   76.720371][    C7]  cpuidle_idle_call+0x144/0x2c8
[   76.725301][    C7]  do_idle.llvm.15809742245808813570+0xb4/0x110
[   76.731560][    C7]  cpu_startup_entry+0x24/0x28
[   76.736308][    C7]  __cpu_disable+0x0/0xb4
[   76.740615][    C4] CPU4: stopping
[   76.744128][    C4] CPU: 4 PID: 0 Comm: swapper/4 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.753409][    C4] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.761184][    C4] pstate: 20c00005 (nzCv daif +PAN +UAO)
[   76.766822][    C4] pc : lpm_cpuidle_enter+0x4b8/0x4f4
[   76.772101][    C4] lr : lpm_cpuidle_enter+0x4b0/0x4f4
[   76.777379][    C4] sp : ffffffc010233e30
[   76.781507][    C4] x29: ffffffc010233e40 x28: ffffffef98e3b028 
[   76.787676][    C4] x27: ffffffef992786b8 x26: ffffffef97d85a18 
[   76.793847][    C4] x25: ffffff8ceb1c3c40 x24: 0000000000000000 
[   76.800013][    C4] x23: ffffff8c5ff7e300 x22: ffffff8d13b3a880 
[   76.806181][    C4] x21: ffffff8d13b3af50 x20: ffffff8d13b31c80 
[   76.812347][    C4] x19: 0000000000000002 x18: ffffffc0100f5038 
[   76.818516][    C4] x17: 0000000000000001 x16: 0000000000000000 
[   76.824684][    C4] x15: 0000000000000022 x14: ffffff8d05c63300 
[   76.830853][    C4] x13: 0000000000008d80 x12: 0000000000000001 
[   76.837021][    C4] x11: f74df9690a991f00 x10: f74df9690a991f00 
[   76.843191][    C4] x9 : ffffffef97572424 x8 : 00000000000000e0 
[   76.849359][    C4] x7 : 0000000000000000 x6 : 0000000000300000 
[   76.855533][    C4] x5 : 0000000000000000 x4 : 0000000000000001 
[   76.861700][    C4] x3 : ffffffc010233d90 x2 : ffffffef97d72fb8 
[   76.867868][    C4] x1 : ffffffc010233d90 x0 : ffffffef97572424 
[   76.874039][    C4] CPU: 4 PID: 0 Comm: swapper/4 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.883323][    C4] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.886264][   T64] SMP: failed to stop secondary CPUs 1,4-6
[   76.891098][    C4] Call trace:
[   76.900144][    C4]  dump_backtrace.cfi_jt+0x0/0x4
[   76.905072][    C4]  show_stack+0x18/0x24
[   76.909196][    C4]  dump_stack+0xf0/0x150
[   76.913413][    C4]  local_cpu_stop+0xb8/0xc4
[   76.917898][    C4]  trace_ipi_entry_rcuidle+0x0/0x14c
[   76.923178][    C4]  gic_handle_irq+0x148/0x168
[   76.927835][    C4]  el1_irq+0x108/0x200
[   76.931869][    C4]  lpm_cpuidle_enter+0x4b8/0x4f4
[   76.936797][    C4]  cpuidle_enter_state+0x11c/0x2e8
[   76.941896][    C4]  cpuidle_enter+0x38/0x50
[   76.946282][    C4]  cpuidle_idle_call+0x144/0x2c8
[   76.951210][    C4]  do_idle.llvm.15809742245808813570+0xb4/0x110
[   76.957467][    C4]  cpu_startup_entry+0x24/0x28
[   76.962209][    C4]  __cpu_disable+0x0/0xb4
[   76.966517][    C5] CPU5: stopping
[   76.970027][    C5] CPU: 5 PID: 0 Comm: swapper/5 Tainted: G S         O      5.4.278-qgki-debug #1
[   76.979309][    C5] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   76.987081][    C5] pstate: 20c00005 (nzCv daif +PAN +UAO)
[   76.992731][    C5] pc : lpm_cpuidle_enter+0x4b8/0x4f4
[   76.998011][    C5] lr : lpm_cpuidle_enter+0x4b0/0x4f4
[   77.003286][    C5] sp : ffffffc01023be30
[   77.007409][    C5] x29: ffffffc01023be40 x28: ffffffef98e3b028 
[   77.013580][    C5] x27: ffffffef992786b8 x26: ffffffef97d85a18 
[   77.019745][    C5] x25: ffffff8ceb1c3c40 x24: 0000000000000000 
[   77.025911][    C5] x23: ffffff8c5ff7a100 x22: ffffff8d13b3a880 
[   77.032080][    C5] x21: ffffff8d13b3af50 x20: ffffff8d13b31c80 
[   77.038245][    C5] x19: 0000000000000002 x18: ffffffc0100f7038 
[   77.044421][    C5] x17: ffffff8d13b31d24 x16: 0000000000002edd 
[   77.050589][    C5] x15: 0000000000000022 x14: ffffff8d09b14d80 
[   77.056753][    C5] x13: 0000000000008d80 x12: 0000000000000001 
[   77.062920][    C5] x11: f74df9690a991f00 x10: f74df9690a991f00 
[   77.069088][    C5] x9 : ffffffef97572424 x8 : 00000000000000e0 
[   77.075256][    C5] x7 : 0000000000000000 x6 : 0000000000300000 
[   77.081422][    C5] x5 : 0000000000000000 x4 : 0000000000000001 
[   77.087590][    C5] x3 : ffffffc01023bd90 x2 : ffffffef97d72fb8 
[   77.093755][    C5] x1 : ffffffc01023bd90 x0 : ffffffef97572424 
[   77.099923][    C5] CPU: 5 PID: 0 Comm: swapper/5 Tainted: G S         O      5.4.278-qgki-debug #1
[   77.109201][    C5] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   77.116971][    C5] Call trace:
[   77.120205][    C5]  dump_backtrace.cfi_jt+0x0/0x4
[   77.125129][    C5]  show_stack+0x18/0x24
[   77.129257][    C5]  dump_stack+0xf0/0x150
[   77.133467][    C5]  local_cpu_stop+0xb8/0xc4
[   77.137947][    C5]  trace_ipi_entry_rcuidle+0x0/0x14c
[   77.143233][    C5]  gic_handle_irq+0x148/0x168
[   77.147886][    C5]  el1_irq+0x108/0x200
[   77.151926][    C5]  lpm_cpuidle_enter+0x4b8/0x4f4
[   77.156847][    C5]  cpuidle_enter_state+0x11c/0x2e8
[   77.161951][    C5]  cpuidle_enter+0x38/0x50
[   77.166339][    C5]  cpuidle_idle_call+0x144/0x2c8
[   77.171263][    C5]  do_idle.llvm.15809742245808813570+0xb4/0x110
[   77.177522][    C5]  cpu_startup_entry+0x24/0x28
[   77.182265][    C5]  __cpu_disable+0x0/0xb4
[   77.186569][    C6] CPU6: stopping
[   77.190086][    C6] CPU: 6 PID: 0 Comm: swapper/6 Tainted: G S         O      5.4.278-qgki-debug #1
[   77.199376][    C6] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   77.207148][    C6] pstate: 20c00005 (nzCv daif +PAN +UAO)
[   77.212796][    C6] pc : lpm_cpuidle_enter+0x4b8/0x4f4
[   77.218079][    C6] lr : lpm_cpuidle_enter+0x4b0/0x4f4
[   77.223357][    C6] sp : ffffffc010243e30
[   77.227476][    C6] x29: ffffffc010243e40 x28: ffffffef98e3b028 
[   77.233648][    C6] x27: ffffffef992786b8 x26: ffffffef97d85a18 
[   77.239819][    C6] x25: ffffff8ceb1c3c40 x24: 0000000000000000 
[   77.245989][    C6] x23: ffffff8c5ff78000 x22: ffffff8d13b3a880 
[   77.252160][    C6] x21: ffffff8d13b3af50 x20: ffffff8d13b31c80 
[   77.258328][    C6] x19: 0000000000000002 x18: ffffffc0100f9038 
[   77.264498][    C6] x17: ffffff8d13b31d24 x16: 0000000000000001 
[   77.270670][    C6] x15: 0000000000000022 x14: ffffff8d0855e900 
[   77.276842][    C6] x13: 0000000000008d80 x12: 0000000000000001 
[   77.283014][    C6] x11: f74df9690a991f00 x10: f74df9690a991f00 
[   77.289187][    C6] x9 : ffffffef97572424 x8 : 00000000000000e0 
[   77.295361][    C6] x7 : 0000000000000000 x6 : 0000000000300000 
[   77.301533][    C6] x5 : 0000000000000000 x4 : 0000000000000001 
[   77.307704][    C6] x3 : ffffffc010243d90 x2 : ffffffef97d72fb8 
[   77.313874][    C6] x1 : ffffffc010243d90 x0 : ffffffef97572424 
[   77.320049][    C6] CPU: 6 PID: 0 Comm: swapper/6 Tainted: G S         O      5.4.278-qgki-debug #1
[   77.329336][    C6] Hardware name: Qualcomm Technologies, Inc. YUPIKP-IOT IDP (DT)
[   77.337109][    C6] Call trace:
[   77.340345][    C6]  dump_backtrace.cfi_jt+0x0/0x4
[   77.345271][    C6]  show_stack+0x18/0x24
[   77.349391][    C6]  dump_stack+0xf0/0x150
[   77.353603][    C6]  local_cpu_stop+0xb8/0xc4
[   77.358084][    C6]  trace_ipi_entry_rcuidle+0x0/0x14c
[   77.363368][    C6]  gic_handle_irq+0x148/0x168
[   77.368021][    C6]  el1_irq+0x108/0x200
[   77.372052][    C6]  lpm_cpuidle_enter+0x4b8/0x4f4
[   77.376978][    C6]  cpuidle_enter_state+0x11c/0x2e8
[   77.382084][    C6]  cpuidle_enter+0x38/0x50
[   77.386475][    C6]  cpuidle_idle_call+0x144/0x2c8
[   77.391401][    C6]  do_idle.llvm.15809742245808813570+0xb4/0x110
[   77.397662][    C6]  cpu_startup_entry+0x24/0x28
[   77.402405][    C6]  __cpu_disable+0x0/0xb4
[   79.499783][   T64] Skip md ftrace buffer dump for: 0x1609e0
[   79.505650][   T64] cpufreq_hw_debug: cpufreq_panic_callback: FREQUENCY DOMAIN 0
[   79.513248][   T64] cpufreq_hw_debug: cpufreq_panic_callback:        PERF_STATE_DESIRED: 0x00000009
[   79.522534][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            CYCLE_CNTR_VAL: 0x7f41c997
[   79.531821][   T64] cpufreq_hw_debug: cpufreq_panic_callback:             PSTATE_STATUS: 0x34c00966
[   79.541107][   T64] cpufreq_hw_debug: cpufreq_panic_callback:         EPSS_DEBUG_STATUS: 0x34c10966
[   79.550394][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            EPSS_DEBUG_SRB: 0x00000000
[   79.559681][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            EPSS_DEBUG_LUT: 0x0004000f
[   79.568967][   T64] cpufreq_hw_debug: cpufreq_panic_callback: FREQUENCY DOMAIN 1
[   79.576561][   T64] cpufreq_hw_debug: cpufreq_panic_callback:        PERF_STATE_DESIRED: 0x00000000
[   79.585847][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            CYCLE_CNTR_VAL: 0xded1ab75
[   79.595133][   T64] cpufreq_hw_debug: cpufreq_panic_callback:             PSTATE_STATUS: 0x25c00024
[   79.604418][   T64] cpufreq_hw_debug: cpufreq_panic_callback:         EPSS_DEBUG_STATUS: 0x25c10024
[   79.613704][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            EPSS_DEBUG_SRB: 0x00000001
[   79.622989][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            EPSS_DEBUG_LUT: 0x40040024
[   79.632274][   T64] cpufreq_hw_debug: cpufreq_panic_callback: FREQUENCY DOMAIN 2
[   79.639867][   T64] cpufreq_hw_debug: cpufreq_panic_callback:        PERF_STATE_DESIRED: 0x00000000
[   79.649153][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            CYCLE_CNTR_VAL: 0xc35812ff
[   79.658439][   T64] cpufreq_hw_debug: cpufreq_panic_callback:             PSTATE_STATUS: 0x2640002a
[   79.667724][   T64] cpufreq_hw_debug: cpufreq_panic_callback:         EPSS_DEBUG_STATUS: 0x2641002a
[   79.677010][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            EPSS_DEBUG_SRB: 0x00000001
[   79.686296][   T64] cpufreq_hw_debug: cpufreq_panic_callback:            EPSS_DEBUG_LUT: 0x4004002a
[   79.695589][   T64] kgsl kgsl-3d0: snapshot: device is powered off
[   80.302043][   T64] Kernel Offset: 0x2f86600000 from 0xffffffc010000000
[   80.308838][   T64] PHYS_OFFSET: 0xfffffff480000000
[   80.313850][   T64] CPU features: 0x00030006,2a00a238
[   80.319044][   T64] Memory Limit: 5028 MB
[   80.323181][   T64] Triggering late bite
[   80.327410][   T64] (virq:irq_count)- 3:105098 217:42076 47:20671 57:14630 281:10069 299:8375 263:6728 10:5816 311:4931 220:4602 
[   80.339369][   T64] (cpu:irq_count)- 0:115251 1:33546 2:30047 3:28951 4:6534 5:6647 6:6956 7:8062 
[   80.348573][   T64] (ipi:irq_count)- 0:222392 1:76195 2:7 3:0 4:0 5:57392 6:0 
[   80.355998][   T64] hh-watchdog hypervisor:qcom,hh-watchdog: Causing a QCOM Apps Watchdog bite!
[   80.364937][   T64] hh-watchdog hypervisor:qcom,hh-watchdog: vWdog-CTL: 1, vWdog-time since last pet: 3467, vWdog-expired status: 1
S - Format: Log Type - Time(microsec) - Message - Optional Info
S - Log Type: B - Since Boot(Power On Reset),  D - Delta,  S - Statistic
S - QC_IMAGE_VERSION_STRING=BOOT.MXF.1.0-01055-LAHAINA-1
S - IMAGE_VARIANT_STRING=SocKodiakLAA
S - OEM_IMAGE_VERSION_STRING=f0e2f5e65d22
S - Boot Interface: UFS
S - Secure Boot: Off
S - Boot Config @ 0x00786070 = 0x000000c1
S - JTAG ID @ 0x00786130 = 0x001980e1
S - OEM ID @ 0x00786138 = 0x00000000
S - Serial Number @ 0x00786134 = 0xf7384cf8
S - OEM Config Row 0 @ 0x007841c0 = 0x0000000000000000
S - OEM Config Row 1 @ 0x007841c8 = 0x0000000000000000
S - Feature Config Row 0 @ 0x00784148 = 0x0000000000000000
S - Feature Config Row 1 @ 0x00784150 = 0x0000000000000000
S - Core 0 Frequency, 1516 MHz
S - PBL Patch Ver: 1
D -      6633 - pbl_apps_init_timestamp
D -     31587 - bootable_media_detect_timestamp
D -       993 - bl_elf_metadata_loading_timestamp
D -       705 - bl_hash_seg_auth_timestamp
D -      6809 - bl_elf_loadable_segment_loading_timestamp
D -      4555 - bl_elf_segs_hash_verify_timestamp
D -     17369 - bl_sec_hash_seg_auth_timestamp
D -       822 - bl_sec_segs_hash_verify_timestamp
D -        28 - pbl_populate_shared_data_and_exit_timestamp
S -     69501 - PBL, End
B -     79025 - SBL1, Start
B -    207552 - SBL1 BUILD @ 15:17:26 on Jul 30 2025
B -    211609 - usb: usb_shared_hs_phy_init: hs phy cfg size , 0xc
B -    220759 - usb: eud_serial_upd , 0xf7384cf8
D -    221094 - sbl1_hw_init
B -    395402 - UFS INQUIRY ID: SAMSUNG KM2L9001CM-B518 0700
B -    396957 - UFS Boot LUN: 1
B -    406992 - UFS GEAR: 3
D -    188277 - boot_media_init
D -        30 - smss_load_cancel
B -    416813 - SMSS -  Image Load, Start
D -      3050 - SMSS -  Image Loaded, Delta - (0 Bytes)
D -       915 - Auth Metadata
D -      5643 - sbl1_xblconfig_init
B -    431544 - XBL Config -  Image Load, Start
D -        31 - shrm_load_cancel
B -    439230 - SHRM -  Image Load, Start
D -         0 - boot_default_cdt_init
B -    446184 - Using default CDT
D -      3477 - boot_cdt_init
B -    452742 - CDT -  Image Load, Start
B -    455517 - CDT Version:3,Platform ID:34,Major ID:1,Minor ID:0,Subtype:0
D -      9486 - sbl1_hw_platform_pre_ddr
D -         0 - devcfg init
B -    475708 - PMIC A:2.0 B:1.0 C:2.2
B -    477386 - PM: Reset by PSHOLD
B -    479277 - PM: Reset Type: Warm Reset
B -    482601 - PM: Warm reset count:0x3
B -    486536 - PM: Reset by PSHOLD
B -    490287 - PM: Reset Type: Warm Reset
B -    493581 - PM: Warm reset count:0x2
B -    497516 - PM: Reset by PSHOLD
B -    501267 - PM: Reset Type: Warm Reset
B -    504592 - PM: Warm reset count:0x1
B -    735263 - PM: SET_VAL:Skip
B -    735294 - PM: Verifying PON-Trigger specific configurations & current PON-Trigger
B -    744047 - PM: All PON-Trigger specific configs verified. Proceeding to BOOT
B -    753411 - PM: PSI: b0x06_v0x3f
B -    756217 - PM: Device Init # SPMI Transn: 13583
D -    290879 - pm_device_init, Delta
B -    761920 - pm_driver_init, Start
B -    771985 - PM: Driver Init # SPMI Transn: 442
D -      6588 - pm_driver_init, Delta
B -    776682 - PM: CHG Init # SPMI Transn: 14029
B -    780159 - vsense_init, Start
D -         0 - vsense_init, Delta
D -    330986 - sbl1_hw_pre_ddr_init
D -         0 - boot_dload_handle_forced_dload_timeout
B -    800686 - Pre_DDR_clock_init, Start
D -        92 - Pre_DDR_clock_init, Delta
D -      8906 - sbl1_ddr_set_params
B -    813343 - sbl1_ddr_init, Start
B -    816759 - LP4 DDR detected
B -    820328 - PASS1 Cookie =  0xcafe1111
B -    823134 - HAL_DDR_SDI_Recover() start:  DBG PDC Tr = 0x0, First Pass expire = 0x0, First Pass complete = 0x1 , BIMC Alt ARES  = 0x0 , PASS2 Cookie = 0xcafecab0 
B -    838475 - SHRM run state = 0x1 
B -    842166 - HAL_DDR_SDI_Recover() end : DBG PDC Tr = 0x0, First Pass expire = 0x0, First Pass complete = 0x1 , BIMC Alt ARES  = 0x0 , PASS2 Cookie = 0xcafebabe 
B -    856958 - SHRM run state = 0x1 
D -     43706 - sbl1_ddr_init, Delta
D -     50600 - sbl1_ddr_init
D -        30 - boot_pre_ddi_entry
B -    870043 - do_ddr_training, Start
D -         0 - do_ddr_training, Delta
D -      6832 - sbl1_do_ddr_training
D -         0 - boot_ddi_entry
B -    883798 - Pimem init cmd, entry
D -       915 - Pimem init cmd, exit
B -    890966 - External heap init, Start
B -    893589 - External heap init, End
D -     16409 - sbl1_post_ddr_init
D -        31 - sbl1_hw_init_secondary
B -    904294 - DDR -  Image Load, Start
B -    907924 - usb: ser_str_cookie_pid - d6175429
B -    911706 - usb: fedl, vbus_det_err
D -      8357 - boot_fedl_check
B -    919941 - APDP -  Image Load, Start
D -       884 - Auth Metadata
D -       488 - Segments hash check
D -      9607 - APDP -  Image Loaded, Delta - (7844 Bytes)
D -       610 - boot_dload_dump_security_regions
B -    938210 - TCSR reg value 0x10 
D -      4453 - ramdump_load_cancel
B -    946049 - RamDump -  Image Load, Start
D -       640 - Auth Metadata
D -      2227 - Segments hash check
D -     10919 - RamDump -  Image Loaded, Delta - (350012 Bytes)
B -    965996 - boot_dload_entry
B -    966911 - External heap deinit, Start
B -    969137 - External heap init, End
B -    973163 - External heap init, Start
B -    976854 - External heap init, End
B -   1046607 - usb: init start
B -   1046638 - usb: qusb_dci_platform , 0x22
B -   1049596 - usb: vbus_pm_err , 0x4
B -   1054324 - usb: usb_shared_ss_phy_init: ss phy cfg size , 0x8f
B -   1065121 - usb: ssusb_phy_init_success_lane_A
B -   1065151 - usb: usb_shared_hs_phy_init: hs phy cfg size , 0xc
B -   1075552 - usb: dci, chgr_type_det_err
B -   1076619 - usb: timer_start , 0x4c4b40
B -   1079913 - usb: vbus_det_pm_unavail
B -   1313360 - usb: HIGH , 0x901d
