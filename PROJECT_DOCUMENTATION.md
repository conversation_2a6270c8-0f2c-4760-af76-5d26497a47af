# EventProvider 项目开发文档

## 项目概述

EventProvider 是一个基于 Android 的事件管理系统，负责监控硬件状态、处理运行时错误、管理事件发布和订阅。项目采用 AIDL 接口与 EventBroker 服务通信，实现跨进程的事件传递。

## 项目架构

### 核心组件

1. **EventMonitorService** - 主要的事件监控服务
2. **EventBrokerManager** - 事件代理管理工具类
3. **RuntimeErrorListener** - 运行时错误监听器
4. **DeviceDetectionManager** - 设备检测管理器
5. **DeviceDetectionListener** - 设备检测事件监听器

### 事件分类系统

```java
// 事件分类
EventCategory.TRANSITION = 1          // 状态转换事件
EventCategory.RUNTIME_ERROR = 2       // 运行时错误事件
EventCategory.LED_CONTROL = 12        // LED控制事件
EventCategory.CATEGORY_TOOL_EVENT = 6 // 工具事件分类

// 硬件运行时错误类型
EventType.HardwareRuntimeError.HwErrorCode.CPU_HIGH_TEMPERATURE = 90
EventType.HardwareRuntimeError.HwErrorCode.FRONT_CAMERA_UNAVAILABLE = 91
EventType.HardwareRuntimeError.HwErrorCode.BATTERY_LOW_POWER = 93
EventType.HardwareRuntimeError.HwErrorCode.READ_WRITE_FAIL = 92

// LED控制类型
EventType.LedControl.SDCARD_ERROR = 某个值

// 工具事件类型
EventType.CatergoryTool.START_DETECTION = 0x01
EventType.CatergoryTool.RECEIVED_TEST_RESULTS = 0x02
```

### 硬件ID映射

```java
EventType.HardwareRuntimeError.HwId.MCU = 1
EventType.HardwareRuntimeError.HwId.LED = 2
EventType.HardwareRuntimeError.HwId.G_SENSOR = 3
EventType.HardwareRuntimeError.HwId.GYRO_SENSOR = 4
EventType.HardwareRuntimeError.HwId.FRONT_CAMERA = 5
EventType.HardwareRuntimeError.HwId.INCABIN_CAMERA = 6
EventType.HardwareRuntimeError.HwId.REAR_CAMERA = 7
EventType.HardwareRuntimeError.HwId.OPTION_CAMERA = 8
EventType.HardwareRuntimeError.HwId.SD_CARD = 9
EventType.HardwareRuntimeError.HwId.LTE_MODULE = 10
EventType.HardwareRuntimeError.HwId.GNSS = 11
EventType.HardwareRuntimeError.HwId.BT = 12
EventType.HardwareRuntimeError.HwId.WIFI = 13
EventType.HardwareRuntimeError.HwId.IF_BOX_UNIT = 14
EventType.HardwareRuntimeError.HwId.TEMPERATURE = 15
EventType.HardwareRuntimeError.HwId.VOLTAGE = 16
```

## 新功能开发详情

### 1. EventBrokerManager 工具类

**开发目标**: 提供单例模式的EventBroker服务连接和事件发布功能

**核心特性**:
- 单例模式，全局唯一实例，线程安全
- 自动连接EventBroker服务
- 队列缓存机制（未连接时缓存事件，连接成功后自动发布）
- 连接状态回调接口
- 完全复用EventMonitorService中的连接和发布逻辑

**主要API**:
```java
// 初始化
EventBrokerManager.getInstance().init(context);

// 发布JSON事件
EventBrokerManager.getInstance().publishJsonEvent(category, type, name, content);

// 发布事件（扩展接口）
EventBrokerManager.getInstance().publishEvent(category, type, name, content, payloadType);

// 发布事件（便捷接口）
EventBrokerManager.getInstance().publishEvent(name, content);

// 连接状态管理
EventBrokerManager.getInstance().addCallback(callback);
EventBrokerManager.getInstance().removeCallback(callback);
EventBrokerManager.getInstance().isConnected();
```

**连接状态回调接口**:
```java
public interface EventBrokerCallback {
    void onConnected(ComponentName name, IEventBroker broker);
    void onDisconnected(ComponentName name);
    void onConnectionFailed(String error);
}
```

**支持的JSON格式**:
```json
{
  "errorCode": 5,
  "hwErrorCode": 155,
  "data": {
    "temp": "85.5",
    "state": 1,
    "type": 3
  }
}
```

### 2. SD卡健康检测功能

**开发目标**: 在收到READ_WRITE_FAIL错误时自动检测SD卡健康状态

**实现位置**: RuntimeErrorListener.java

**检测逻辑**:
1. 检查存储权限
2. 获取外部存储目录
3. 创建测试文件并写入"test"内容
4. 读取测试文件内容并验证
5. 自动清理测试文件
6. 返回检测结果

**错误处理**:
- 如果检测失败，发送LED控制事件让LED变色
- 使用JSONObject构建结构化的错误数据
- 包含详细的错误信息和时间戳

**权限要求**:
```xml
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
```

**错误事件格式**:
```json
{
  "errorCode": 2,
  "hwErrorCode": 92,
  "data": {
    "error_type": "sd_card_failed",
    "test_result": "failed",
    "timestamp": 1234567890123,
    "mount_status": "mounted"
  }
}
```

### 3. 自动设备检测系统

**开发目标**: 实现系统启动后自动检测所有硬件设备状态

**系统架构**:
- **DeviceDetectionManager**: 检测管理器，负责流程控制和超时处理
- **DeviceDetectionListener**: 检测事件监听器，处理设备响应

**工作流程**:
1. EventMonitorService连接成功后自动发送检测指令
2. 各模块收到指令后进行自检并发送响应
3. 系统收集所有响应，处理正常和异常状态
4. 1分钟超时机制，未响应设备标记为异常
5. 生成完整的检测报告

**检测指令格式** (category=6, type=1):
```
name: "checkdevice"
payload: "checkdevice"
```

**响应数据格式** (category=6, type=2):

正常响应:
```json
{
  "state": 0,     // 必填：0正常，1异常
  "hwid": 3       // 必填：硬件ID
}
```

异常响应:
```json
{
  "state": 1,           // 必填
  "hwid": 3,            // 必填
  "errorCode": 3,       // 必填（state=1时）
  "hwErrorCode": "013", // 必填（state=1时）
  "data": {             // 非必填
    "state": 1,
    "type": 2,
    "temp": "25.8"
  }
}
```

**字段规范**:
- **state**: int(0正常，1异常) - 必填
- **hwid**: int - 必填
- **errorCode**: int - 如果state等于1必填
- **hwErrorCode**: String - 如果state等于1必填
- **data**: json - 非必填（根据具体业务选择）

**超时处理**:
- 检测超时时间：1分钟
- 未响应的硬件自动标记为异常
- 生成超时错误事件并转发到RUNTIME_ERROR分类

**错误转发**:
异常设备信息会转发到RUNTIME_ERROR分类:
```json
{
  "hwid": 3,
  "errorCode": 3,
  "hwErrorCode": "013",
  "data": {
    "state": 1,
    "type": 2,
    "temp": "25.8"
  }
}
```

## 文件结构

```
app/src/main/java/com/thundercomm/eventprovider/
├── EventMonitorService.java             // 主服务，添加了设备检测启动逻辑
├── MainActivity.java                    // 主活动，添加了SD卡错误测试方法
├── anno/
│   └── EventType.java                   // 事件类型定义，添加了工具事件常量
├── detection/                           // 新增：设备检测模块
│   ├── DeviceDetectionManager.java     // 检测管理器
│   └── DeviceDetectionListener.java    // 检测事件监听器
├── listener/
│   └── RuntimeErrorListener.java       // 运行时错误监听器，添加了SD卡检测
└── utils/
    ├── EventBrokerManager.java         // 新增：事件代理管理工具类
    └── EventBrokerManagerExample.java  // 新增：使用示例
```

## 测试功能

### SD卡错误测试
在MainActivity中提供了测试方法，可以模拟SD卡读写失败：
```java
// 取消注释以启用测试
sendTestSDCardError(eventBroker);
```

### 设备检测测试
系统会在连接成功后自动启动设备检测，无需手动触发。

## 使用示例

### 1. 在Application中初始化EventBrokerManager
```java
public class EventProviderApp extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        EventBrokerManager.getInstance().init(this);
    }
}
```

### 2. 发布事件
```java
// 发布硬件错误事件
EventBrokerManager.getInstance().publishJsonEvent(
    EventCategory.RUNTIME_ERROR,
    EventType.HardwareRuntimeError.HwErrorCode.CPU_HIGH_TEMPERATURE,
    "ThermalEvent",
    "{\"errorCode\":5,\"hwErrorCode\":155,\"data\":{\"temp\":\"85.5\"}}"
);
```

### 3. 监听连接状态
```java
EventBrokerManager.getInstance().addCallback(new EventBrokerManager.EventBrokerCallback() {
    @Override
    public void onConnected(ComponentName name, IEventBroker broker) {
        // 连接成功
    }
    
    @Override
    public void onDisconnected(ComponentName name) {
        // 连接断开
    }
    
    @Override
    public void onConnectionFailed(String error) {
        // 连接失败
    }
});
```

## 开发注意事项

1. **权限管理**: 确保应用具有必要的存储权限
2. **线程安全**: 所有管理器都采用线程安全设计
3. **错误处理**: 完整的异常处理和日志记录
4. **内存管理**: 注意回调的添加和移除，避免内存泄漏
5. **兼容性**: 使用ContextCompat确保不同Android版本的兼容性

## 技术实现细节

### EventBrokerManager 实现细节

**单例模式实现**:
```java
private static volatile EventBrokerManager sInstance;
public static EventBrokerManager getInstance() {
    if (sInstance == null) {
        synchronized (EventBrokerManager.class) {
            if (sInstance == null) {
                sInstance = new EventBrokerManager();
            }
        }
    }
    return sInstance;
}
```

**事件队列缓存机制**:
```java
private final ConcurrentLinkedQueue<PendingJsonEvent> mPendingEvents = new ConcurrentLinkedQueue<>();

// 未连接时缓存事件
if (mIsConnected && mEventBroker != null) {
    executePublishJsonEvent(event);
} else {
    mPendingEvents.offer(event);
    ensureConnection();
}

// 连接成功后处理缓存的事件
private void processPendingOperations() {
    while (!mPendingEvents.isEmpty()) {
        PendingJsonEvent event = mPendingEvents.poll();
        if (event != null) {
            executePublishJsonEvent(event);
        }
    }
}
```

**连接状态回调管理**:
```java
private final ConcurrentLinkedQueue<EventBrokerCallback> mCallbacks = new ConcurrentLinkedQueue<>();

// 线程安全的回调通知
private void notifyConnected(ComponentName name, IEventBroker broker) {
    for (EventBrokerCallback callback : mCallbacks) {
        try {
            callback.onConnected(name, broker);
        } catch (Exception e) {
            EventProviderApp.LOG.w(TAG + ": Error in callback onConnected", e);
        }
    }
}
```

### SD卡检测实现细节

**权限检查机制**:
```java
private boolean hasStoragePermission() {
    return ContextCompat.checkSelfPermission(EventProviderApp.getContext(),
        android.Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
}
```

**读写测试实现**:
```java
private boolean testSDCard() {
    try {
        // 权限检查
        if (!hasStoragePermission()) {
            return false;
        }

        // 获取外部存储目录
        File externalDir = Environment.getExternalStorageDirectory();
        if (externalDir == null || !externalDir.canWrite()) {
            return false;
        }

        // 创建测试文件
        File testFile = new File(externalDir, "test_sdcard.tmp");

        // 写入测试
        FileWriter writer = new FileWriter(testFile);
        writer.write("test");
        writer.close();

        // 读取测试
        FileReader reader = new FileReader(testFile);
        char[] buffer = new char[4];
        int length = reader.read(buffer);
        reader.close();

        // 删除测试文件
        testFile.delete();

        // 验证结果
        return length == 4 && "test".equals(new String(buffer));

    } catch (Exception e) {
        return false;
    }
}
```

### 设备检测系统实现细节

**超时监控机制**:
```java
private Handler mTimeoutHandler = new Handler(Looper.getMainLooper());
private static final long DETECTION_TIMEOUT_MS = 60000; // 1分钟

private void startTimeoutMonitor() {
    mTimeoutRunnable = () -> {
        handleDetectionTimeout();
    };
    mTimeoutHandler.postDelayed(mTimeoutRunnable, DETECTION_TIMEOUT_MS);
}

private void handleDetectionTimeout() {
    Set<Integer> missingHwIds = new HashSet<>(mExpectedHwIds);
    missingHwIds.removeAll(mReceivedHwIds);

    for (int hwId : missingHwIds) {
        createTimeoutErrorEvent(hwId);
    }
}
```

**字段验证实现**:
```java
private boolean validateDetectionResponse(JSONObject jsonObject) {
    try {
        // 检查必填字段
        if (!jsonObject.has("state") || !jsonObject.has("hwid")) {
            return false;
        }

        int state = jsonObject.getInt("state");

        // 如果state=1，检查异常时的必填字段
        if (state == 1) {
            if (!jsonObject.has("errorCode") || !jsonObject.has("hwErrorCode")) {
                return false;
            }
        }

        return true;
    } catch (JSONException e) {
        return false;
    }
}
```

**错误转发机制**:
```java
private void forwardRuntimeError(JSONObject responseJson) {
    try {
        // 提取必填字段
        int hwId = responseJson.getInt("hwid");
        int errorCode = responseJson.getInt("errorCode");
        String hwErrorCode = responseJson.getString("hwErrorCode");

        // 构建转发的JSON
        JSONObject forwardJson = new JSONObject();
        forwardJson.put("hwid", hwId);
        forwardJson.put("errorCode", errorCode);
        forwardJson.put("hwErrorCode", hwErrorCode);

        // 处理可选的data字段
        if (responseJson.has("data")) {
            JSONObject data = responseJson.getJSONObject("data");
            forwardJson.put("data", data);
        }

        // 发送RUNTIME_ERROR事件
        EventBrokerManager.getInstance().publishJsonEvent(
            EventCategory.RUNTIME_ERROR,
            Integer.parseInt(hwErrorCode),
            "device_detection_error",
            forwardJson.toString()
        );

    } catch (JSONException | NumberFormatException e) {
        EventProviderApp.LOG.e(TAG + ": Failed to forward runtime error", e);
    }
}
```

## 性能优化

### 1. 线程安全
- 使用ConcurrentLinkedQueue确保线程安全的队列操作
- 使用ConcurrentHashMap.newKeySet()创建线程安全的Set
- 所有回调通知都包含异常处理

### 2. 内存管理
- 事件队列在连接成功后自动清空
- 回调列表支持动态添加和移除
- 超时任务在检测完成后自动取消

### 3. 错误恢复
- 连接失败时自动重试机制
- 事件发布失败时的队列缓存
- 完整的异常处理和日志记录

## 调试和日志

### 日志级别
- **DEBUG**: 正常流程日志
- **INFO**: 重要状态变更
- **WARN**: 警告信息和异常状态
- **ERROR**: 错误信息和异常

### 关键日志点
1. EventBroker连接状态变化
2. 事件发布成功/失败
3. 设备检测启动/完成/超时
4. SD卡健康检测结果
5. 错误事件转发

## 版本历史

- **v1.0**: 基础事件监控功能
- **v1.1**: 添加EventBrokerManager工具类
- **v1.2**: 添加SD卡健康检测功能
- **v1.3**: 添加自动设备检测系统
