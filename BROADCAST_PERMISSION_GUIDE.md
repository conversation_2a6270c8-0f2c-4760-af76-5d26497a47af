# EventProvider广播权限使用指南

## 📋 **权限概述**

EventProvider的所有Sender发送的广播都需要接收方声明特定权限才能接收。这提供了额外的安全层，确保只有授权的应用才能接收敏感的系统广播。

## 🔒 **权限定义**

### 权限名称
```xml
com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS
```

### 权限级别
- **protectionLevel**: `normal`
- **说明**: 普通权限，用户安装时自动授予，无需运行时请求

## 📱 **接收方应用配置**

### 1. 在AndroidManifest.xml中声明权限

接收EventProvider广播的应用必须在其`AndroidManifest.xml`中添加以下权限声明：

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.receiver">

    <!-- 必须声明此权限才能接收EventProvider广播 -->
    <uses-permission android:name="com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS" />

    <application>
        <!-- 应用内容 -->
    </application>
</manifest>
```

### 2. 注册广播接收器

#### 静态注册（推荐）
```xml
<receiver android:name=".ErrorInfoReceiver"
    android:exported="true">
    <intent-filter>
        <action android:name="yellowstone.ssolapp.ERROR_INFO" />
    </intent-filter>
</receiver>
```

#### 动态注册
```java
public class MainActivity extends AppCompatActivity {
    private ErrorInfoReceiver mReceiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        mReceiver = new ErrorInfoReceiver();
        IntentFilter filter = new IntentFilter("yellowstone.ssolapp.ERROR_INFO");
        registerReceiver(mReceiver, filter);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mReceiver != null) {
            unregisterReceiver(mReceiver);
        }
    }
}
```

### 3. 实现广播接收器

```java
public class ErrorInfoReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if ("yellowstone.ssolapp.ERROR_INFO".equals(intent.getAction())) {
            String errorCode = intent.getStringExtra("KEY_ERROR_CODE");
            String errorTime = intent.getStringExtra("KEY_ERROR_TIME");
            String errorSummary = intent.getStringExtra("KEY_ERROR_SUMMARY");
            
            Log.i("ErrorReceiver", "Received error: " + errorCode + 
                  " at " + errorTime + " - " + errorSummary);
            
            // 处理错误信息
            handleErrorInfo(errorCode, errorTime, errorSummary);
        }
    }
    
    private void handleErrorInfo(String code, String time, String summary) {
        // 实现错误处理逻辑
    }
}
```

## 🎯 **支持的广播类型**

### 1. 错误信息广播
- **Action**: `yellowstone.ssolapp.ERROR_INFO`
- **参数**:
  - `KEY_ERROR_CODE`: 错误代码
  - `KEY_ERROR_TIME`: 错误发生时间 (yyyyMMddHHmmss)
  - `KEY_ERROR_SUMMARY`: 错误摘要

### 2. ACC状态变化广播（未来支持）
- **Action**: `yellowstone.system.ACC_STATE_CHANGE`
- **参数**:
  - `KEY_ACC_STATE`: ACC状态 (0=OFF, 1=ON)
  - `KEY_TIMESTAMP`: 时间戳

### 3. 系统唤醒广播（未来支持）
- **Action**: `yellowstone.system.SYS_WAKEUP`
- **参数**:
  - `KEY_WAKEUP_TIME`: 唤醒时间戳

## ⚠️ **权限验证**

### 如果没有声明权限会怎样？

1. **广播发送成功**: EventProvider会正常发送广播
2. **接收失败**: 没有权限的应用无法接收到广播
3. **无错误提示**: 系统不会报错，只是静默忽略

### 验证权限是否生效

#### 方法1: 日志验证
```bash
# 查看EventProvider发送日志
adb logcat | grep "ErrorInfoSender"

# 查看接收方日志
adb logcat | grep "ErrorReceiver"
```

#### 方法2: 代码验证
```java
public class PermissionChecker {
    public static boolean hasReceivePermission(Context context) {
        int result = context.checkSelfPermission(
            "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS");
        return result == PackageManager.PERMISSION_GRANTED;
    }
}
```

## 🔧 **故障排除**

### 问题1: 接收不到广播
**可能原因**:
1. 未在AndroidManifest.xml中声明权限
2. 广播接收器未正确注册
3. Intent Filter不匹配

**解决方案**:
```xml
<!-- 确保权限声明正确 -->
<uses-permission android:name="com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS" />

<!-- 确保接收器注册正确 -->
<receiver android:name=".YourReceiver" android:exported="true">
    <intent-filter>
        <action android:name="yellowstone.ssolapp.ERROR_INFO" />
    </intent-filter>
</receiver>
```

### 问题2: 权限被拒绝
**可能原因**:
1. EventProvider未正确定义权限
2. 权限名称拼写错误

**解决方案**:
```bash
# 检查权限是否已定义
adb shell pm list permissions | grep thundercomm

# 检查应用是否有权限
adb shell dumpsys package com.your.package | grep permissions
```

## 📊 **最佳实践**

### 1. 权限检查
```java
public class BroadcastUtils {
    public static void checkPermission(Context context) {
        if (!hasReceivePermission(context)) {
            Log.w("BroadcastUtils", "Missing RECEIVE_BROADCASTS permission");
            // 提示用户或处理权限缺失情况
        }
    }
}
```

### 2. 优雅降级
```java
public class ErrorHandler {
    public void handleError(String errorCode) {
        if (hasReceivePermission(getContext())) {
            // 可以接收EventProvider广播
            registerErrorReceiver();
        } else {
            // 使用其他方式获取错误信息
            useAlternativeErrorSource();
        }
    }
}
```

### 3. 文档说明
在您的应用文档中明确说明：
```markdown
## 权限要求

本应用需要以下权限来接收系统状态广播：

- `com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS`
  - 用途：接收设备错误和状态变化通知
  - 级别：普通权限，安装时自动授予
```

## 🎯 **总结**

通过统一的权限控制：

1. **安全性提升**: 只有授权应用才能接收敏感广播
2. **访问控制**: 可以控制哪些应用能够接收系统状态信息
3. **向后兼容**: 现有应用只需添加权限声明即可
4. **统一管理**: 所有Sender使用相同的权限，简化管理

这种设计确保了EventProvider广播的安全性，同时保持了使用的简便性。
