# 未来Sender实现示例（带权限控制）

## 📋 **AccStateChangeSender实现示例**

```java
package com.thundercomm.eventprovider.send;

import android.content.Intent;
import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.SenderInfo;
import com.thundercomm.eventprovider.core.BaseSender;

/**
 * Sender for ACC state change broadcasts.
 * Requires RECEIVE_BROADCASTS permission to receive.
 */
@SenderInfo(action = ActionConstants.ACTION_ACC_STATE_CHANGE)
public class AccStateChangeSender extends BaseSender {
    
    private static final String KEY_ACC_STATE = "KEY_ACC_STATE";
    private static final String KEY_TIMESTAMP = "KEY_TIMESTAMP";
    private static final int ACC_STATE_OFF = 0;
    private static final int ACC_STATE_ON = 1;
    
    @Override
    public void sendBroadcast(Object... params) {
        if (params.length >= 1) {
            boolean accState = (Boolean) params[0];
            sendAccStateChange(accState);
        }
    }
    
    /**
     * Send ACC state change broadcast with permission control.
     * @param accOn true if ACC is ON, false if OFF
     */
    public void sendAccStateChange(boolean accOn) {
        try {
            Intent intent = new Intent(getAction());
            intent.putExtra(KEY_ACC_STATE, accOn ? ACC_STATE_ON : ACC_STATE_OFF);
            intent.putExtra(KEY_TIMESTAMP, System.currentTimeMillis());
            
            // Send broadcast with permission control
            sendBroadcastWithPermission(intent);
            EventProviderApp.LOG.i("AccStateChangeSender: Broadcast sent with permission - " +
                "ACC State: " + (accOn ? "ON" : "OFF") + ", Permission: " + getRequiredPermission());
        } catch (Exception ex) {
            EventProviderApp.LOG.e("AccStateChangeSender: Failed to send ACC state broadcast: " + ex.getMessage());
        }
    }
}
```

## 📋 **WakeupSender实现示例**

```java
package com.thundercomm.eventprovider.send;

import android.content.Intent;
import com.thundercomm.eventprovider.EventProviderApp;
import com.thundercomm.eventprovider.anno.ActionConstants;
import com.thundercomm.eventprovider.anno.SenderInfo;
import com.thundercomm.eventprovider.core.BaseSender;

/**
 * Sender for system wakeup broadcasts.
 * Requires RECEIVE_BROADCASTS permission to receive.
 */
@SenderInfo(action = ActionConstants.ACTION_SYS_WAKEUP)
public class WakeupSender extends BaseSender {
    
    private static final String KEY_WAKEUP_TIME = "KEY_WAKEUP_TIME";
    private static final String KEY_WAKEUP_REASON = "KEY_WAKEUP_REASON";
    
    @Override
    public void sendBroadcast(Object... params) {
        if (params.length >= 1) {
            String reason = String.valueOf(params[0]);
            sendWakeupBroadcast(reason);
        } else {
            sendWakeupBroadcast("UNKNOWN");
        }
    }
    
    /**
     * Send system wakeup broadcast with permission control.
     * @param reason The reason for wakeup
     */
    public void sendWakeupBroadcast(String reason) {
        try {
            Intent intent = new Intent(getAction());
            intent.putExtra(KEY_WAKEUP_TIME, System.currentTimeMillis());
            intent.putExtra(KEY_WAKEUP_REASON, reason);
            
            // Send broadcast with permission control
            sendBroadcastWithPermission(intent);
            EventProviderApp.LOG.i("WakeupSender: Broadcast sent with permission - " +
                "Reason: " + reason + ", Permission: " + getRequiredPermission());
        } catch (Exception ex) {
            EventProviderApp.LOG.e("WakeupSender: Failed to send wakeup broadcast: " + ex.getMessage());
        }
    }
}
```

## 📋 **使用示例**

### 通过SenderManager使用
```java
// 发送ACC状态变化
SenderManager.getInstance().sendBroadcast(
    ActionConstants.ACTION_ACC_STATE_CHANGE, 
    true  // ACC ON
);

// 发送系统唤醒
SenderManager.getInstance().sendBroadcast(
    ActionConstants.ACTION_SYS_WAKEUP, 
    "POWER_BUTTON"
);
```

### 直接使用（向后兼容）
```java
// 如果需要直接使用
AccStateChangeSender accSender = new AccStateChangeSender();
accSender.setContext(context);
accSender.setAction(ActionConstants.ACTION_ACC_STATE_CHANGE);
accSender.sendAccStateChange(true);
```

## 📋 **接收方示例**

### ACC状态变化接收器
```java
public class AccStateReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (ActionConstants.ACTION_ACC_STATE_CHANGE.equals(intent.getAction())) {
            int accState = intent.getIntExtra("KEY_ACC_STATE", 0);
            long timestamp = intent.getLongExtra("KEY_TIMESTAMP", 0);
            
            boolean isAccOn = (accState == 1);
            Log.i("AccReceiver", "ACC State: " + (isAccOn ? "ON" : "OFF") + 
                  " at " + new Date(timestamp));
            
            // 处理ACC状态变化
            handleAccStateChange(isAccOn, timestamp);
        }
    }
    
    private void handleAccStateChange(boolean accOn, long timestamp) {
        if (accOn) {
            // ACC开启处理逻辑
            startRecording();
        } else {
            // ACC关闭处理逻辑
            stopRecording();
        }
    }
}
```

### 系统唤醒接收器
```java
public class WakeupReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (ActionConstants.ACTION_SYS_WAKEUP.equals(intent.getAction())) {
            long wakeupTime = intent.getLongExtra("KEY_WAKEUP_TIME", 0);
            String reason = intent.getStringExtra("KEY_WAKEUP_REASON");
            
            Log.i("WakeupReceiver", "System wakeup: " + reason + 
                  " at " + new Date(wakeupTime));
            
            // 处理系统唤醒
            handleSystemWakeup(reason, wakeupTime);
        }
    }
    
    private void handleSystemWakeup(String reason, long wakeupTime) {
        // 根据唤醒原因执行不同逻辑
        switch (reason) {
            case "POWER_BUTTON":
                // 电源按钮唤醒
                break;
            case "TIMER":
                // 定时器唤醒
                break;
            default:
                // 其他原因
                break;
        }
    }
}
```

### AndroidManifest.xml配置
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.receiver">

    <!-- 必须声明权限 -->
    <uses-permission android:name="com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS" />

    <application>
        <!-- ACC状态接收器 -->
        <receiver android:name=".AccStateReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="yellowstone.system.ACC_STATE_CHANGE" />
            </intent-filter>
        </receiver>

        <!-- 系统唤醒接收器 -->
        <receiver android:name=".WakeupReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="yellowstone.system.SYS_WAKEUP" />
            </intent-filter>
        </receiver>
    </application>
</manifest>
```

## 📋 **权限验证**

### 测试权限是否生效
```java
public class PermissionTest {
    public static void testBroadcastPermission(Context context) {
        // 检查是否有接收权限
        int result = context.checkSelfPermission(
            "com.thundercomm.eventprovider.permission.RECEIVE_BROADCASTS");
        
        if (result == PackageManager.PERMISSION_GRANTED) {
            Log.i("PermissionTest", "Has RECEIVE_BROADCASTS permission");
        } else {
            Log.w("PermissionTest", "Missing RECEIVE_BROADCASTS permission");
        }
    }
}
```

这些示例展示了如何在未来实现其他Sender时保持权限控制的一致性。
