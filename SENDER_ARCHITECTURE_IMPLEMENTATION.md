# Sender架构实现文档

## 📋 **实现概述**

成功将`send`路径下的广播发送器改造为类似`controller`的统一架构模式，实现了注解驱动的自动注册和管理机制。

## 🏗️ **新增的基础架构**

### 1. **ISender接口**
**文件**: `app/src/main/java/com/thundercomm/eventprovider/core/ISender.java`

**功能**: 定义所有Sender的统一接口
```java
public interface ISender {
    void sendBroadcast(Object... params);
    void setContext(Context context);
    void setAction(String action);
    String getAction();
}
```

### 2. **BaseSender基类**
**文件**: `app/src/main/java/com/thundercomm/eventprovider/core/BaseSender.java`

**功能**: 提供Sender的基础实现和Context管理
- 继承ISender接口
- 管理Context和Action
- 为具体Sender提供通用功能

### 3. **@SenderInfo注解**
**文件**: `app/src/main/java/com/thundercomm/eventprovider/anno/SenderInfo.java`

**功能**: 用于标记Sender类并声明其处理的Action
```java
@SenderInfo(action = ActionConstants.ACTION_ERROR_INFO)
```

### 4. **SenderManager管理器**
**文件**: `app/src/main/java/com/thundercomm/eventprovider/manager/SenderManager.java`

**功能**: 提供统一的Sender调用接口
- 单例模式
- 统一的sendBroadcast方法
- 错误处理和日志记录

## 🔧 **EventProviderApp集成**

### 新增功能:
1. **SENDERS Map**: 存储所有注册的Sender
2. **initSenders()方法**: 自动扫描和注册Sender
3. **getSender()方法**: 根据Action获取Sender实例

### 自动注册机制:
- 扫描`com.thundercomm.eventprovider.send`包
- 查找带有@SenderInfo注解的类
- 自动实例化并注册到SENDERS Map

## 📊 **ActionConstants扩展**

### 新增常量:
```java
// ErrorInfoSender constants
public static final String EXTRA_KEY_ERROR_CODE = "KEY_ERROR_CODE";
public static final String EXTRA_KEY_ERROR_TIME = "KEY_ERROR_TIME";
public static final String EXTRA_KEY_ERROR_SUMMARY = "KEY_ERROR_SUMMARY";
```

## 🔄 **ErrorInfoSender改造**

### 主要变更:
1. **继承BaseSender**: 使用新的基础架构
2. **@SenderInfo注解**: 自动注册机制
3. **sendBroadcast方法**: 实现统一接口
4. **向后兼容**: 保持原有public方法不变
5. **使用ActionConstants**: 所有常量统一管理

### 新架构实现:
```java
@SenderInfo(action = ActionConstants.ACTION_ERROR_INFO)
public class ErrorInfoSender extends BaseSender {
    
    @Override
    public void sendBroadcast(Object... params) {
        // 统一的参数处理
        if (params.length >= 2) {
            String errorCode = String.valueOf(params[0]);
            String errorSummary = String.valueOf(params[1]);
            sendErrorInfo(errorCode, errorSummary);
        }
    }
    
    // 保持原有方法向后兼容
    public void sendErrorInfo(String hwErrorCode, String errorSummary) {
        // 使用getContext()和getAction()
        // 使用ActionConstants中的常量
    }
}
```

## 🎯 **RuntimeErrorListener更新**

### 使用新架构:
```java
// 旧方式 (已移除)
ErrorInfoSender sender = new ErrorInfoSender(context);
sender.sendErrorInfo(errorCode, summary);

// 新方式
SenderManager.getInstance().sendBroadcast(
    ActionConstants.ACTION_ERROR_INFO, 
    hwErrorCode, 
    ""
);
```

### 优势:
- 无需手动管理Sender实例
- 统一的错误处理
- 解耦合设计

## ✅ **实现特点**

### 1. **统一架构**
- 与Controller模式保持一致
- 注解驱动的自动注册
- 统一的管理和调用方式

### 2. **向后兼容**
- ErrorInfoSender保持原有构造函数
- 原有public方法继续可用
- 渐进式迁移支持

### 3. **易于扩展**
- 新增Sender只需添加@SenderInfo注解
- 自动注册，无需手动配置
- 统一的接口规范

### 4. **错误处理**
- SenderManager提供统一错误处理
- 详细的日志记录
- 优雅的异常处理

## 🔮 **未来扩展**

### AccStateChangeSender示例:
```java
@SenderInfo(action = ActionConstants.ACTION_ACC_STATE_CHANGE)
public class AccStateChangeSender extends BaseSender {
    
    @Override
    public void sendBroadcast(Object... params) {
        if (params.length >= 1) {
            boolean accState = (Boolean) params[0];
            sendAccStateChange(accState);
        }
    }
    
    private void sendAccStateChange(boolean accOn) {
        Intent intent = new Intent(getAction());
        intent.putExtra("KEY_ACC_STATE", accOn ? 1 : 0);
        intent.putExtra("KEY_TIMESTAMP", System.currentTimeMillis());
        getContext().sendBroadcast(intent);
    }
}
```

### WakeupSender示例:
```java
@SenderInfo(action = ActionConstants.ACTION_SYS_WAKEUP)
public class WakeupSender extends BaseSender {
    
    @Override
    public void sendBroadcast(Object... params) {
        sendWakeupBroadcast();
    }
    
    private void sendWakeupBroadcast() {
        Intent intent = new Intent(getAction());
        intent.putExtra("KEY_WAKEUP_TIME", System.currentTimeMillis());
        getContext().sendBroadcast(intent);
    }
}
```

## 📈 **性能和维护性**

### 优势:
1. **集中管理**: 所有Sender统一注册和管理
2. **减少耦合**: 使用者无需知道具体实现
3. **易于测试**: 统一接口便于单元测试
4. **代码复用**: BaseSender提供通用功能
5. **一致性**: 与Controller架构保持一致

### 注意事项:
1. **初始化顺序**: Sender在Controller之后初始化
2. **异常处理**: SenderManager提供统一异常处理
3. **线程安全**: SenderManager使用单例模式，注意线程安全

## 🎉 **实施完成**

✅ **已完成**:
- ISender接口和BaseSender基类
- @SenderInfo注解
- SenderManager管理器
- EventProviderApp集成
- ActionConstants扩展
- ErrorInfoSender改造
- RuntimeErrorListener更新

✅ **测试建议**:
1. 验证ErrorInfoSender广播发送正常
2. 确认SenderManager.getInstance().sendBroadcast()工作正常
3. 检查向后兼容性
4. 验证自动注册机制

这个架构为EventProvider的广播发送功能提供了统一、可扩展、易维护的解决方案。
