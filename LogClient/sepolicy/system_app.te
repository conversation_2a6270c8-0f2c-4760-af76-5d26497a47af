allow system_app system_tlogd_file:dir { search getattr setattr open read remove_name rmdir write add_name create };
allow system_app system_tlogd_file:file { map unlink getattr setattr create open read write append };
allow system_app vendor_tlogd_file:dir { search getattr setattr open read remove_name rmdir write add_name create };
allow system_app vendor_tlogd_file:file { unlink getattr setattr create open read write };
allow system_app pstorefs:dir { read open search };
allow system_app dumpstate:unix_stream_socket connectto;
allow system_app dumpstate_socket:sock_file write;
allow system_app ctl_start_prop:property_service { set };
allow system_app storaged:binder { call };
allow system_app wificond:binder { call };

