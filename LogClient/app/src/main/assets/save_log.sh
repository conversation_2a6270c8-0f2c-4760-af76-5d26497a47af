#!/system/bin/sh

IMEI=$1
EXTERNAL_SD_STORAGE=$2
BUGREPORT_PROMPT=$3

TLOG=Logs
TLOG_DIR=/data/tlog/

DST_NAME="tlog_${IMEI}_`date +%m%d%H%M%S`"

DST_PATH=${EXTERNAL_SD_STORAGE}/${TLOG}
mkdir -p ${DST_PATH}

TARGET=${DST_PATH}/${DST_NAME}.tar.gz

setprop sys.tlog.fatal.active 0

getprop > ${TLOG_DIR}/prop.txt

# get bugreport
if [ -n "$BUGREPORT_PROMPT" ]; then
    echo $BUGREPORT_PROMPT
fi
echo "[`date +%H:%M:%S`]Getting bugreport..."
bfolder='/data/tlog/bugreport'
TARGET_BUGREPORT=bugreport_`date +%m%d%H%M%S`.zip
BUGREPORT_PATH=${bfolder}/${TARGET_BUGREPORT}
/system/bin/bugreportz -s > ${BUGREPORT_PATH}
echo "[`date +%H:%M:%S`]Save Bugreport to: ${BUGREPORT_PATH}"
sleep 1

echo "[`date +%H:%M:%S`]Getting dumpsys..."
TARGET_DUMPSYS=${TLOG_DIR}/dumpsys_`date +%m%d%H%M%S`.txt
dresult=`dumpsys > ${TARGET_DUMPSYS}`
# echo "[`date +%H:%M:%S`]Dumpsys results: $dresult"
sleep 1

#cp vendor qxdm,pstore to data
cp -fpr /data/vendor/tlog/qxdm/* /data/tlog/qxdm/
cp -fpr /data/vendor/tlog/persist/* /data/tlog/persist/
cp -fpr /data/vendor/tlog/tzlog/* /data/tlog/tzlog/
# tar
echo "[`date +%H:%M:%S`]Save log to: " ${TARGET}
tar -zcf ${TARGET} ${TLOG_DIR}
sleep 1

rm -rf ${TARGET_DUMPSYS}
rm -rf /data/tlog/qxdm/*
rm -rf /data/tlog/persist/*
rm -rf /data/tlog/tzlog/*
rm -rf /data/vendor/tlog/qxdm/*
rm -rf /data/vendor/tlog/persist/*
rm -rf /data/vendor/tlog/tzlog/*
rm -rf /data/tlog/bugreport/*
rm -rf /data/tlog/bluetooth/*
rm -rf /data/tlog/tcpdump/*
rm -rf /data/tlog/fatal/anr/*
rm -rf /data/tlog/fatal/tombstones/*
exit 0
