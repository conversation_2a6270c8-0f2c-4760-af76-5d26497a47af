<resources>
    <string name="app_name">LogClient</string>
    <string name="logging_starting">Collect ongoing</string>
    <string name="logging_stopped">Collect stopped</string>
    <string name="button_start">Start</string>
    <string name="button_stop">Stop</string>

    <!--Notification string -->
    <string name="channel_id">LogClient</string>
    <string name="channel_title">LogClient enabled</string>
    <string name="builder_id">LogClient</string>
    <string name="builder_content">Take log is ongoing</string>

    <!-- Android logs -->
    <string name="android_logs">Android logs</string>
    <string name="android_default_logs">Default log</string>
    <string name="android_main_logs">Main log</string>
    <string name="android_system_logs">System log</string>
    <string name="android_events_logs">Events log</string>
    <string name="android_radio_logs">Radio log</string>
    <string name="android_crash_logs">Crash log</string>
    <string name="kernel_logs">Kernel log</string>

    <!-- Bugreport -->
    <string name="bugreport_anr">Bugreport &amp; Anr</string>
    <string name="bugreport_prompt_message">Take bugreport may take more than 4 minutes</string>

    <!-- TCP dump -->
    <string name="tcp_dump">TCP dump</string>

    <!-- QXDM log -->
    <string name="qxdm_log">QXDM</string>

    <!-- Bluetooth log -->
    <string name="bluetooth_log">Bluetooth</string>

    <string name="must_select_message">Hey Guy, You must select one of them.</string>

    <string name="enable_ramdump">Enable ramdump when panic</string>

    <!-- Menu -->
    <string name="delete_all">Delete All</string>
    <string name="menu_settings">Settings</string>
    <string name="save_settings">Save Settings</string>

    <!-- persist settings -->
    <string name="persist_settings">Persist Settings</string>
    <string name="persist_parameter_key">persist_parameter</string>
    <string name="persist_enable_when_boot">Enable persist log when boot</string>

    <string name="persist_max_number_key">persist_max_number</string>
    <string name="persist_max_number">Persist max number</string>
    <string name="persist_file_size_key">persist_file_size</string>
    <string name="persist_file_size">Persist file size (KB)</string>

    <string name="persist_separate_kernel_key">persist_separate_kernel</string>
    <string name="persist_separate_kernel">Take persist kernel log to specific files</string>

    <string name="persist_separate_event_key">persist_separate_event</string>
    <string name="persist_separate_event">Take persist event log to specific files</string>

    <!-- manual settings -->
    <string name="manual_settings">Manual Settings</string>

    <string name="manual_max_number_key">manual_max_number</string>
    <string name="manual_max_number">Manual max number</string>
    <string name="manual_file_size_key">manual_file_size</string>
    <string name="manual_file_size">Manual file size (KB)</string>

    <string name="manual_separate_kernel_key">manual_separate_kernel</string>
    <string name="manual_separate_kernel">Take manual kernel log to specific files</string>

    <string name="manual_separate_event_key">manual_separate_event</string>
    <string name="manual_separate_event">Take manual event log to specific files</string>

    <!-- ramdump -->
    <string name="ramdump_settings">Ramdump Settings</string>
    <string name="ramdump_settings_key">ramdump_settings</string>

    <!-- tcpdump -->
    <string name="tcpdump_settings">Tcpdump Settings</string>
    <string name="tcpdump_parameter_key">tcpdump_parameter</string>
    <string name="show_change_tcpdump_parameter">Change tcpdump parameter</string>

    <!-- qxdm -->
    <string name="qxdm_settings">Qxdm Settings</string>
    <string name="qxdm_parameter_key">qxdm_parameter</string>
    <string name="show_change_qxdm_parameter">Change qxdm parameter</string>

    <!-- Delete Dialog -->
    <string name="delete_dialog_title">Delete all zip files?</string>
    <string name="delete_dialog_body">Are you want to delete all the files?</string>

    <!-- kernel log switch -->
    <string name="kernel_log_turn_on">Turn on kernel log</string>
    <string name="kernel_log_turn_off">Turn off kernel log</string>
    <string name="kernel_log_dialog_reboot_msg">For the setting to take effect, the device will be restarted, please confirm?</string>
    <string name="dialog_btn_ok">Ok</string>
    <string name="dialog_btn_cancel">Cancel</string>

    <!-- diag switch -->
    <string name="diag_turn_on">Turn on diag</string>
    <string name="diag_turn_off">Turn off diag</string>
</resources>
