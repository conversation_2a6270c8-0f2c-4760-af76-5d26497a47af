<?xml version ="1.0" encoding ="utf-8"?>
<!--
 Copyright © 2020, The Thundercomm. All rights reserved.
-->
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:logclient="http://schemas.android.com/apk/res-auto">

    <!-- persist log -->
    <PreferenceCategory android:title="@string/persist_settings" >
        <!-- Enable when boot -->
        <CheckBoxPreference
            android:defaultValue="@bool/persist_enable_when_boot_default"
            android:key="@string/persist_parameter_key"
            android:title="@string/persist_enable_when_boot" />

        <!-- Persist max number -->
        <com.tc.logclient.widget.NumericalEditTextPreference
            android:defaultValue="@integer/persist_max_number_default"
            android:key="@string/persist_max_number_key"
            android:title="@string/persist_max_number"
            logclient:minNumber="1"
            logclient:maxNumber="1000" />

        <!-- Persist file size -->
        <com.tc.logclient.widget.NumericalEditTextPreference
            android:defaultValue="@integer/persist_max_file_size"
            android:key="@string/persist_file_size_key"
            android:title="@string/persist_file_size"
            logclient:minNumber="1"
            logclient:maxNumber="1024" />

        <!-- Separate kernel log -->
        <CheckBoxPreference
            android:defaultValue="@bool/persist_enable_separate_kernel_log"
            android:key="@string/persist_separate_kernel_key"
            android:title="@string/persist_separate_kernel" />

        <!-- Separate event log -->
        <CheckBoxPreference
            android:defaultValue="@bool/persist_enable_separate_event_log"
            android:key="@string/persist_separate_event_key"
            android:title="@string/persist_separate_event" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/manual_settings" >
        <!-- Manual max number -->
        <com.tc.logclient.widget.NumericalEditTextPreference
            android:defaultValue="@integer/manual_max_number_default"
            android:key="@string/manual_max_number_key"
            android:title="@string/manual_max_number"
            logclient:minNumber="1"
            logclient:maxNumber="1000" />

        <!-- Manual file size -->
        <com.tc.logclient.widget.NumericalEditTextPreference
            android:defaultValue="@integer/manual_max_file_size"
            android:key="@string/manual_file_size_key"
            android:title="@string/manual_file_size"
            logclient:minNumber="1"
            logclient:maxNumber="1024" />

        <!-- Separate kernel log -->
        <CheckBoxPreference
            android:defaultValue="@bool/manual_enable_separate_kernel_log"
            android:key="@string/manual_separate_kernel_key"
            android:title="@string/manual_separate_kernel" />

        <!-- Separate event log -->
        <CheckBoxPreference
            android:defaultValue="@bool/manual_enable_separate_event_log"
            android:key="@string/manual_separate_event_key"
            android:title="@string/manual_separate_event" />
    </PreferenceCategory>

    <!-- RAM dump -->
    <PreferenceCategory
        android:title="@string/ramdump_settings" >
        <CheckBoxPreference
            android:defaultValue="@bool/default_enable_ramdump"
            android:key="@string/ramdump_settings_key"
            android:title="@string/enable_ramdump" />
    </PreferenceCategory>

    <!-- TCP dump -->
    <PreferenceCategory
        android:title="@string/tcpdump_settings" >
        <EditTextPreference
            android:defaultValue="@string/tcpdump_default_parameter"
            android:key="@string/tcpdump_parameter_key"
            android:maxLength="30"
            android:title="@string/show_change_tcpdump_parameter" />
    </PreferenceCategory>

    <!-- Qxdm -->
    <PreferenceCategory
        android:title="@string/qxdm_settings" >
        <EditTextPreference
            android:defaultValue="@string/qxdm_default_parameter"
            android:key="@string/qxdm_parameter_key"
            android:maxLength="30"
            android:title="@string/show_change_qxdm_parameter" />
    </PreferenceCategory>
</PreferenceScreen>