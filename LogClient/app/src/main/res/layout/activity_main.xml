<?xml version="1.0" encoding="utf-8"?>
<!--
 Copyright © 2020, The Thundercomm. All rights reserved.
-->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Status message with progress bar -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_margin="10dp">

            <ProgressBar
                android:id="@+id/progress_bar"
                style="@android:style/Widget.Material.ProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:indeterminate="true" />

            <TextView
                android:id="@+id/status_message"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_marginLeft="10dp"
                android:gravity="center"
                android:textSize="@dimen/statusTextSize" />
        </LinearLayout>

        <!-- Log selection check boxes -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingLeft="50dp">

            <!-- Android LogCat logs -->
            <include layout="@layout/android_logs_main" />

            <!-- Bugreport  -->
            <CheckBox
                android:id="@+id/bugreport_checkbox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/bugreport_anr"
                android:textSize="@dimen/checkBoxTextSize" />

            <!-- TCP Dump -->
            <CheckBox
                android:id="@+id/tcp_dump_checkbox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/tcp_dump"
                android:textSize="@dimen/checkBoxTextSize" />

            <!-- QXDM LOG -->
            <CheckBox
                android:id="@+id/qxdm_checkbox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/qxdm_log"
                android:textSize="@dimen/checkBoxTextSize" />

            <!-- Bluetooth LOG -->
            <CheckBox
                android:id="@+id/bluetooth_checkbox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/bluetooth_log"
                android:textSize="@dimen/checkBoxTextSize" />

        </LinearLayout>

        <!-- Start/Stop button -->
        <Button
            android:id="@+id/start_stop_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_margin="10dp"
            android:text="@string/button_start"
            android:textSize="@dimen/buttonTextSize" />

        <TextView
            android:id="@+id/log_tv"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:fadeScrollbars="false"
            android:scrollbars="vertical" />
    </LinearLayout>

</ScrollView>
