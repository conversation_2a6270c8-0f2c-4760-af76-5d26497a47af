<?xml version="1.0" encoding="utf-8"?>
<!--
 Copyright © 2020, The Thundercomm. All rights reserved.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.tc.logclient.widget.MultiSelectBox
        xmlns:logClientNS="http://schemas.android.com/apk/res/com.tc.logclient"
        android:id="@+id/android_logs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/android_logs"
        android:textSize="@dimen/checkBoxTextSize"
        logClientNS:childSelectBoxes="@+id/android_logs_checkboxes" />

    <LinearLayout
        android:id="@+id/android_logs_checkboxes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="20dp">

        <CheckBox
            android:id="@+id/android_default_logs_checkbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/android_default_logs"
            android:textSize="@dimen/checkBoxTextSize"
            android:enabled="false" />

        <CheckBox
            android:id="@+id/android_main_logs_checkbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/android_main_logs"
            android:textSize="@dimen/checkBoxTextSize"
            android:enabled="false" />

        <CheckBox
            android:id="@+id/android_system_logs_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/android_system_logs"
            android:textSize="@dimen/checkBoxTextSize"
            android:enabled="false" />

        <CheckBox
            android:id="@+id/android_events_logs_checkbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/android_events_logs"
            android:textSize="@dimen/checkBoxTextSize"
            android:enabled="false" />

        <CheckBox
            android:id="@+id/android_radio_logs_checkbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/android_radio_logs"
            android:textSize="@dimen/checkBoxTextSize"
            android:enabled="false" />

        <CheckBox
            android:id="@+id/android_crash_logs_checkbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/android_crash_logs"
            android:textSize="@dimen/checkBoxTextSize"
            android:enabled="false" />

        <CheckBox
            android:id="@+id/kernel_log_checkbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/kernel_logs"
            android:textSize="@dimen/checkBoxTextSize"
            android:enabled="false" />
    </LinearLayout>
</LinearLayout>