<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.tc.logclient"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.SHUTDOWN" />
    <uses-permission android:name="android.permission.REBOOT" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="false"
        android:theme="@style/AppTheme">

        <activity
            android:name=".LogClientActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:excludeFromRecents="true"
            android:launchMode="singleTask"
            android:screenOrientation="nosensor"
            android:supportsPictureInPicture="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="com.tc.logclient.logclientactivity" />
            </intent-filter>
        </activity>

        <service
            android:name=".LogService"
            android:exported="false" />
    </application>

</manifest>
