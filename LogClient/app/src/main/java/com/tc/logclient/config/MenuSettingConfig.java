/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.config;

import com.tc.logclient.utils.SysfsRWer;

public class MenuSettingConfig {
    private static final String TAG = "MenuSettingConfigInfo";

    public static final String CONFIG_COMMAND_SET = "SET";
    public static final String CONFIG_COMMAND_GET = "GET";
    public static final String CONFIG_COMMAND_DIVIDER = "-";

    public static final String CONFIG_DIVIDER = ";";

    public static final String SEPARATE_KERNEL = "kernel";
    public static final String SEPARATE_EVENT = "event";
    private static final String SEPARATE_SPLITTER = ",";
    private static final String SEPARATE_NOT_SET = "NOT";

    private boolean mPersistEnable;
    private String mPersistMaxFileCount;
    private String mPersistFileSizeBytes;
    private boolean mPersistSeparateKernel;
    private boolean mPersistSeparateEvent;

    private String mManualMaxFileCount;
    private String mManualFileSizeBytes;
    private boolean mManualSeparateKernel;
    private boolean mManualSeparateEvent;

    public MenuSettingConfig() {
    }

    public void setPersistEnable(boolean persistEnable) {
        this.mPersistEnable = persistEnable;
    }

    public void setPersistMaxFileCount(String persistMaxFileCount) {
        this.mPersistMaxFileCount = persistMaxFileCount;
    }

    public void setPersistFileSizeBytes(String persistFileSizeBytes) {
        this.mPersistFileSizeBytes = persistFileSizeBytes;
    }

    public void setPersistSeparateKernel(boolean persistSeparateKernel) {
        this.mPersistSeparateKernel = persistSeparateKernel;
    }

    public void setPersistSeparateEvent(boolean persistSeparateEvent) {
        this.mPersistSeparateEvent = persistSeparateEvent;
    }

    public void setManualMaxFileCount(String manualMaxFileCount) {
        this.mManualMaxFileCount = manualMaxFileCount;
    }

    public void setManualFileSizeBytes(String manualFileSizeBytes) {
        this.mManualFileSizeBytes = manualFileSizeBytes;
    }

    public void setManualSeparateKernel(boolean manualSeparateKernel) {
        this.mManualSeparateKernel = manualSeparateKernel;
    }

    public void setManualSeparateEvent(boolean manualSeparateEvent) {
        this.mManualSeparateEvent = manualSeparateEvent;
    }

    public boolean getIsPersistEnable() {
        return mPersistEnable;
    }

    public String getPersistMaxFileCount() {
        return mPersistMaxFileCount;
    }

    public String getPersistFileSizeBytes() {
        return mPersistFileSizeBytes;
    }

    public boolean getIsPersistSeparateKernel() {
        return mPersistSeparateKernel;
    }

    public boolean getIsPersistSeparateEvent() {
        return mPersistSeparateEvent;
    }

    public String getManualMaxFileCount() {
        return mManualMaxFileCount;
    }

    public String getManualFileSizeBytes() {
        return mManualFileSizeBytes;
    }

    public boolean getIsManualSeparateKernel() {
        return mManualSeparateKernel;
    }

    public boolean getIsManualSeparateEvent() {
        return mManualSeparateEvent;
    }

    public void enableRamdump(boolean enable) {
        SysfsRWer.enableRamdump(enable);
    }

    public boolean isRamdumpEnabled() {
        return SysfsRWer.isRamdumpEnabled();
    }

    @Override
    public String toString() {
        return CONFIG_COMMAND_SET + CONFIG_COMMAND_DIVIDER
                + booleanToString(mPersistEnable) + CONFIG_DIVIDER
                + mPersistMaxFileCount + CONFIG_DIVIDER
                + mPersistFileSizeBytes + CONFIG_DIVIDER
                + logToFileToString(mPersistSeparateKernel, mPersistSeparateEvent) + CONFIG_DIVIDER
                + mManualMaxFileCount + CONFIG_DIVIDER
                + mManualFileSizeBytes + CONFIG_DIVIDER
                + logToFileToString(mManualSeparateKernel, mManualSeparateEvent);
    }

    private String booleanToString(boolean enable) {
        return enable ? "1" : "0";
    }

    private String logToFileToString(boolean separateKernel, boolean separateEvent) {
        String result = "";
        if (separateKernel) {
            result = SEPARATE_KERNEL;
        }
        if (separateKernel && separateEvent) {
            result += SEPARATE_SPLITTER;
        }
        if (separateEvent) {
            result += SEPARATE_EVENT;
        }
        if (result.equals("")) {
            result = SEPARATE_NOT_SET;
        }
        return result;
    }
}
