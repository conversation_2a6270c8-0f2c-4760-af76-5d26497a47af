/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.utils;

import android.content.Context;
import android.os.Environment;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;

import java.io.File;

public class StorageManagerWrapper {
    private static final String TAG = "SMWrapper";
    private static final int VOLUME_SDCARD_INDEX = 1;

    private static StorageManager mStorageManager = null;

    /**
     * getStorageManager.
     */
    public static StorageManager getStorageManager(Context context) {
        if (mStorageManager == null) {
            mStorageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
        }
        return mStorageManager;
    }

    /**
     * getVolumeList.
     */
    public static StorageVolume[] getVolumeList(Context context) {
        StorageVolume[] volumes = null;
        try {
            volumes = getStorageManager(context).getVolumeList();
        } catch (Exception ex) {
            Logger.logE(TAG, "couldn't talk to MountService", ex);
        }
        return volumes;
    }

    public static StorageVolume getStorageVolume(Context context, File file) {
        return getStorageManager(context).getStorageVolume(file);
    }

    /**
     * getSdDirectory.
     */
    public static String getSdDirectory(Context context) {
        String sdDirectory = null;
        try {
            final StorageVolume[] volumes = getVolumeList(context);
            if (volumes.length > VOLUME_SDCARD_INDEX) {
                StorageVolume volume = volumes[VOLUME_SDCARD_INDEX];
                if (volume.isRemovable()) {
                    sdDirectory = volume.getPath();
                }
            }
        } catch (Exception ex) {
            Logger.logE(TAG, "couldn't talk to MountService", ex);
        }
        return sdDirectory;
    }

    /**
     * getInternalSdDirectory.
     */
    public static String getInternalSdDirectory(Context context) {
        String sdDirectory = null;
        try {
            final StorageVolume[] volumes = getVolumeList(context);
            if (volumes != null) {
                StorageVolume volume = volumes[0];
                sdDirectory = volume.getInternalPath();
            }
        } catch (Exception ex) {
            Logger.logE(TAG, "couldn't talk to MountService", ex);
        }
        return sdDirectory;
    }

    /**
     * getSdState.
     */
    public static String getSdState(Context context) {
        String sdPath = getSdDirectory(context);
        if (sdPath != null) {
            StorageVolumeWrapper.setBaseInstance(context, new File(sdPath));
            String state = StorageVolumeWrapper.getState();
            if (state != null) {
                return state;
            } else {
                return Environment.MEDIA_UNKNOWN;
            }
        }
        return Environment.MEDIA_UNMOUNTED;
    }
}
