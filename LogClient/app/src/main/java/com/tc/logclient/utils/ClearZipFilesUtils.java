package com.tc.logclient.utils;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.AsyncTask;

import com.tc.logclient.LogCollector;
import com.tc.logclient.R;

import java.io.File;

public class ClearZipFilesUtils {
    private static final String TAG = "ClearZipFilesTask";

    private Context mContext;
    private String mClearPath;
    private ClearZipTask mClearTask = null;

    /**
     * Init.
     * @Context Context.
     * @String path.
     */
    public ClearZipFilesUtils(Context context, String path) {
        mContext = context;
        mClearPath = path;
        mClearTask = new ClearZipTask(mClearPath);
    }

    /**
     * Show confirm dialog if user want to clear log files.
     */
    public void createConfirmDialog() {
        AlertDialog.Builder dialog = new AlertDialog.Builder(mContext);
        dialog.setIcon(android.R.drawable.ic_dialog_alert);
        dialog.setTitle(mContext.getString(R.string.delete_dialog_title));
        dialog.setMessage(mContext.getString(R.string.delete_dialog_body));
        dialog.setCancelable(false);
        dialog.setPositiveButton(android.R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                executeClear();
            }
        });

        dialog.setNegativeButton(android.R.string.cancel,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        cancelClear();
                    }
                });
        dialog.create();
        dialog.show();
    }

    private void executeClear() {
        mClearTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    private void cancelClear() {
        mClearTask.cancel(true);
    }

    private class ClearZipTask extends AsyncTask<Void, String, Void> {

        String mPath;

        public ClearZipTask(String path) {
            mPath = path;
        }

        @Override
        protected Void doInBackground(Void... voids) {
            File path = new File(mPath);
            File[] files = path.listFiles();
            if (files != null) {
                for (int i = 0; i < files.length; i++) {
                    Logger.logI(TAG, files[i].getName());
                    files[i].delete();
                }
            }
            if (StorageManagerWrapper.getSdDirectory(mContext) != null) {
                File sdPath = new File(StorageManagerWrapper.getSdDirectory(mContext) + "/tlog/");
                File[] sdFiles = sdPath.listFiles();
                if (sdFiles != null) {
                    for (int i = 0; i < sdFiles.length; i++) {
                        Logger.logI(TAG, sdFiles[i].getName());
                        sdFiles[i].delete();
                    }
                }
            }
            return null;
        }
    }
}
