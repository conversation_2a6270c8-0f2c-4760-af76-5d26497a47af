/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.preference.EditTextPreference;
import android.preference.Preference;
import android.preference.Preference.OnPreferenceChangeListener;
import android.text.InputType;
import android.util.AttributeSet;

import com.tc.logclient.R;
import com.tc.logclient.utils.Logger;

public class NumericalEditTextPreference extends EditTextPreference
    implements OnPreferenceChangeListener {
    private static final String TAG = "NumericalEditTextPreference";

    /**
     * The minimum value.
     */
    private int mMinNumber;

    /**
     * The maximum value.
     */
    private int mMaxNumber;

    /**
     * Construct.
     *
     * @param context application context.
     */
    public NumericalEditTextPreference(Context context) {
        super(context);
    }

    /**
     * Construct.
     *
     * @param context application context.
     * @param attrs attribute set for preference.
     */
    public NumericalEditTextPreference(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.NumericalEditTextPreference);
        mMinNumber = typedArray.getInt(R.styleable.NumericalEditTextPreference_minNumber, Integer.MIN_VALUE);
        mMaxNumber = typedArray.getInt(R.styleable.NumericalEditTextPreference_maxNumber, Integer.MAX_VALUE);
        typedArray.recycle();
        getEditText().setInputType(InputType.TYPE_CLASS_NUMBER);
        setOnPreferenceChangeListener(this);
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {
        try {
            int value = Integer.parseInt((String) newValue);
            return value >= mMinNumber && value <= mMaxNumber;
        } catch (NumberFormatException ex) {
            Logger.logE(TAG, "" + ex.toString());
            return false;
        }
    }

    @Override
    public String getText() {
        return String.valueOf(Integer.parseInt(super.getText()));
    }
}
