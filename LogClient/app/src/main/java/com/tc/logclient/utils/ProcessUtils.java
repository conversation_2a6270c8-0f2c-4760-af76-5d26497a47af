/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.utils;

import android.content.Context;
import android.os.Process;

public class ProcessUtils {
    public static final String TAG = "ProcessUtils";

    /**
     * Start a process.
     *
     * @param pid the id for process.
     */
    public void startProcess(int pid) {

    }

    /**
     * Stop a process.
     *
     * @param pid the id for process.
     */
    public void stopProcess(int pid) {

    }

    /**
     * Stop a process.
     *
     * @param name the name for process.
     */
    public static void getRunningProcess(Context context, String name) {
    }
}
