/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.config;

public class ManualLogConfig {
    // Android default logs.
    public static final int FLAG_ANDROID_DEFAULT_LOGS = 0x00000001;
    // Android main logs.
    public static final int FLAG_ANDROID_MAIN_LOGS    = 0x00000002;
    // Android system logs.
    public static final int FLAG_ANDROID_SYSTEM_LOGS  = 0x00000004;
    // Android events logs.
    public static final int FLAG_ANDROID_EVENTS_LOGS  = 0x00000008;
    // Android radio logs.
    public static final int FLAG_ANDROID_RADIO_LOGS   = 0x00000010;
    // Android crash logs.
    public static final int FLAG_ANDROID_CRASH_LOGS   = 0x00000020;
    // Kernel logs.
    public static final int FLAG_KERNEL_LOGS          = 0x00000040;
    // TCP dump.
    public static final int FLAG_BUGREPORT            = 0x00000080;
    // TCP dump.
    public static final int FLAG_TCP_DUMP             = 0x00000100;
    // QXDM
    public static final int FLAG_QXDM_LOGS            = 0x00000200;
    // Bluetooth
    public static final int FLAG_BLUETOOTH_LOGS        = 0x00000400;

    // All the android log.
    public static final int FLAG_ANDROID_LOG = FLAG_ANDROID_DEFAULT_LOGS
                                               | FLAG_ANDROID_MAIN_LOGS
                                               | FLAG_ANDROID_SYSTEM_LOGS
                                               | FLAG_ANDROID_EVENTS_LOGS
                                               | FLAG_ANDROID_RADIO_LOGS
                                               | FLAG_ANDROID_CRASH_LOGS;

    private static int logArgFlags;

    public static void addLogArgFlags(int flags) {
        setLogArgFlags(flags, flags);
    }

    public static void clearLogArgFlags(int flags) {
        setLogArgFlags(0, flags);
    }

    public static void setLogArgFlags(int flags, int mask) {
        logArgFlags = (logArgFlags & ~mask) | (flags & mask);
    }

    public static int getLogArgFlags() {
        return logArgFlags;
    }

    public static boolean needAndroidLog() {
        return ((logArgFlags & FLAG_ANDROID_LOG) != 0x0)
                | ((logArgFlags & FLAG_KERNEL_LOGS) != 0x0);
    }
}
