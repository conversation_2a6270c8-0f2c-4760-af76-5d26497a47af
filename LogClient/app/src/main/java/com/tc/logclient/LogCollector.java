/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.app.Service;
import android.content.Context;
import android.os.PowerManager;
import android.os.SystemProperties;
import android.telephony.TelephonyManager;

import com.tc.logclient.config.ManualLogConfig;
import com.tc.logclient.utils.CloseableHelper;
import com.tc.logclient.utils.Logger;
import com.tc.logclient.utils.StorageManagerWrapper;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.Arrays;

import static com.tc.logclient.Constants.COMMAND_SAVELOG;

public class LogCollector {
    private static final String TAG = "LogCollector";

    private final Context mContext;
    private final String[] mCommand;
    private OnProgressListener mOnProgressListener = null;
    private Process mProcess = null;
    private Status mStatus = Status.PENDING;

    public enum Status {
        PENDING, RUNNING,
    }

    public interface OnProgressListener {
        void onProgress(String... args);
    }

    /**
     * constructor.
     */
    public LogCollector(Context context) {
        mContext = context;
        mCommand = new String[]{"/system/bin/sh", new File(context.getFilesDir(), COMMAND_SAVELOG).getAbsolutePath(),
                getPhoneImei(context), getSdDirectory(context), getBugreportPrompt()};
        Logger.logD(TAG, "mCommand: " + Arrays.toString(mCommand));
    }

    public Status getStatus() {
        return mStatus;
    }

    private String getBugreportPrompt() {
        String str = "";
        if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_BUGREPORT) != 0) {
            str = mContext.getString(R.string.bugreport_prompt_message);
        }
        return str;
    }

    private String getPhoneImei(Context context) {
        TelephonyManager tm = (TelephonyManager) context.getSystemService(Service.TELEPHONY_SERVICE);
        String imei = tm.getImei();
        if (imei == null) {
            imei = "xxx";
        }
        return imei;
    }

    /**
     * Get SD card directory.
     * @param context the application context.
     * @return sd card path.
     */
    public static String getSdDirectory(Context context) {
        String directory = StorageManagerWrapper.getSdDirectory(context);
        if (directory == null) {
            directory = StorageManagerWrapper.getInternalSdDirectory(context);
        }
        Logger.logD(TAG, "sdcardDir: " + directory);
        return directory;
    }

    public void executeAsync() {
        executeAsync(false);
    }

    /**
     * executeAsync.
     */
    public void executeAsync(final boolean isShowLog) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                execute(isShowLog);
            }
        }).start();
    }

    public void execute() {
        execute(false);
    }

    /**
     * execute.
     */
    public void execute(final boolean isShowLog) {
        if (isShowLog) {
            Logger.logD(TAG, "=== START to collect logs === ");
        }

        CloseableHelper helper = new CloseableHelper();
        PowerManager.WakeLock wakelock = null;
        try {
            PowerManager pm = (PowerManager) mContext.getSystemService(Context.POWER_SERVICE);
            wakelock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK | PowerManager.ON_AFTER_RELEASE, "TlogCollector");
            wakelock.acquire();

            mProcess = Runtime.getRuntime().exec(mCommand);
            mStatus = Status.RUNNING;

            if (mOnProgressListener != null) {
                BufferedReader reader = helper
                        .add(new BufferedReader(new InputStreamReader(mProcess.getInputStream())));
                String line = "";
                while ((line = reader.readLine()) != null) {
                    mOnProgressListener.onProgress(line, "\n");
                }
            }

            mProcess.waitFor();
        } catch (Exception ex) {
            Logger.logE(TAG, "error occurred when save tlog", ex);
        } finally {
            helper.closeAll();
            if (wakelock != null) {
                wakelock.release();
            }
            mStatus = Status.PENDING;
        }

        if (isShowLog) {
            Logger.logD(TAG, "=== FINISH to collect logs === ");
            if (mOnProgressListener != null) {
                mOnProgressListener.onProgress("COLLECT_END");
            }
        }
    }

    /**
     * cancel.
     */
    public void cancel() {
        if (mStatus == Status.RUNNING) {
            mProcess.destroy();
            mStatus = Status.PENDING;
        }
    }

    public void setOnProgressListener(OnProgressListener listener) {
        mOnProgressListener = listener;
    }

}
