/*
 * Copyright © 2025, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.tc.logclient.config.ManualLogConfig;
import com.tc.logclient.utils.Logger;

/**
 * 接收来自EventProvider的日志控制广播
 */
public class LogControlReceiver extends BroadcastReceiver {
    
    private static final String TAG = "LogControlReceiver";
    
    // 广播Action
    private static final String ACTION_LOG_CONTROL = "vendor.thundercomm.logclient.LOG_CONTROL";
    
    // 参数常量
    private static final String EXTRA_LOG_OPERATION = "log_operation";
    private static final int LOG_OPERATION_START = 1;
    private static final int LOG_OPERATION_STOP = 0;
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            Logger.logW(TAG, "Received null intent");
            return;
        }
        
        String action = intent.getAction();
        if (TextUtils.isEmpty(action)) {
            Logger.logW(TAG, "Received intent with empty action");
            return;
        }
        
        Logger.logD(TAG, "Received broadcast with action: " + action);
        
        if (!ACTION_LOG_CONTROL.equals(action)) {
            Logger.logW(TAG, "Unknown action: " + action);
            return;
        }
        
        int operation = intent.getIntExtra(EXTRA_LOG_OPERATION, -1);
        Logger.logI(TAG, "Log operation: " + operation);
        
        switch (operation) {
            case LOG_OPERATION_START:
                startLogCollection(context);
                break;
            case LOG_OPERATION_STOP:
                stopLogCollection(context);
                break;
            default:
                Logger.logW(TAG, "Unknown operation: " + operation);
                break;
        }
    }
    
    /**
     * 开始日志收集
     */
    private void startLogCollection(Context context) {
        Logger.logI(TAG, "Starting log collection");
        
        try {
            // 设置所有日志类型为启用状态
            ManualLogConfig.clearLogArgFlags(0xFFFFFFFF); // 清除所有标志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_LOG); // 添加Android日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_KERNEL_LOGS); // 添加内核日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_TCP_DUMP); // 添加TCP dump
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_QXDM_LOGS); // 添加QXDM日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BLUETOOTH_LOGS); // 添加蓝牙日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BUGREPORT); // 添加Bugreport
            
            // 通过Intent启动LogClientActivity并触发日志收集
            Intent activityIntent = new Intent(context, LogClientActivity.class);
            activityIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            activityIntent.putExtra("auto_start_logging", true);
            context.startActivity(activityIntent);
            
            Logger.logI(TAG, "Log collection started successfully");
            
        } catch (Exception e) {
            Logger.logE(TAG, "Failed to start log collection", e);
        }
    }
    
    /**
     * 停止日志收集
     */
    private void stopLogCollection(Context context) {
        Logger.logI(TAG, "Stopping log collection");
        
        try {
            // 通过Intent通知LogClientActivity停止日志收集
            Intent activityIntent = new Intent(context, LogClientActivity.class);
            activityIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            activityIntent.putExtra("auto_stop_logging", true);
            context.startActivity(activityIntent);
            
            Logger.logI(TAG, "Log collection stopped successfully");
            
        } catch (Exception e) {
            Logger.logE(TAG, "Failed to stop log collection", e);
        }
    }
}
