/*
 * Copyright © 2025, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemProperties;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.tc.logclient.config.ManualLogConfig;
import com.tc.logclient.loggers.AndroidLoggers;
import com.tc.logclient.loggers.BluetoothLogger;
import com.tc.logclient.loggers.QxdmLoggers;
import com.tc.logclient.loggers.TcpdumpLoggers;
import com.tc.logclient.utils.Logger;
import com.tc.logclient.utils.ZipUtils;

/**
 * 静态广播接收器，接收来自EventProvider的日志控制广播
 */
public class LogControlReceiver extends BroadcastReceiver {

    private static final String TAG = "LogControlReceiver";

    // 广播Action
    private static final String ACTION_LOG_CONTROL = "vendor.thundercomm.logclient.LOG_CONTROL";

    // 参数常量
    private static final String EXTRA_LOG_OPERATION = "log_operation";
    private static final int LOG_OPERATION_START = 1;
    private static final int LOG_OPERATION_STOP = 0;

    // SharedPreferences相关
    private static final String LOGSP = "logsp";
    private static final String LOGSTATUS = "logstatus";

    // 静态变量保存状态
    private static boolean sIsLogging = false;
    private static boolean sIsRegisted = false;
    private static ShutdownBroadcastReceiver sShutdownBroadcastReceiver;

    // 日志收集器实例（静态保存）
    private static AndroidLoggers sAndroidLoggers;
    private static TcpdumpLoggers sTcpdumpLoggers;
    private static QxdmLoggers sQxdmLoggers;
    private static BluetoothLogger sBluetoothLogger;
    private static ZipUtils sZipUtils;
    private static Intent sLogServiceIntent;
    private static Handler sHandler;
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            Logger.logW(TAG, "Received null intent");
            return;
        }
        
        String action = intent.getAction();
        if (TextUtils.isEmpty(action)) {
            Logger.logW(TAG, "Received intent with empty action");
            return;
        }
        
        Logger.logD(TAG, "Received broadcast with action: " + action);
        
        if (!ACTION_LOG_CONTROL.equals(action)) {
            Logger.logW(TAG, "Unknown action: " + action);
            return;
        }
        
        int operation = intent.getIntExtra(EXTRA_LOG_OPERATION, -1);
        Logger.logI(TAG, "Log operation: " + operation);
        
        switch (operation) {
            case LOG_OPERATION_START:
                startLogCollection(context);
                break;
            case LOG_OPERATION_STOP:
                stopLogCollection(context);
                break;
            default:
                Logger.logW(TAG, "Unknown operation: " + operation);
                break;
        }
    }
    
    /**
     * 开始日志收集（参考LogClientFragment.startingLog()）
     */
    private void startLogCollection(Context context) {
        Logger.logI(TAG, "Starting log collection");

        if (getIsLogging(context)) {
            Logger.logI(TAG, "Log collection is already running");
            return;
        }

        try {
            // 初始化日志收集器（如果还没有初始化）
            initLoggers(context);

            // 设置所有日志类型为启用状态
            ManualLogConfig.clearLogArgFlags(0xFFFFFFFF); // 清除所有标志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_LOG); // 添加Android日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_KERNEL_LOGS); // 添加内核日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_TCP_DUMP); // 添加TCP dump
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_QXDM_LOGS); // 添加QXDM日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BLUETOOTH_LOGS); // 添加蓝牙日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BUGREPORT); // 添加Bugreport

            // 设置日志状态
            setIsLogging(context, true);
            updateLogStatusToSp(context, true);

            // 更新ZipUtils状态
            if (sZipUtils != null) {
                sZipUtils.updateState(ZipUtils.State.Collecting);
            }

            // 启动LogService
            if (sLogServiceIntent == null) {
                sLogServiceIntent = new Intent(context, LogService.class);
            }
            context.startService(sLogServiceIntent);

            // 启动各种日志收集器
            if (ManualLogConfig.needAndroidLog() && sAndroidLoggers != null) {
                sAndroidLoggers.startingLog();
                Logger.logI(TAG, "Android logger started");
            }

            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_TCP_DUMP) != 0x0 && sTcpdumpLoggers != null) {
                sTcpdumpLoggers.startingLog();
                Logger.logI(TAG, "Tcpdump logger started");
            }

            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_QXDM_LOGS) != 0x0 && sQxdmLoggers != null) {
                sQxdmLoggers.startingLog();
                Logger.logI(TAG, "QXDM logger started");
            }

            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_BLUETOOTH_LOGS) != 0x0 && sBluetoothLogger != null) {
                sBluetoothLogger.startingLog();
                Logger.logI(TAG, "Bluetooth logger started");
            }

            // 注册关机广播接收器
            registerShutdownReceiver(context);

            Logger.logI(TAG, "All log collection started successfully");

        } catch (Exception e) {
            Logger.logE(TAG, "Failed to start log collection", e);
        }
    }
    
    /**
     * 停止日志收集（参考LogClientFragment.stoppingLog()）
     */
    private void stopLogCollection(Context context) {
        Logger.logI(TAG, "Stopping log collection");

        if (!getIsLogging(context)) {
            Logger.logI(TAG, "Log collection is not running");
            return;
        }

        try {
            // 更新ZipUtils状态
            if (sZipUtils != null) {
                sZipUtils.updateState(ZipUtils.State.Collected);
            }

            // 停止LogService
            if (sLogServiceIntent != null) {
                context.stopService(sLogServiceIntent);
            }

            // 停止各种日志收集器
            if (sAndroidLoggers != null) {
                sAndroidLoggers.stoppingLog();
                Logger.logI(TAG, "Android logger stopped");
            }

            if (sTcpdumpLoggers != null) {
                sTcpdumpLoggers.stoppingLog();
                Logger.logI(TAG, "Tcpdump logger stopped");
            }

            if (sQxdmLoggers != null) {
                sQxdmLoggers.stoppingLog();
                Logger.logI(TAG, "QXDM logger stopped");
            }

            if (sBluetoothLogger != null) {
                sBluetoothLogger.stoppingLog();
                Logger.logI(TAG, "Bluetooth logger stopped");
            }

            // 保存日志
            if (sZipUtils != null) {
                sZipUtils.saveLog();
                Logger.logI(TAG, "Log save triggered");
            }

            // 注销关机广播接收器
            unregisterShutdownReceiver(context);

            // 设置日志状态
            setIsLogging(context, false);
            updateLogStatusToSp(context, false);

            Logger.logI(TAG, "All log collection stopped successfully");

        } catch (Exception e) {
            Logger.logE(TAG, "Failed to stop log collection", e);
        }
    }

    /**
     * 初始化日志收集器
     */
    private void initLoggers(Context context) {
        if (sAndroidLoggers == null) {
            sAndroidLoggers = new AndroidLoggers(context);
        }
        if (sTcpdumpLoggers == null) {
            sTcpdumpLoggers = new TcpdumpLoggers(context);
        }
        if (sQxdmLoggers == null) {
            sQxdmLoggers = new QxdmLoggers(context);
        }
        if (sBluetoothLogger == null) {
            sBluetoothLogger = new BluetoothLogger(context);
        }
        if (sHandler == null) {
            sHandler = new Handler(Looper.getMainLooper());
        }
        if (sZipUtils == null) {
            sZipUtils = new ZipUtils(context, sHandler);
            sZipUtils.loadAssets();
        }
    }

    /**
     * 获取日志状态
     */
    private boolean getIsLogging(Context context) {
        SharedPreferences sp = context.getSharedPreferences(LOGSP, Context.MODE_PRIVATE);
        return sp.getBoolean(LOGSTATUS, false);
    }

    /**
     * 设置日志状态
     */
    private void setIsLogging(Context context, boolean status) {
        sIsLogging = status;
    }

    /**
     * 更新日志状态到SharedPreferences
     */
    private void updateLogStatusToSp(Context context, boolean status) {
        SharedPreferences sp = context.getSharedPreferences(LOGSP, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        editor.putBoolean(LOGSTATUS, status);
        editor.apply();
    }

    /**
     * 注册关机广播接收器
     */
    private void registerShutdownReceiver(Context context) {
        if (!sIsRegisted) {
            IntentFilter shutdownFilter = new IntentFilter();
            shutdownFilter.addAction(Intent.ACTION_SHUTDOWN);
            shutdownFilter.addAction(Intent.ACTION_REBOOT);
            sShutdownBroadcastReceiver = new ShutdownBroadcastReceiver();
            context.registerReceiver(sShutdownBroadcastReceiver, shutdownFilter);
            sIsRegisted = true;
            Logger.logI(TAG, "Shutdown receiver registered");
        }
    }

    /**
     * 注销关机广播接收器
     */
    private void unregisterShutdownReceiver(Context context) {
        if (sIsRegisted && sShutdownBroadcastReceiver != null) {
            try {
                context.unregisterReceiver(sShutdownBroadcastReceiver);
                sIsRegisted = false;
                sShutdownBroadcastReceiver = null;
                Logger.logI(TAG, "Shutdown receiver unregistered");
            } catch (Exception ex) {
                sIsRegisted = false;
                Logger.logW(TAG, "Broadcast unregister failed:" + ex.toString());
            }
        }
    }

    /**
     * 关机广播接收器
     */
    public static class ShutdownBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            Logger.logI(TAG, "Shutdown broadcast received, stopping log collection");
            // 在关机时停止日志收集
            LogControlReceiver receiver = new LogControlReceiver();
            receiver.stopLogCollection(context);
        }
    }
}
