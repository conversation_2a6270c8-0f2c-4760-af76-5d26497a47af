/*
 * Copyright © 2025, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemProperties;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.tc.logclient.config.ManualLogConfig;
import com.tc.logclient.loggers.AndroidLoggers;
import com.tc.logclient.loggers.BluetoothLogger;
import com.tc.logclient.loggers.QxdmLoggers;
import com.tc.logclient.loggers.TcpdumpLoggers;
import com.tc.logclient.utils.Logger;
import com.tc.logclient.utils.ZipUtils;

/**
 * Static broadcast receiver for handling log control broadcasts from EventProvider.
 */
public class LogControlReceiver extends BroadcastReceiver {

    private static final String TAG = "LogControlReceiver";

    // Broadcast action
    private static final String ACTION_LOG_CONTROL = "vendor.thundercomm.logclient.LOG_CONTROL";

    // Parameter constants
    private static final String EXTRA_LOG_OPERATION = "log_operation";
    private static final int LOG_OPERATION_START = 1;
    private static final int LOG_OPERATION_STOP = 0;

    // SharedPreferences related
    private static final String LOGSP = "logsp";
    private static final String LOGSTATUS = "logstatus";

    // Static variables to save state
    private static boolean sIsLogging = false;

    // Log collector instances (static saved)
    private static AndroidLoggers sAndroidLoggers;
    private static TcpdumpLoggers sTcpdumpLoggers;
    private static QxdmLoggers sQxdmLoggers;
    private static BluetoothLogger sBluetoothLogger;
    private static ZipUtils sZipUtils;
    private static Intent sLogServiceIntent;
    private static Handler sHandler;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            Logger.logW(TAG, "Received null intent");
            return;
        }

        String action = intent.getAction();
        if (TextUtils.isEmpty(action)) {
            Logger.logW(TAG, "Received intent with empty action");
            return;
        }

        Logger.logD(TAG, "Received broadcast with action: " + action);

        // Handle shutdown broadcast
        if (Intent.ACTION_SHUTDOWN.equals(action)
                || Intent.ACTION_REBOOT.equals(action)) {
            Logger.logI(TAG, "Shutdown/Reboot broadcast received, stopping log collection");
            handleShutdown(context);
            return;
        }

        // Handle log control broadcast
        if (!ACTION_LOG_CONTROL.equals(action)) {
            Logger.logW(TAG, "Unknown action: " + action);
            return;
        }

        // Initialize ZipUtils
        initZipUtils(context);

        int operation = intent.getIntExtra(EXTRA_LOG_OPERATION, -1);
        Logger.logI(TAG, "Log operation: " + operation);

        switch (operation) {
            case LOG_OPERATION_START:
                startLogCollection(context);
                break;
            case LOG_OPERATION_STOP:
                stopLogCollection(context);
                break;
            default:
                Logger.logW(TAG, "Unknown operation: " + operation);
                break;
        }
    }

    /**
     * Start log collection (refer to LogClientFragment.startingLog()).
     */
    private void startLogCollection(Context context) {
        Logger.logI(TAG, "Starting log collection");

        if (getIsLogging(context)) {
            Logger.logI(TAG, "Log collection is already running");
            return;
        }

        try {
            // Initialize log collectors (if not initialized yet)
            initLoggers(context);

            // Set all log types to enabled state
            final int allFlags = 0xFFFFFFFF;
            ManualLogConfig.clearLogArgFlags(allFlags); // Clear all flags
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_LOG); // Add Android log
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_KERNEL_LOGS); // Add kernel logs
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_TCP_DUMP); // Add TCP dump
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_QXDM_LOGS); // Add QXDM logs
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BLUETOOTH_LOGS); // Add Bluetooth logs
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BUGREPORT); // Add Bugreport

            // Set log status
            setIsLogging(context, true);
            updateLogStatusToSp(context, true);

            if (sZipUtils != null) {
                sZipUtils.updateState(ZipUtils.State.Collecting);
            }

            // Start LogService
            if (sLogServiceIntent == null) {
                sLogServiceIntent = new Intent(context, LogService.class);
            }
            context.startService(sLogServiceIntent);

            // Start various log collectors
            if (ManualLogConfig.needAndroidLog()
                    && sAndroidLoggers != null) {
                sAndroidLoggers.startingLog();
                Logger.logI(TAG, "Android logger started");
            }

            final int tcpDumpFlag = ManualLogConfig.getLogArgFlags()
                    & ManualLogConfig.FLAG_TCP_DUMP;
            if (tcpDumpFlag != 0
                    && sTcpdumpLoggers != null) {
                sTcpdumpLoggers.startingLog();
                Logger.logI(TAG, "Tcpdump logger started");
            }

            final int qxdmFlag = ManualLogConfig.getLogArgFlags()
                    & ManualLogConfig.FLAG_QXDM_LOGS;
            if (qxdmFlag != 0
                    && sQxdmLoggers != null) {
                sQxdmLoggers.startingLog();
                Logger.logI(TAG, "QXDM logger started");
            }

            final int bluetoothFlag = ManualLogConfig.getLogArgFlags()
                    & ManualLogConfig.FLAG_BLUETOOTH_LOGS;
            if (bluetoothFlag != 0
                    && sBluetoothLogger != null) {
                sBluetoothLogger.startingLog();
                Logger.logI(TAG, "Bluetooth logger started");
            }

            // Register shutdown broadcast receiver - skip in static broadcast
            // registerShutdownReceiver(context);

            Logger.logI(TAG, "All log collection started successfully");

        } catch (Exception exception) {
            Logger.logE(TAG, "Failed to start log collection", exception);
        }
    }

    /**
     * Stop log collection (refer to LogClientFragment.stoppingLog()).
     */
    private void stopLogCollection(Context context) {
        Logger.logI(TAG, "Stopping log collection");

        if (!getIsLogging(context)) {
            Logger.logI(TAG, "Log collection is not running");
            return;
        }

        try {
            if (sZipUtils != null) {
                sZipUtils.updateState(ZipUtils.State.Collected);
            }

            // Stop LogService
            if (sLogServiceIntent != null) {
                context.stopService(sLogServiceIntent);
            }

            // Stop various log collectors
            if (sAndroidLoggers != null) {
                sAndroidLoggers.stoppingLog();
                Logger.logI(TAG, "Android logger stopped");
            }

            if (sTcpdumpLoggers != null) {
                sTcpdumpLoggers.stoppingLog();
                Logger.logI(TAG, "Tcpdump logger stopped");
            }

            if (sQxdmLoggers != null) {
                sQxdmLoggers.stoppingLog();
                Logger.logI(TAG, "QXDM logger stopped");
            }

            if (sBluetoothLogger != null) {
                sBluetoothLogger.stoppingLog();
                Logger.logI(TAG, "Bluetooth logger stopped");
            }

            if (sZipUtils != null) {
                sZipUtils.saveLog();
                Logger.logI(TAG, "Log save triggered");
            }

            // Unregister shutdown broadcast receiver - skip in static broadcast
            // unregisterShutdownReceiver(context);

            // Set log status
            setIsLogging(context, false);
            updateLogStatusToSp(context, false);

            Logger.logI(TAG, "All log collection stopped successfully");

        } catch (Exception exception) {
            Logger.logE(TAG, "Failed to stop log collection", exception);
        }
    }

    /**
     * Initialize log collectors.
     */
    private void initLoggers(Context context) {
        if (sAndroidLoggers == null) {
            sAndroidLoggers = new AndroidLoggers(context);
        }
        if (sTcpdumpLoggers == null) {
            sTcpdumpLoggers = new TcpdumpLoggers(context);
        }
        if (sQxdmLoggers == null) {
            sQxdmLoggers = new QxdmLoggers(context);
        }
        if (sBluetoothLogger == null) {
            sBluetoothLogger = new BluetoothLogger(context);
        }
    }

    /**
     * Get log status.
     */
    private boolean getIsLogging(Context context) {
        SharedPreferences sp = context.getSharedPreferences(LOGSP, Context.MODE_PRIVATE);
        return sp.getBoolean(LOGSTATUS, false);
    }

    /**
     * Set log status.
     */
    private void setIsLogging(Context context, boolean status) {
        sIsLogging = status;
    }

    /**
     * Update log status to SharedPreferences.
     */
    private void updateLogStatusToSp(Context context, boolean status) {
        SharedPreferences sp = context.getSharedPreferences(LOGSP, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        editor.putBoolean(LOGSTATUS, status);
        editor.apply();
    }

    /**
     * Static method: Initialize ZipUtils instance to avoid repeated creation.
     */
    private static void initZipUtils(Context context) {
        if (sZipUtils == null) {
            if (sHandler == null) {
                sHandler = new Handler(Looper.getMainLooper());
            }
            sZipUtils = new ZipUtils(context, sHandler);
            sZipUtils.loadAssets();
            sZipUtils.updateState(ZipUtils.State.Idle);
            Logger.logI(TAG, "ZipUtils initialized");
        }
    }

    /**
     * Handle shutdown event.
     */
    private void handleShutdown(Context context) {
        Logger.logI(TAG, "Handling shutdown, stopping log collection");

        try {
            // Stop various log collectors
            if (sAndroidLoggers != null) {
                sAndroidLoggers.stoppingLog();
                Logger.logI(TAG, "Android logger stopped on shutdown");
            }

            if (sTcpdumpLoggers != null) {
                sTcpdumpLoggers.stoppingLog();
                Logger.logI(TAG, "Tcpdump logger stopped on shutdown");
            }

            if (sQxdmLoggers != null) {
                sQxdmLoggers.stoppingLog();
                Logger.logI(TAG, "QXDM logger stopped on shutdown");
            }

            if (sBluetoothLogger != null) {
                sBluetoothLogger.stoppingLog();
                Logger.logI(TAG, "Bluetooth logger stopped on shutdown");
            }

            // Stop LogService
            if (sLogServiceIntent != null) {
                context.stopService(sLogServiceIntent);
            }

            // Set log status
            setIsLogging(context, false);
            updateLogStatusToSp(context, false);

            Logger.logI(TAG, "Shutdown handling completed");

        } catch (Exception exception) {
            Logger.logE(TAG, "Failed to handle shutdown", exception);
        }
    }
}
