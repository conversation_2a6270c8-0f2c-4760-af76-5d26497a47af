/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.loggers;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

public abstract class BaseLoggers {
    public static final String TAG = "AndroidLoggers";

    Context mContext;
    // Get the share preferences
    public SharedPreferences mSharePreferences;

    private Boolean mRunning = false;

    /**
     * Create a new Loggers.
     *
     * @param context the application context.
     */
    public BaseLoggers(Context context) {
        mContext = context;
        mSharePreferences = PreferenceManager.getDefaultSharedPreferences(mContext);
    }

    /**
     * Starting take log.
     */
    public void startingLog() {
        synchronized (mRunning) {
            if (!mRunning) {
                start();
                mRunning = true;
            }
        }
    }

    /**
     * Stopping take log.
     */
    public void stoppingLog() {
        synchronized (mRunning) {
            if (mRunning) {
                stop();
            }
            mRunning = false;
        }
    }

    /**
     * get the args file path.
     *
     * @return file path.
     */
    protected String getArgsFilePath() {
        return null;
    }

    protected abstract boolean start();

    protected abstract boolean stop();

    /**
     * Check status.
     *
     * @return true if log is taking.
     */
    public abstract boolean isTakingLogging();
}
