package com.tc.logclient.utils;

import android.net.LocalSocket;
import android.net.LocalSocketAddress;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;

public class SocketUtils {
    private static final String TAG = "SocketUtils";

    private LocalSocket mLocalSocket = null;
    private OutputStream mOutputStream;
    private BufferedWriter mBufferedWriter = null;
    private InputStream mInputStream;
    private BufferedReader mBufferedReader = null;

    LocalSocketAddress mAddress;

    private String mSocketName = null;

    /**
     * Set a socket name.
     */
    public SocketUtils(String name) {
        mSocketName = name;
    }

    /**
     * Create a socket
     * @throws IOException exception when use this api.
     */
    public void createSocket() throws IOException {
        mLocalSocket = new LocalSocket();
        mAddress = new LocalSocketAddress(mSocketName,
                LocalSocketAddress.Namespace.ABSTRACT);

        mLocalSocket.connect(mAddress);
    }

    /**
     * Send config.
     * @param cmd String need to send.
     * @throws IOException exception when use this api.
     */
    public void sendConfig(String cmd) throws IOException {
        // Logger.logD(TAG, "sendConfig : " + cmd);
        mOutputStream = mLocalSocket.getOutputStream();
        mBufferedWriter = new BufferedWriter(
                new OutputStreamWriter(mOutputStream));

        mBufferedWriter.write(cmd);
        mBufferedWriter.flush();
    }

    /**
     * Get config.
     * @throws IOException exception when use this api.
     */
    public String getConfig() throws IOException {
        String result = "";
        mInputStream = mLocalSocket.getInputStream();
        mBufferedReader = new BufferedReader(
                new InputStreamReader(mInputStream));

        result =  mBufferedReader.readLine();
        // Logger.logD(TAG, "getConfig : " + result);
        return result;
    }

    /**
     * Close a socket
     * @throws IOException exception when use this api.
     */
    public void closeSocket() throws IOException {
        if (mOutputStream != null) {
            mOutputStream.close();
            mOutputStream = null;
        }
        if (mInputStream != null) {
            mInputStream.close();
            mInputStream = null;
        }
        if (mLocalSocket != null) {
            mLocalSocket.close();
            mLocalSocket = null;
        }
    }
}
