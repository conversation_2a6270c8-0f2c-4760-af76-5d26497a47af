/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.loggers;

import android.content.Context;
import android.os.SystemProperties;

import com.tc.logclient.R;
import com.tc.logclient.utils.Logger;
import com.tc.logclient.utils.TlogArgsUtils;

public class QxdmLoggers extends BaseLoggers {
    public static final String TAG = "QxdmLoggers";

    Context mContext;

    // This property is start/stop service tlog_tcpdump.
    private static final String SYS_TLOG_QXDM_ACTIVE = "sys.tlog.qxdm.active";
    // This property is set for tcpdump's parameter.
    private static final String SYS_TLOG_QXDM_ARG = "sys.tlog.qxdm.arg";
    // This is default value for tcpdump.
    // 100M for 5 files
    private static final String TLOG_QXDM_DEFAULT_ARG = "-n 5 -s 100 -c";

    public QxdmLoggers(Context context) {
        super(context);
        mContext = context;
    }

    /**
     * Starting tcpdump.
     */
    @Override
    protected boolean start() {
        Logger.logD(TAG,"startingLog");
        String qxdmArg = mSharePreferences.getString(mContext.getString(R.string.qxdm_parameter_key),
                TLOG_QXDM_DEFAULT_ARG);
        //SystemProperties.set(SYS_TLOG_QXDM_ARG, qxdmArg);
        int result = TlogArgsUtils.setTlogArgs(getArgsFilePath(), qxdmArg);
        if (result < 0) {
            Logger.logW(TAG, "qxdm save params error.");
        }
        SystemProperties.set(SYS_TLOG_QXDM_ACTIVE, "1");
        return true;
    }

    @Override
    protected String getArgsFilePath() {
        return "/data/vendor/tlog/args/qxdm.arg";
    }

    /**
     * Stopping tcpdump.
     */
    @Override
    protected boolean stop() {
        Logger.logD(TAG,"stopingLog");
        SystemProperties.set(SYS_TLOG_QXDM_ACTIVE, "0");
        return true;
    }

    /**
     * Check QXDM logger status.
     *
     * @return true if log is taking.
     */
    @Override
    public boolean isTakingLogging() {
        return true;
    }
}

