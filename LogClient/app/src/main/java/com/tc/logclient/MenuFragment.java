package com.tc.logclient;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.SystemProperties;
import android.preference.CheckBoxPreference;
import android.preference.EditTextPreference;
import android.preference.Preference;
import android.preference.PreferenceFragment;
import android.preference.PreferenceGroup;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;

import com.tc.logclient.config.MenuSettingConfig;
import com.tc.logclient.utils.Logger;
import com.tc.logclient.utils.SocketUtils;

import java.io.IOException;

public class MenuFragment extends PreferenceFragment implements
        SharedPreferences.OnSharedPreferenceChangeListener {
    private static final String TAG = "MenuFragment";

    private static final int INDEX_PERSIST_ENABLE = 0;
    private static final int INDEX_PERSIST_MAXFILECOUNT = 1;
    private static final int INDEX_PERSIST_FILESIZEBYTES = 2;
    private static final int INDEX_PERSIST_SEPARATE = 3;
    private static final int INDEX_MANUAL_MAXFILECOUNT = 4;
    private static final int INDEX_MANUAL_FILESIZEBYTES = 5;
    private static final int INDEX_MANUAL_SEPARATE = 6;

    // This property is start/stop service tlogFatal.
    private static final String PERSIST_SYS_TLOG_ACTIVE = "persist.sys.tlog.active";

    MenuSettingConfig mMenuSettingConfig;

    public static final int GET_PREFERENCE_VALUE = 1;
    public static final int UPDATE_PREFERENCE = 2;
    public static final String UPDATE_CONFIG = "config";

    SocketUtils mSocketUtils;
    PreferenceUpdateHandler mPreferenceUpdateHandler;

    @Override
    public void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        addPreferencesFromResource(R.xml.menu_settings_preference);
        setHasOptionsMenu(true);

        loadPersistLogActiveStatus();

        mMenuSettingConfig = new MenuSettingConfig();
        mSocketUtils = new SocketUtils("tlogd_persist");
        mPreferenceUpdateHandler = new PreferenceUpdateHandler();
        mPreferenceUpdateHandler.sendEmptyMessage(GET_PREFERENCE_VALUE);
    }

    private void loadPersistLogActiveStatus() {
        String persist = SystemProperties.get(PERSIST_SYS_TLOG_ACTIVE, "0");
        CheckBoxPreference preference =
            (CheckBoxPreference) findPreference(getString(R.string.persist_parameter_key));
        preference.setChecked(persist.equals("1") ? true : false);
    }

    @Override
    public void onResume() {
        super.onResume();
        getPreferenceScreen().getSharedPreferences().registerOnSharedPreferenceChangeListener(this);
    }

    @Override
    public void onPause() {
        super.onPause();
        getPreferenceScreen().getSharedPreferences().unregisterOnSharedPreferenceChangeListener(this);
    }

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
        if (key.equals(getString(R.string.persist_parameter_key))) {
            CheckBoxPreference preference = (CheckBoxPreference)findPreference(key);
            if (preference.isChecked()) {
                mMenuSettingConfig.setPersistEnable(true);
                SystemProperties.set(PERSIST_SYS_TLOG_ACTIVE, "1");
            } else {
                mMenuSettingConfig.setPersistEnable(false);
                SystemProperties.set(PERSIST_SYS_TLOG_ACTIVE, "0");
            }
        } else if (key.equals(getString(R.string.persist_max_number_key))) {
            EditTextPreference preference = (EditTextPreference)findPreference(key);
            mMenuSettingConfig.setPersistMaxFileCount(preference.getText());
            preference.setSummary(preference.getText());
        } else if (key.equals(getString(R.string.persist_file_size_key))) {
            EditTextPreference preference = (EditTextPreference)findPreference(key);
            mMenuSettingConfig.setPersistFileSizeBytes(preference.getText());
            preference.setSummary(preference.getText());
        } else if (key.equals(getString(R.string.persist_separate_kernel_key))) {
            CheckBoxPreference preference = (CheckBoxPreference)findPreference(key);
            if (preference.isChecked()) {
                mMenuSettingConfig.setPersistSeparateKernel(true);
            } else {
                mMenuSettingConfig.setPersistSeparateKernel(false);
            }
        } else if (key.equals(getString(R.string.persist_separate_event_key))) {
            CheckBoxPreference preference = (CheckBoxPreference)findPreference(key);
            if (preference.isChecked()) {
                mMenuSettingConfig.setPersistSeparateEvent(true);
            } else {
                mMenuSettingConfig.setPersistSeparateEvent(false);
            }
        } else if (key.equals(getString(R.string.manual_max_number_key))) {
            EditTextPreference preference = (EditTextPreference)findPreference(key);
            mMenuSettingConfig.setManualMaxFileCount(preference.getText());
            preference.setSummary(preference.getText());
        } else if (key.equals(getString(R.string.manual_file_size_key))) {
            EditTextPreference preference = (EditTextPreference)findPreference(key);
            mMenuSettingConfig.setManualFileSizeBytes(preference.getText());
            preference.setSummary(preference.getText());
        } else if (key.equals(getString(R.string.manual_separate_kernel_key))) {
            CheckBoxPreference preference = (CheckBoxPreference)findPreference(key);
            if (preference.isChecked()) {
                mMenuSettingConfig.setManualSeparateKernel(true);
            } else {
                mMenuSettingConfig.setManualSeparateKernel(false);
            }
        } else if (key.equals(getString(R.string.manual_separate_event_key))) {
            CheckBoxPreference preference = (CheckBoxPreference)findPreference(key);
            if (preference.isChecked()) {
                mMenuSettingConfig.setManualSeparateEvent(true);
            } else {
                mMenuSettingConfig.setManualSeparateEvent(false);
            }
        } else if (key.equals(getString(R.string.ramdump_settings_key))) {
            CheckBoxPreference preference = (CheckBoxPreference)findPreference(key);
            mMenuSettingConfig.enableRamdump(preference.isChecked());
        }
    }

    private void updateSummary(PreferenceGroup group) {
        for (int i = 0; i < group.getPreferenceCount(); i++) {
            Preference preference = group.getPreference(i);
            if (preference instanceof PreferenceGroup) {
                updateSummary((PreferenceGroup)preference);
            } else {
                updateSummary(preference);
            }
        }
    }

    private void updateSummary(Preference preference) {
        if (preference instanceof EditTextPreference) {
            if (preference.getKey().equals(getString(R.string.persist_max_number_key))) {
                ((EditTextPreference) preference).setText(
                        mMenuSettingConfig.getPersistMaxFileCount());
            } else if (preference.getKey().equals(getString(R.string.persist_file_size_key))) {
                ((EditTextPreference) preference).setText(
                        mMenuSettingConfig.getPersistFileSizeBytes());
            } else if (preference.getKey().equals(getString(R.string.manual_max_number_key))) {
                ((EditTextPreference) preference).setText(
                        mMenuSettingConfig.getManualMaxFileCount());
            } else if (preference.getKey().equals(getString(R.string.manual_file_size_key))) {
                ((EditTextPreference) preference).setText(
                        mMenuSettingConfig.getManualFileSizeBytes());
            }
            String sum = ((EditTextPreference) preference).getText();

            preference.setSummary(sum);
        } else if (preference instanceof CheckBoxPreference) {
            if (preference.getKey().equals(getString(R.string.persist_parameter_key))) {
                ((CheckBoxPreference) preference).setChecked(
                        mMenuSettingConfig.getIsPersistEnable());
            } else if (preference.getKey().equals(getString(R.string.persist_separate_kernel_key))) {
                ((CheckBoxPreference) preference).setChecked(
                        mMenuSettingConfig.getIsPersistSeparateKernel());
            } else if (preference.getKey().equals(getString(R.string.persist_separate_event_key))) {
                ((CheckBoxPreference) preference).setChecked(
                        mMenuSettingConfig.getIsPersistSeparateEvent());
            } else if (preference.getKey().equals(getString(R.string.manual_separate_kernel_key))) {
                ((CheckBoxPreference) preference).setChecked(
                        mMenuSettingConfig.getIsManualSeparateKernel());
            } else if (preference.getKey().equals(getString(R.string.manual_separate_event_key))) {
                ((CheckBoxPreference) preference).setChecked(
                        mMenuSettingConfig.getIsManualSeparateEvent());
            } else if (preference.getKey().equals(getString(R.string.ramdump_settings_key))) {
                ((CheckBoxPreference) preference).setChecked(
                        mMenuSettingConfig.isRamdumpEnabled());
            }
        }
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.settings_menu, menu);
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.save_settings:
                saveConfiguration(mMenuSettingConfig.toString());
                break;
            default:
                break;
        }
        return true;
    }

    private void saveConfiguration(String config) {
        try {
            mSocketUtils.createSocket();
            mSocketUtils.sendConfig(config);
            mSocketUtils.closeSocket();
        } catch (IOException ex) {
            Logger.logE(TAG, "Save configuration socket connect error:" + ex.toString());
        }
    }

    private void getConfiguration() {
        try {
            mSocketUtils.createSocket();
            mSocketUtils.sendConfig(MenuSettingConfig.CONFIG_COMMAND_GET);
            mPreferenceUpdateHandler.post(new Runnable() {
                @Override
                public void run() {
                    try {
                        String config = mSocketUtils.getConfig();
                        Message msg = new Message();
                        msg.what = UPDATE_PREFERENCE;
                        Bundle bundle = new Bundle();
                        bundle.putString(UPDATE_CONFIG, config);
                        msg.setData(bundle);
                        mPreferenceUpdateHandler.sendMessage(msg);
                        mSocketUtils.closeSocket();
                    } catch (Exception ex) {
                        Logger.logE(TAG, "Get configuration socket connect error:" + ex.toString());
                    }
                }
            });
        } catch (IOException ex) {
            Logger.logE(TAG, "Get configuration socket connect error:" + ex.toString());
        }
    }

    private void updatePreference(String config) {
        String[] value = config.split(MenuSettingConfig.CONFIG_DIVIDER);

        String persist;
        persist = SystemProperties.get(PERSIST_SYS_TLOG_ACTIVE, "0");
        mMenuSettingConfig.setPersistEnable(
                persist.equals("1") ? true : false);
        mMenuSettingConfig.setPersistMaxFileCount(
                value[INDEX_PERSIST_MAXFILECOUNT].trim());
        mMenuSettingConfig.setPersistFileSizeBytes(
                value[INDEX_PERSIST_FILESIZEBYTES].trim());
        mMenuSettingConfig.setPersistSeparateKernel(
                value[INDEX_PERSIST_SEPARATE].contains(
                        MenuSettingConfig.SEPARATE_KERNEL) ? true : false);
        mMenuSettingConfig.setPersistSeparateEvent(
                value[INDEX_PERSIST_SEPARATE].contains(
                        MenuSettingConfig.SEPARATE_EVENT) ? true : false);

        mMenuSettingConfig.setManualMaxFileCount(
                value[INDEX_MANUAL_MAXFILECOUNT].trim());
        mMenuSettingConfig.setManualFileSizeBytes(
                value[INDEX_MANUAL_FILESIZEBYTES].trim());
        mMenuSettingConfig.setManualSeparateKernel(
                value[INDEX_MANUAL_SEPARATE].contains(
                        MenuSettingConfig.SEPARATE_KERNEL) ? true : false);
        mMenuSettingConfig.setManualSeparateEvent(
                value[INDEX_MANUAL_SEPARATE].contains(
                        MenuSettingConfig.SEPARATE_EVENT) ? true : false);

        updateSummary(getPreferenceScreen());
    }

    private class PreferenceUpdateHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case GET_PREFERENCE_VALUE:
                    getConfiguration();
                    break;
                case UPDATE_PREFERENCE:
                    Bundle bundle = msg.getData();
                    String config = bundle.getString(UPDATE_CONFIG);
                    updatePreference(config);
                    break;
                default:
                    break;
            }
        }
    }
}
