package com.tc.logclient.utils;

import android.os.FileUtils;
import android.text.TextUtils;
import android.util.Log;

import java.io.File;
import java.io.IOException;

public class SysfsRWer {
    private static final String TAG = SysfsRWer.class.getSimpleName();

    private static final String RAMDUMP_ENABLE =
            "/sys/module/msm_poweroff/parameters/panic_download";

    public static boolean isRamdumpEnabled() {
        String enableRamdump = readFileString(RAMDUMP_ENABLE);
        return "1".equals(enableRamdump);
    }

    public static void enableRamdump(boolean enable) {
        writeFileString(RAMDUMP_ENABLE, enable ? "1" : "0");
    }

    /**
     * Write a text to file.
     */
    public static void writeFileString(String path, String str) {
        try {
            if (!TextUtils.isEmpty(path)) {
                FileUtils.stringToFile(path, str);
            }
        } catch (Exception ex) {
            Log.e(TAG, "Couldn't write " + path, ex);
        }
    }

    /**
     * Read a text file into a String.
     */
    public static String readFileString(String path) {
        if (!TextUtils.isEmpty(path)) {
            try {
                String value = FileUtils.readTextFile(new File(path), 0, null);
                if (!TextUtils.isEmpty(value)) {
                    return value.trim();
                }
            } catch (IOException ioe) {
                Log.e(TAG, "Couldn't read " + path, ioe);
            }
        }
        return "";
    }
}
