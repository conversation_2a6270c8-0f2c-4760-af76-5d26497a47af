/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.loggers;

import android.content.Context;
import android.os.SystemProperties;

public class BluetoothLogger extends BaseLoggers {
    public static final String TAG = "BluetoothLogger";

    Context mContext;

    // This property is start/stop service tlog bluetooth.
    private static final String SYS_TLOG_BLUETOOTH_ACTIVE = "sys.tlog.bluetooth.active";

    /**
     * Create a new Loggers.
     *
     * @param context the application context.
     */
    public BluetoothLogger(Context context) {
        super(context);
        mContext = context;
    }

    /**
     * Starting bluetooth logger.
     */
    @Override
    protected boolean start() {
        SystemProperties.set(SYS_TLOG_BLUETOOTH_ACTIVE, "1");
        return true;
    }

    /**
     * Stopping bluetooth logger.
     */
    @Override
    protected boolean stop() {
        SystemProperties.set(SYS_TLOG_BLUETOOTH_ACTIVE, "0");
        return true;
    }

    /**
     * Check bluetooth logger status.
     *
     * @return true if log is taking.
     */
    @Override
    public boolean isTakingLogging() {
        return true;
    }
}