/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.utils;

import java.io.Closeable;
import java.io.IOException;
import java.util.Stack;

public class CloseableHelper {

    private Stack<Closeable> objects = new Stack<Closeable>();

    public <T extends Closeable> T add(T item) {
        objects.push(item);
        return item;
    }

    /**
     * closeAll.
     */
    public void closeAll() {
        while (!objects.empty()) {
            Closeable obj = objects.pop();
            try {
                obj.close();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

}
