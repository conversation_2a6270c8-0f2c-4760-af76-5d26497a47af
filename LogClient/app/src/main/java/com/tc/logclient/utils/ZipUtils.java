package com.tc.logclient.utils;

import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.format.DateFormat;

import com.tc.logclient.LogClientFragment;
import com.tc.logclient.LogCollector;

import java.io.IOException;

import static com.tc.logclient.Constants.COMMAND_SAVELOG;

public class ZipUtils {
    private static final String TAG = "ZipUtils";

    Context mContext;
    private SaveLogTask mSaveLogTask = null;

    private Handler mUiHandler;

    private ZipUtils.State mState = ZipUtils.State.Idle;

    public enum State {
        Idle,
        Collecting,
        Collected,
        Saving,
        Saved,
        Cancel,
    }

    public ZipUtils(Context context, Handler handler) {
        mContext = context;
        mUiHandler = handler;
    }

    /**
     * Get zip state.
     */
    public ZipUtils.State getState() {
        return mState;
    }

    /**
     * Whether zip is running.
     */
    public boolean isRunning() {
        return (mState == State.Saving || mState == State.Collecting);
    }

    /**
     * Load sh from assets.
     */
    public void loadAssets() {
        try {
            ResourceUtils.loadAssetsFile(mContext, COMMAND_SAVELOG);
        } catch (IOException ex) {
            Logger.logE(TAG, "loadAssets ex:", ex);
        }
    }

    /**
     * Create a task to save log.
     */
    public void saveLog() {
        if (mSaveLogTask != null && mSaveLogTask.getStatus() == AsyncTask.Status.RUNNING) {
            return;
        }

        mSaveLogTask = new SaveLogTask();
        mSaveLogTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    /**
     * Update the state.
     */
    public void updateState(ZipUtils.State state) {
        mState = state;
        String message = "";
        switch (state) {
            case Idle:
                break;
            case Saved:
                message = "=== FINISH to save logs " + getDateText() + " === ";
                break;
            case Cancel:
                message = "=== CANCEL to save logs " + getDateText() + " === ";
                break;
            case Collecting:
                message = "=== Start collect log " + getDateText() + " === ";
                break;
            case Saving:
                message = "=== START to save logs " + getDateText() + " === ";
                break;
            case Collected:
                message = "=== Stop collect log " + getDateText() + " === ";
                break;
            default:
                return;
        }
        Logger.logD(TAG, "updateState:" + message);
        updateNotifyView(message + "\n");
    }

    /**
     * stop save log task.
     */
    public void stopTask() {
        if ((mSaveLogTask != null) && (mSaveLogTask.getStatus() == AsyncTask.Status.RUNNING)) {
            mSaveLogTask.cancel(true);
        }
    }

    private void updateNotifyView(String message) {
        Message msg = new Message();
        msg.what = LogClientFragment.UPDATE_NOTIFY_TEXT;
        Bundle bundle = new Bundle();
        bundle.putString(LogClientFragment.UPDATE_NOTIFY_STRING, message);
        msg.setData(bundle);
        mUiHandler.sendMessage(msg);
    }

    private CharSequence getDateText() {
        return DateFormat.format("MM/dd kk:mm:ss", System.currentTimeMillis());
    }

    private class SaveLogTask extends AsyncTask<Void, String, Void> {
        private LogCollector mCollector;

        @Override
        protected void onPreExecute() {
            mCollector = new LogCollector(mContext);
            mCollector.setOnProgressListener(mOnProgressListener);

            updateState(ZipUtils.State.Saving);
        }

        @Override
        protected Void doInBackground(Void... params) {
            mCollector.executeAsync();
            final int sleepTimeout = 100;
            do {
                try {
                    Thread.sleep(sleepTimeout);
                    if (isCancelled()) {
                        mCollector.cancel();
                        break;
                    }
                } catch (InterruptedException ex) {
                    Logger.logE(TAG, "interrupt to collect logs");
                    mCollector.cancel();
                    break;
                }
            } while (mCollector.getStatus() == LogCollector.Status.RUNNING);
            return null;
        }

        private LogCollector.OnProgressListener mOnProgressListener = new LogCollector.OnProgressListener() {
            @Override
            public void onProgress(final String... args) {
                mUiHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        String message = "";
                        for (String text : args) {
                            message += text;
                        }
                        updateNotifyView(message);
                    }
                });
            }
        };

        @Override
        protected void onPostExecute(Void result) {
            updateState(ZipUtils.State.Saved);
        }

        @Override
        protected void onCancelled() {
            updateState(ZipUtils.State.Cancel);
        }
    }
}
