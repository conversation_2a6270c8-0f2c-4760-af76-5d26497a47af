/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.widget;

import android.os.Build;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.CompoundButton.OnCheckedChangeListener;

import androidx.appcompat.widget.AppCompatCheckBox;

import com.tc.logclient.R;
import com.tc.logclient.config.ManualLogConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * The MultiSelectBox use to control a group of check boxes.
 */
public class MultiSelectBox extends AppCompatCheckBox implements OnClickListener,
        OnCheckedChangeListener {
    private static final String TAG = "MultiSelectBox";

    /**
     * The parent checkbox resource ID.
     */
    private int mViewGroupId;

    /**
     * The list for child check boxes.
     */
    private List<AppCompatCheckBox> mChildCheckBoxes;

    /**
     * Get view group for a MultiSelectBox.
     *
     * @param context the context
     * @param attrs   the attribute set
     */
    public MultiSelectBox(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray typesArray = context.obtainStyledAttributes(attrs, R.styleable.MultiSelectBox);
        mViewGroupId = typesArray.getResourceId(R.styleable.MultiSelectBox_childSelectBoxes, 0);
        typesArray.recycle();
    }

    /**
     * Init the MultiSelectBox.
     */
    public void init() {
        // Get child check box.
        ViewGroup viewGroup = getRootView().findViewById(mViewGroupId);
        mChildCheckBoxes = new ArrayList<AppCompatCheckBox>();
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof AppCompatCheckBox) {
                AppCompatCheckBox checkBox = (AppCompatCheckBox) view;
                mChildCheckBoxes.add(checkBox);
            }
        }

        // Init the parent check box state depending on the children
        updateParent();

        // set click/check listen
        setOnClickListener(this);
        setOnCheckedChangeListener(this);
        for (AppCompatCheckBox child : mChildCheckBoxes) {
            child.setOnClickListener(this);
            child.setOnCheckedChangeListener(this);
            if (child.getId() == R.id.kernel_log_checkbox) {
                child.setVisibility(Build.IS_USER ? View.GONE : View.VISIBLE);
            }
        }

        super.onAttachedToWindow();
    }

    /**
     * update UI.
     *
     * @param v checkbox
     */
    @Override
    public void onClick(View v) {
        if (v == this) {
            updateChildren();
        } else {
            updateParent();
        }
    }

    /**
     * update log argument.
     *
     * @param buttonView the widget that has been checked
     * @param isChecked checked or not
     */
    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.android_default_logs_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_DEFAULT_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_ANDROID_DEFAULT_LOGS);
                }
                break;
            case R.id.android_main_logs_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_MAIN_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_ANDROID_MAIN_LOGS);
                }
                break;
            case R.id.android_system_logs_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_SYSTEM_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_ANDROID_SYSTEM_LOGS);
                }
                break;
            case R.id.android_events_logs_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_EVENTS_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_ANDROID_EVENTS_LOGS);
                }
                break;
            case R.id.android_radio_logs_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_RADIO_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_ANDROID_RADIO_LOGS);
                }
                break;
            case R.id.android_crash_logs_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_CRASH_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_ANDROID_CRASH_LOGS);
                }
                break;
            case R.id.kernel_log_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_KERNEL_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_KERNEL_LOGS);
                }
                break;
            default:
                break;
        }
    }

    /**
     * Update the child check boxes status.
     */
    public void updateChildrenStatus(boolean enable) {
        for (AppCompatCheckBox child : mChildCheckBoxes) {
            child.setEnabled(enable);
        }
    }

    /**
     * Update the child check boxes.
     */
    private void updateChildren() {
        for (AppCompatCheckBox child : mChildCheckBoxes) {
            child.setChecked(isChecked());
        }
    }

    /**
     * Update the parent check box.
     */
    private void updateParent() {
        for (AppCompatCheckBox child : mChildCheckBoxes) {
            if (child.isChecked()) {
                setChecked(true);
                return;
            }
        }
        setChecked(false);
    }

    /**
     * update checkbox check or not.
     *
     * @param arg the android log arg
     */
    public void updateCheckBoxCheckableStatus(int arg) {
        ViewGroup viewGroup = getRootView().findViewById(mViewGroupId);
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof AppCompatCheckBox) {
                AppCompatCheckBox checkBox = (AppCompatCheckBox) view;
                switch (checkBox.getId()) {
                    case R.id.android_default_logs_checkbox:
                        checkBox.setChecked((arg & ManualLogConfig.FLAG_ANDROID_DEFAULT_LOGS) != 0);
                        break;
                    case R.id.android_main_logs_checkbox:
                        checkBox.setChecked((arg & ManualLogConfig.FLAG_ANDROID_MAIN_LOGS) != 0);
                        break;
                    case R.id.android_system_logs_checkbox:
                        checkBox.setChecked((arg & ManualLogConfig.FLAG_ANDROID_SYSTEM_LOGS) != 0);
                        break;
                    case R.id.android_events_logs_checkbox:
                        checkBox.setChecked((arg & ManualLogConfig.FLAG_ANDROID_EVENTS_LOGS) != 0);
                        break;
                    case R.id.android_radio_logs_checkbox:
                        checkBox.setChecked((arg & ManualLogConfig.FLAG_ANDROID_RADIO_LOGS) != 0);
                        break;
                    case R.id.android_crash_logs_checkbox:
                        checkBox.setChecked((arg & ManualLogConfig.FLAG_ANDROID_CRASH_LOGS) != 0);
                        break;
                    case R.id.kernel_log_checkbox:
                        checkBox.setChecked((arg & ManualLogConfig.FLAG_KERNEL_LOGS) != 0);
                        break;
                    default:
                        break;
                }
            }
        }
        updateParent();
    }
}
