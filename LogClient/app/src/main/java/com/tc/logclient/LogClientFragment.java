/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.app.AlertDialog;
import android.app.Fragment;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.PowerManager;
import android.os.SystemProperties;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.tc.logclient.loggers.AndroidLoggers;
import com.tc.logclient.loggers.BluetoothLogger;
import com.tc.logclient.loggers.FatalLogger;
import com.tc.logclient.loggers.QxdmLoggers;
import com.tc.logclient.loggers.TcpdumpLoggers;
import com.tc.logclient.utils.ZipUtils;
import com.tc.logclient.utils.ClearZipFilesUtils;
import com.tc.logclient.widget.MultiSelectBox;
import com.tc.logclient.config.ManualLogConfig;
import com.tc.logclient.utils.Logger;

public class LogClientFragment extends Fragment implements OnClickListener,
        OnCheckedChangeListener, KernelLogPresenter.IKernelLogView {
    private static final String TAG = "LogClientFragment";

    private static final String LOGSP = "logsp";
    private static final String LOGARGS = "logargs";
    private static final String LOGSTATUS = "logstatus";

    public static final int UPDATE_NOTIFY_TEXT = 1;
    public static final String UPDATE_NOTIFY_STRING = "update_notify_string";

    private static final String ZIP_FOLDER = "/sdcard/tlog/";

    Context mContext;
    // The root view of the layout.
    private View mRootView;

    // Checkbox
    private MultiSelectBox mMultiSelectBox;
    private CheckBox mBugreportCheckBox;
    private CheckBox mTcpdumpCheckBox;
    private CheckBox mQxdmCheckBox;
    private CheckBox mBluetoothCheckBox;

    // Status widget
    private TextView mStatusView;
    private ProgressBar mProgressBar;

    // Start/Stop button
    private Button mOperatingButton;

    // Current state
    boolean mLongingState = false;

    // For notification service
    private Intent mLogServiceIntent;

    private AndroidLoggers mAndroidLoggers;
    private TcpdumpLoggers mTcpdumpLoggers;
    private QxdmLoggers mQxdmLoggers;
    private FatalLogger mFatalLogger;
    private BluetoothLogger mBluetoothLogger;

    private TextView mNotifyTextView;
    private ZipUtils mZipUtils;
    private UiHandler mUiHandler;

    ShutdownBroadcastReceiver mShutdownBroadcastReceiver;
    private boolean mIsRegisted = false;

    private KernelLogPresenter mKernelLogPresenter;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);

        mContext = getActivity();
        mKernelLogPresenter = new KernelLogPresenter(this);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.activity_main, container, false);

        mOperatingButton = (Button) mRootView.findViewById(R.id.start_stop_button);
        mOperatingButton.setOnClickListener(this);

        mStatusView = (TextView) mRootView.findViewById(R.id.status_message);
        mProgressBar = (ProgressBar) mRootView.findViewById(R.id.progress_bar);

        mMultiSelectBox = (MultiSelectBox) mRootView.findViewById(R.id.android_logs);
        mMultiSelectBox.init();
        mMultiSelectBox.setEnabled(true);

        mTcpdumpCheckBox = (CheckBox) mRootView.findViewById(R.id.tcp_dump_checkbox);
        mQxdmCheckBox = (CheckBox) mRootView.findViewById(R.id.qxdm_checkbox);
        mTcpdumpCheckBox.setOnCheckedChangeListener(this);
        mQxdmCheckBox.setOnCheckedChangeListener(this);
        mBugreportCheckBox = (CheckBox) mRootView.findViewById(R.id.bugreport_checkbox);
        mBugreportCheckBox.setOnCheckedChangeListener(this);
        mBugreportCheckBox.setChecked(true);
        mBluetoothCheckBox = (CheckBox) mRootView.findViewById(R.id.bluetooth_checkbox);
        mBluetoothCheckBox.setOnCheckedChangeListener(this);

        mNotifyTextView = (TextView) mRootView.findViewById(R.id.log_tv);
        mNotifyTextView.setMovementMethod(ScrollingMovementMethod.getInstance());

        mAndroidLoggers = new AndroidLoggers(mContext);
        mTcpdumpLoggers = new TcpdumpLoggers(mContext);
        mQxdmLoggers = new QxdmLoggers(mContext);
        mFatalLogger = new FatalLogger(mContext);
        mBluetoothLogger = new BluetoothLogger(mContext);

        mUiHandler = new UiHandler();
        mZipUtils = new ZipUtils(mContext, mUiHandler);

        mZipUtils.loadAssets();
        mZipUtils.updateState(ZipUtils.State.Idle);

        return mRootView;
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.main_menu, menu);
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public void onPrepareOptionsMenu(Menu menu) {
        //kernel log menu item
        MenuItem item = menu.findItem(R.id.kernel_log_switch);
        int title = mKernelLogPresenter.isKernelLogEnable() ? R.string.kernel_log_turn_off
                : R.string.kernel_log_turn_on;
        item.setTitle(title);
        //diag menu item
        item = menu.findItem(R.id.diag_switch);
        boolean hasOpenPort = hasOpenDiagPort();
        title = hasOpenPort ? R.string.diag_turn_off : R.string.diag_turn_on;
        item.setTitle(title);
    }

    private boolean hasOpenDiagPort() {
        String configDiag = SystemProperties.get("sys.usb.config", "adb");
        return !TextUtils.isEmpty(configDiag) && configDiag.contains("diag");
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.delete_all:
                ClearZipFilesUtils clearZipFilesUtils = new ClearZipFilesUtils(mContext, ZIP_FOLDER);
                clearZipFilesUtils.createConfirmDialog();
                break;
            case R.id.menu_settings:
                ((LogClientActivity) mContext).showFragment(new MenuFragment(), true);
                break;
            case R.id.kernel_log_switch:
                AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
                builder.setMessage(R.string.kernel_log_dialog_reboot_msg);
                builder.setPositiveButton(R.string.dialog_btn_ok, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        boolean isKernelLogEnable = mKernelLogPresenter.isKernelLogEnable();
                        mKernelLogPresenter.switchKernelLog(!isKernelLogEnable);
                    }
                });
                builder.setNegativeButton(R.string.dialog_btn_cancel, null);
                builder.create().show();
                break;
            case R.id.diag_switch:
                if (hasOpenDiagPort()) {
                    SystemProperties.set("sys.usb.config", "adb");
                } else {
                    SystemProperties.set("sys.usb.config", "diag,serial_cdev,rmnet,dpl,qdss,adb");
                }
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public void onPause() {
        super.onPause();
        updateLogStatusToSp(getIsLogging());
    }

    @Override
    public void onResume() {
        super.onResume();
        SharedPreferences sp = mContext.getSharedPreferences(LOGSP, Context.MODE_PRIVATE);
        int arg = sp.getInt(LOGARGS, 0);
        boolean status = sp.getBoolean(LOGSTATUS, false);
        setIsLogging(status);
        if (status) {
            updateCheckBoxCheckableStatus(arg);
            updateUi();
            if (!mIsRegisted) {
                IntentFilter shutdownFilter = new IntentFilter();
                shutdownFilter.addAction(Intent.ACTION_SHUTDOWN);
                shutdownFilter.addAction(Intent.ACTION_REBOOT);
                mShutdownBroadcastReceiver = new ShutdownBroadcastReceiver();
                mContext.registerReceiver(mShutdownBroadcastReceiver, shutdownFilter,
                        null, mUiHandler);
                mIsRegisted = true;
            }

        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mZipUtils.stopTask();
        mKernelLogPresenter.destroy();
    }

    @Override
    public void onClick(View v) {
        //enterPictureInPictureMode();
        if (v.getId() == R.id.start_stop_button) {
            if (ManualLogConfig.getLogArgFlags() == 0) {
               Toast.makeText(mContext,
                       getText(R.string.must_select_message), Toast.LENGTH_SHORT).show();
               return;
            }
            mLongingState = !mLongingState;
            if (mLongingState) {
                startingLog();
                updateUi();
            } else {
                stoppingLog();
            }
        }
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.tcp_dump_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_TCP_DUMP);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_TCP_DUMP);
                }
                break;
            case R.id.qxdm_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_QXDM_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_QXDM_LOGS);
                }
                break;
            case R.id.bugreport_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BUGREPORT);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_BUGREPORT);
                }
                break;
            case R.id.bluetooth_checkbox:
                if (isChecked) {
                    ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BLUETOOTH_LOGS);
                } else {
                    ManualLogConfig.clearLogArgFlags(ManualLogConfig.FLAG_BLUETOOTH_LOGS);
                }
                break;
            default:
                break;
        }
    }

    private void updateLogStatusToSp(boolean status) {
        SharedPreferences sp = mContext.getSharedPreferences(LOGSP, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        editor.putInt(LOGARGS, ManualLogConfig.getLogArgFlags());
        editor.putBoolean(LOGSTATUS, status);
        editor.commit();
    }

    private void updateUi() {
        mOperatingButton.setClickable(true);
        mOperatingButton.setEnabled(true);

        if (isAdded()) {
            if (getIsLogging()) {
                mOperatingButton.setText(getText(R.string.button_stop));
                mStatusView.setText(R.string.logging_starting);
                mProgressBar.setVisibility(View.VISIBLE);
                updateCheckBoxEnableStatus(false);
            } else if (mZipUtils.isRunning()) {
                mOperatingButton.setText(getText(R.string.button_stop));
                mStatusView.setText(R.string.logging_starting);
                mProgressBar.setVisibility(View.VISIBLE);
                mOperatingButton.setEnabled(false);
                updateCheckBoxEnableStatus(false);
            } else {
                mOperatingButton.setText(getText(R.string.button_start));
                mStatusView.setText(R.string.logging_stopped);
                mProgressBar.setVisibility(View.GONE);
                updateCheckBoxEnableStatus(true);
            }
        }
    }

    private void updateCheckBoxEnableStatus(boolean enable) {
        mMultiSelectBox.setEnabled(enable);
        // Don't update android checkboxes enable status.
        // mMultiSelectBox.updateChildrenStatus(enable);
        mBugreportCheckBox.setEnabled(enable);
        mTcpdumpCheckBox.setEnabled(enable);
        mQxdmCheckBox.setEnabled(enable);
        mBluetoothCheckBox.setEnabled(enable);
    }

    private void updateCheckBoxCheckableStatus(int arg) {
        if ((arg & ManualLogConfig.FLAG_ANDROID_LOG) != 0) {
            mMultiSelectBox.updateCheckBoxCheckableStatus(arg);
        }
        if ((arg & ManualLogConfig.FLAG_BUGREPORT) != 0) {
            mBugreportCheckBox.setChecked(true);
        }
        if ((arg & ManualLogConfig.FLAG_TCP_DUMP) != 0) {
            mTcpdumpCheckBox.setChecked(true);
        }
        if ((arg & ManualLogConfig.FLAG_QXDM_LOGS) != 0) {
            mQxdmCheckBox.setChecked(true);
        }
        if ((arg & ManualLogConfig.FLAG_BLUETOOTH_LOGS) != 0) {
            mBluetoothCheckBox.setChecked(true);
        }
    }

    /**
     * The log is catching or not.
     *
     * @return status
     */
    public boolean getIsLogging() {
        return mLongingState;
    }

    /**
     * The log is catching or not.
     *
     * @return status
     */
    private void setIsLogging(boolean status) {
        mLongingState = status;
    }

    /**
     * Start to take log.
     */
    public void startingLog() {
        if (getIsLogging()) {
            mZipUtils.updateState(ZipUtils.State.Collecting);

            if (mLogServiceIntent == null) {
                mLogServiceIntent = new Intent(mContext, LogService.class);
            }
            mContext.startService(mLogServiceIntent);

            // Start take android log
            if (ManualLogConfig.needAndroidLog()) {
                mAndroidLoggers.startingLog();
            }

            // Start take tcpdump
            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_TCP_DUMP) != 0x0) {
                mTcpdumpLoggers.startingLog();
            }

            // Start take qxdm
            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_QXDM_LOGS) != 0x0) {
                mQxdmLoggers.startingLog();
            }

            // Start take bluetooth
            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_BLUETOOTH_LOGS) != 0x0) {
                mBluetoothLogger.startingLog();
            }

            refreshLogView("");
        }
        if (!mIsRegisted) {
            IntentFilter shutdownFilter = new IntentFilter();
            shutdownFilter.addAction(Intent.ACTION_SHUTDOWN);
            shutdownFilter.addAction(Intent.ACTION_REBOOT);
            mShutdownBroadcastReceiver = new ShutdownBroadcastReceiver();
            mContext.registerReceiver(mShutdownBroadcastReceiver, shutdownFilter,
                    null, mUiHandler);
            mIsRegisted = true;
        }

    }

    /**
     * Stop the current log.
     */
    public void stoppingLog() {
        mZipUtils.updateState(ZipUtils.State.Collected);

        if (mLogServiceIntent != null) {
            mContext.stopService(mLogServiceIntent);
        }

        // Stop take android log
        if (mAndroidLoggers != null) {
            mAndroidLoggers.stoppingLog();
        }

        // Stop take tcpdump
        if (mTcpdumpLoggers != null) {
            mTcpdumpLoggers.stoppingLog();
        }

        // Stop take QXDM
        if (mQxdmLoggers != null) {
            mQxdmLoggers.stoppingLog();
        }

        // Start Bluetooth logger
        if (mBluetoothLogger != null) {
            mBluetoothLogger.stoppingLog();
        }

        mZipUtils.saveLog();
        if (mIsRegisted) {
            try {
                mContext.unregisterReceiver(mShutdownBroadcastReceiver);
                mIsRegisted = false;
            } catch (Exception ex) {
                mIsRegisted = false;
                Logger.logW(TAG, "Broadcast unregiste failed:" + ex.toString());
            }
        }
    }

    void refreshLogView(String msg) {
        if (msg.isEmpty()) {
            mNotifyTextView.setText(msg);
        } else {
            mNotifyTextView.append(msg);
            int offset = mNotifyTextView.getLineCount() * mNotifyTextView.getLineHeight();
            if (offset > mNotifyTextView.getHeight()) {
                mNotifyTextView.scrollTo(0, offset - mNotifyTextView.getHeight());
            }
        }
    }

    @Override
    public void reboot() {
        PowerManager powerManager = (PowerManager) getContext().getSystemService(Context.POWER_SERVICE);
        powerManager.reboot(null);
    }

    @Override
    public void showToast(String msg) {
        Toast.makeText(getContext(), msg, Toast.LENGTH_SHORT).show();
    }

    public class UiHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case UPDATE_NOTIFY_TEXT:
                    Bundle bundle = msg.getData();
                    String str = bundle.getString(UPDATE_NOTIFY_STRING);
                    refreshLogView(str);
                    updateUi();
                    break;
                default:
                    break;
            }
        }
    }

    public class ShutdownBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            stoppingLog();
            setIsLogging(false);
            updateLogStatusToSp(false);
        }
    }
}
