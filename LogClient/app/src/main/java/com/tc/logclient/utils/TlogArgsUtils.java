/*
 * Copyright © 2021, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.utils;

import android.text.TextUtils;

import java.io.Closeable;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * write the tlog args to each file.
 */
public class TlogArgsUtils {
    public static final String TAG = "ProcessUtils";

    /**
     * private constructor for util.
     */
    private TlogArgsUtils() {

    }

    /**
     * set the tlog args.
     *
     * @param filePath save the args file path.
     * @param args     the args value.
     * @return 0 set success,-1 fail.
     */
    public static int setTlogArgs(String filePath, String args) {
        if (TextUtils.isEmpty(args)) {
            return -1;
        }
        FileOutputStream fos = null;
        try {
            //false is don't append
            fos = new FileOutputStream(filePath, false);
            fos.write(args.getBytes());
            return 0;
        } catch (IOException exception) {
            exception.printStackTrace();
        } finally {
            silentClose(fos);
        }
        return -1;
    }

    /**
     * silent close the stream.
     *
     * @param closeable the will close stream.
     */
    private static void silentClose(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException exception) {
                exception.printStackTrace();
            }
        }
    }
}
