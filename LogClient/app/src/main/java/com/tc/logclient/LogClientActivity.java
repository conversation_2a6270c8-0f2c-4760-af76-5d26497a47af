/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;

import android.app.Fragment;

import androidx.appcompat.app.AppCompatActivity;
import android.app.FragmentTransaction;

import com.tc.logclient.utils.Logger;

public class LogClientActivity extends AppCompatActivity {
    private static final String TAG = LogClientActivity.class.getSimpleName();

    private Context mContext;
    SharedPreferences mSharedPrefs;

    public Fragment mLogClientFragment;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (savedInstanceState == null) {
            mLogClientFragment = new LogClientFragment();
            showFragment(mLogClientFragment, false);
        }
        mContext = getApplicationContext();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
    }

    @Override
    public void onResume() {
        super.onResume();

        // 处理自动启动/停止日志收集的Intent
        handleAutoLogControl();
    }

    /**
     * 处理自动日志控制
     */
    private void handleAutoLogControl() {
        Intent intent = getIntent();
        if (intent != null) {
            boolean autoStart = intent.getBooleanExtra("auto_start_logging", false);
            boolean autoStop = intent.getBooleanExtra("auto_stop_logging", false);

            if (autoStart) {
                Logger.logI(TAG, "Auto starting log collection");
                if (mLogClientFragment instanceof LogClientFragment) {
                    ((LogClientFragment) mLogClientFragment).autoStartLogging();
                }
                // 清除Intent中的标志，避免重复触发
                intent.removeExtra("auto_start_logging");
            } else if (autoStop) {
                Logger.logI(TAG, "Auto stopping log collection");
                if (mLogClientFragment instanceof LogClientFragment) {
                    ((LogClientFragment) mLogClientFragment).autoStopLogging();
                }
                // 清除Intent中的标志，避免重复触发
                intent.removeExtra("auto_stop_logging");
            }
        }
    }

    @Override
    public void onBackPressed() {
        Fragment fragment = getCurrentFragment();
        if (!(fragment instanceof OnBackPressedListener)
                || !((OnBackPressedListener)fragment).onBackPressed()) {
            super.onBackPressed();
        }
    }

    /**
     * Show the main/menu fragment.
     * @param fragment the fragment need to show.
     * @param addToBackStack need to add to stack or not.
     */
    public void showFragment(Fragment fragment, boolean addToBackStack) {
        FragmentTransaction ft = getFragmentManager().beginTransaction();
        ft.replace(android.R.id.content, fragment);
        if (addToBackStack) {
            ft.addToBackStack(null);
        }
        ft.commit();
    }

    private Fragment getCurrentFragment() {
        return getFragmentManager().findFragmentById(android.R.id.content);
    }

    interface OnBackPressedListener {
        public boolean onBackPressed();
    }
}
