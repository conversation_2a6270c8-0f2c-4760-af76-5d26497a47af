/*
 * Copyright © 2021, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;


public class KernelLogPresenter {
    private KernelLogModel mModel;
    private IKernelLogView mView;

    public KernelLogPresenter(IKernelLogView view) {
        mModel = new KernelLogModel();
        mView = view;
    }

    public interface IKernelLogView {
        void reboot();

        void showToast(String msg);
    }

    public boolean isKernelLogEnable() {
        return mModel.isKernelLogEnable();
    }

    public void destroy() {
        //release the view reference.
        mView = null;
    }

    public boolean switchKernelLog(boolean enable) {
        boolean result = mModel.switchKernelLog(enable);
        if (result) {
            if (mView != null) {
                mView.reboot();
            }
        } else {
            if (mView != null) {
                mView.showToast("set Fail");
            }
        }
        return result;
    }

    public static class KernelLogModel {
        /**
         * Number of bytes read or written to the kernel log.
         */
        private static final int KERNEL_LOG_SIZE = 4;
        /**
         * the ite of nv.
         */
        private static final int KERNEL_LOG_ITEM = 23;
        /**
         * 1 is enable kernel log.
         */
        private static final int KERNEL_LOG_ENABLE = 1;
        /**
         * 0 is disable kernel log.
         */
        private static final int KERNEL_LOG_DISABLE = 0;

        /**
         * check the kernel log whether enable.
         *
         * @return true is enable.
         */
        public boolean isKernelLogEnable() {
            return KERNEL_LOG_ENABLE == 1;
        }

        /**
         * switch kernel log.
         *
         * @param enable false is disable,true is eanble.
         * @return true is set success.
         */
        public boolean switchKernelLog(boolean enable) {
            int value = enable ? KERNEL_LOG_ENABLE : KERNEL_LOG_DISABLE;
            return value == 1;
        }
    }
}

