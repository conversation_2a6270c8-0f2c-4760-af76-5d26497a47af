/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Notification.Builder;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.tc.logclient.config.ManualLogConfig;
import com.tc.logclient.loggers.AndroidLoggers;
import com.tc.logclient.loggers.BluetoothLogger;
import com.tc.logclient.loggers.QxdmLoggers;
import com.tc.logclient.loggers.TcpdumpLoggers;
import com.tc.logclient.utils.Logger;

public class LogService extends Service {
    public static final String TAG = "LogService";

    public static final int NOTIFY_ID = 0;

    // 广播Action
    private static final String ACTION_LOG_CONTROL = "vendor.thundercomm.logclient.LOG_CONTROL";

    // 参数常量
    private static final String EXTRA_LOG_OPERATION = "log_operation";
    private static final int LOG_OPERATION_START = 1;
    private static final int LOG_OPERATION_STOP = 0;

    NotificationManager mNotifyManager;
    Context mContext;

    // 日志收集器
    private AndroidLoggers mAndroidLoggers;
    private TcpdumpLoggers mTcpdumpLoggers;
    private QxdmLoggers mQxdmLoggers;
    private BluetoothLogger mBluetoothLogger;

    // 广播接收器
    private LogControlReceiver mLogControlReceiver;

    // 日志收集状态
    private boolean mIsLogging = false;

    @Override
    public void onCreate() {
        mContext = getApplication().getApplicationContext();
        mNotifyManager = (NotificationManager) mContext.getSystemService(Context.NOTIFICATION_SERVICE);

        // 初始化日志收集器
        initLoggers();

        // 注册广播接收器
        registerLogControlReceiver();

        createNewNotification();
    }

    @Override
    public void onDestroy() {
        // 停止日志收集
        stopLogCollection();

        // 注销广播接收器
        unregisterLogControlReceiver();

        dismissNotification();

        super.onDestroy();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.logD(TAG, "onStartCommand");
        return START_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * Pop up a notification when start logging.
     */
    public void createNewNotification() {
        Logger.logD(TAG, "createNewNotification");

        NotificationChannel channel = new NotificationChannel(getString(R.string.channel_id),
                getString(R.string.channel_title),
                NotificationManager.IMPORTANCE_DEFAULT);
        mNotifyManager.createNotificationChannel(channel);

        PendingIntent mainAction = null;
        Intent startIntent = new Intent(Intent.ACTION_MAIN);
        startIntent.setClass(mContext, LogClientActivity.class);
        startIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        mainAction = PendingIntent.getActivity(mContext, 0, startIntent, PendingIntent.FLAG_IMMUTABLE);
        Builder builder = new Builder(mContext, getString(R.string.channel_id));
        builder.setProgress(0, 0, false);
        builder.setContentTitle(getString(R.string.builder_id))
                .setContentText(getString(R.string.builder_content))
                .setOngoing(true)
                .setContentIntent(mainAction)
                .setSmallIcon(android.R.drawable.ic_media_play);

        mNotifyManager.notify(NOTIFY_ID, builder.build());
    }

    /**
     * Dismiss the notification.
     */
    public void dismissNotification() {
        mNotifyManager.cancel(NOTIFY_ID);
    }

    /**
     * 初始化日志收集器
     */
    private void initLoggers() {
        mAndroidLoggers = new AndroidLoggers(mContext);
        mTcpdumpLoggers = new TcpdumpLoggers(mContext);
        mQxdmLoggers = new QxdmLoggers(mContext);
        mBluetoothLogger = new BluetoothLogger(mContext);
    }

    /**
     * 注册广播接收器
     */
    private void registerLogControlReceiver() {
        if (mLogControlReceiver == null) {
            mLogControlReceiver = new LogControlReceiver();
            IntentFilter filter = new IntentFilter(ACTION_LOG_CONTROL);
            registerReceiver(mLogControlReceiver, filter);
            Logger.logI(TAG, "LogControlReceiver registered");
        }
    }

    /**
     * 注销广播接收器
     */
    private void unregisterLogControlReceiver() {
        if (mLogControlReceiver != null) {
            unregisterReceiver(mLogControlReceiver);
            mLogControlReceiver = null;
            Logger.logI(TAG, "LogControlReceiver unregistered");
        }
    }

    /**
     * 开始日志收集
     */
    private void startLogCollection() {
        if (mIsLogging) {
            Logger.logI(TAG, "Log collection is already running");
            return;
        }

        Logger.logI(TAG, "Starting log collection");

        try {
            // 设置所有日志类型为启用状态
            ManualLogConfig.clearLogArgFlags(0xFFFFFFFF); // 清除所有标志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_ANDROID_LOG); // 添加Android日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_KERNEL_LOGS); // 添加内核日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_TCP_DUMP); // 添加TCP dump
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_QXDM_LOGS); // 添加QXDM日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BLUETOOTH_LOGS); // 添加蓝牙日志
            ManualLogConfig.addLogArgFlags(ManualLogConfig.FLAG_BUGREPORT); // 添加Bugreport

            // 启动各种日志收集器
            if (ManualLogConfig.needAndroidLog()) {
                mAndroidLoggers.startingLog();
            }

            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_TCP_DUMP) != 0x0) {
                mTcpdumpLoggers.startingLog();
            }

            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_QXDM_LOGS) != 0x0) {
                mQxdmLoggers.startingLog();
            }

            if ((ManualLogConfig.getLogArgFlags() & ManualLogConfig.FLAG_BLUETOOTH_LOGS) != 0x0) {
                mBluetoothLogger.startingLog();
            }

            mIsLogging = true;
            Logger.logI(TAG, "Log collection started successfully");

        } catch (Exception e) {
            Logger.logE(TAG, "Failed to start log collection", e);
        }
    }

    /**
     * 停止日志收集
     */
    private void stopLogCollection() {
        if (!mIsLogging) {
            Logger.logI(TAG, "Log collection is not running");
            return;
        }

        Logger.logI(TAG, "Stopping log collection");

        try {
            // 停止各种日志收集器
            if (mAndroidLoggers != null) {
                mAndroidLoggers.stoppingLog();
            }

            if (mTcpdumpLoggers != null) {
                mTcpdumpLoggers.stoppingLog();
            }

            if (mQxdmLoggers != null) {
                mQxdmLoggers.stoppingLog();
            }

            if (mBluetoothLogger != null) {
                mBluetoothLogger.stoppingLog();
            }

            mIsLogging = false;
            Logger.logI(TAG, "Log collection stopped successfully");

        } catch (Exception e) {
            Logger.logE(TAG, "Failed to stop log collection", e);
        }
    }

    /**
     * 广播接收器类
     */
    private class LogControlReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                Logger.logW(TAG, "Received null intent");
                return;
            }

            String action = intent.getAction();
            if (TextUtils.isEmpty(action)) {
                Logger.logW(TAG, "Received intent with empty action");
                return;
            }

            Logger.logD(TAG, "Received broadcast with action: " + action);

            if (!ACTION_LOG_CONTROL.equals(action)) {
                Logger.logW(TAG, "Unknown action: " + action);
                return;
            }

            int operation = intent.getIntExtra(EXTRA_LOG_OPERATION, -1);
            Logger.logI(TAG, "Log operation: " + operation);

            switch (operation) {
                case LOG_OPERATION_START:
                    startLogCollection();
                    break;
                case LOG_OPERATION_STOP:
                    stopLogCollection();
                    break;
                default:
                    Logger.logW(TAG, "Unknown operation: " + operation);
                    break;
            }
        }
    }
}
