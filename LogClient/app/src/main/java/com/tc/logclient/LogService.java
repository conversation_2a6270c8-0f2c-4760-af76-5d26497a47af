/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Notification.Builder;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.tc.logclient.utils.Logger;

public class LogService extends Service {
    public static final String TAG = "LogService";

    public static final int NOTIFY_ID = 0;

    NotificationManager mNotifyManager;
    Context mContext;

    @Override
    public void onCreate() {
        mContext = getApplication().getApplicationContext();
        mNotifyManager = (NotificationManager) mContext.getSystemService(Context.NOTIFICATION_SERVICE);

        createNewNotification();
    }

    @Override
    public void onDestroy() {
        dismissNotification();

        super.onDestroy();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.logD(TAG, "onStartCommand");
        return START_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * Pop up a notification when start logging.
     */
    public void createNewNotification() {
        Logger.logD(TAG, "createNewNotification");

        NotificationChannel channel = new NotificationChannel(getString(R.string.channel_id),
                getString(R.string.channel_title),
                NotificationManager.IMPORTANCE_DEFAULT);
        mNotifyManager.createNotificationChannel(channel);

        PendingIntent mainAction = null;
        Intent startIntent = new Intent(Intent.ACTION_MAIN);
        startIntent.setClass(mContext, LogClientActivity.class);
        startIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        mainAction = PendingIntent.getActivity(mContext, 0, startIntent, PendingIntent.FLAG_IMMUTABLE);
        Builder builder = new Builder(mContext, getString(R.string.channel_id));
        builder.setProgress(0, 0, false);
        builder.setContentTitle(getString(R.string.builder_id))
                .setContentText(getString(R.string.builder_content))
                .setOngoing(true)
                .setContentIntent(mainAction)
                .setSmallIcon(android.R.drawable.ic_media_play);

        mNotifyManager.notify(NOTIFY_ID, builder.build());
    }

    /**
     * Dismiss the notification.
     */
    public void dismissNotification() {
        mNotifyManager.cancel(NOTIFY_ID);
    }
}
