/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.utils;

import android.content.Context;
import android.content.res.AssetManager;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class ResourceUtils {

    /**
     * loadAssetsFile.
     */
    public static void loadAssetsFile(Context context, String name) throws IOException {
        AssetManager assets = context.getResources().getAssets();
        CloseableHelper helper = new CloseableHelper();
        File outputFile = new File(context.getFilesDir(), name);

        try {
            InputStream in = helper.add(assets.open(name));
            OutputStream out = helper.add(new BufferedOutputStream(new FileOutputStream(outputFile)));

            byte[] buff = new byte[in.available()];
            in.read(buff);
            out.write(buff);
        } finally {
            helper.closeAll();
        }

        outputFile.setReadable(true, false);
        outputFile.setExecutable(true, false);
    }

}
