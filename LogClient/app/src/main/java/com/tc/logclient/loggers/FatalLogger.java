package com.tc.logclient.loggers;

import android.content.Context;
import android.os.SystemProperties;

public class Fatal<PERSON>ogger extends BaseLoggers {
    public static final String TAG = "FatalLogger";

    Context mContext;

    // This property is start/stop service tlogFatal.
    private static final String SYS_TLOG_FATAL_ACTIVE = "sys.tlog.fatal.active";

    /**
     * Create a new Loggers.
     *
     * @param context the application context.
     */
    public FatalLogger(Context context) {
        super(context);
        mContext = context;
    }

    /**
     * Starting Bugreport.
     */
    @Override
    protected boolean start() {
        SystemProperties.set(SYS_TLOG_FATAL_ACTIVE, "1");
        return true;
    }

    /**
     * Stopping Bugreport.
     */
    @Override
    protected boolean stop() {
        SystemProperties.set(SYS_TLOG_FATAL_ACTIVE, "0");
        return true;
    }

    /**
     * Check Bugreport status.
     *
     * @return true if log is taking.
     */
    @Override
    public boolean isTakingLogging() {
        return true;
    }
}
