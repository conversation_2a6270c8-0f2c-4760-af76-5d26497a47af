/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.loggers;

import android.content.Context;
import android.os.SystemProperties;

import com.tc.logclient.config.ManualLogConfig;
import com.tc.logclient.utils.Logger;
import com.tc.logclient.utils.TlogArgsUtils;

public class AndroidLoggers extends BaseLoggers {
    public static final String TAG = "AndroidLoggers";

    Context mContext;

    // This property is start/stop service tlogManual.
    private static final String SYS_TLOG_ANDROID_ACTIVE = "sys.tlog.android.active";
    // This property is set for tcpdump's parameter.
    private static final String SYS_TLOG_ANDROID_ARG = "sys.tlog.android.arg";

    public AndroidLoggers(Context context) {
        super(context);
        mContext = context;
    }

    /**
     * Starting Android Log.
     */
    @Override
    protected boolean start() {
        Logger.logD(TAG,"startingLog");
        //SystemProperties.set(SYS_TLOG_ANDROID_ARG, Integer.toHexString(ManualLogConfig.getLogArgFlags()));
        int result = TlogArgsUtils.setTlogArgs(getArgsFilePath(),
                Integer.toHexString(ManualLogConfig.getLogArgFlags()));
        if (result < 0) {
            Logger.logW(TAG, "manual save params error.");
        }
        SystemProperties.set(SYS_TLOG_ANDROID_ACTIVE, "1");
        return true;
    }

    @Override
    protected String getArgsFilePath() {
        return "/data/tlog/args/manual.arg";
    }

    /**
     * Stopping Android Log.
     */
    @Override
    protected boolean stop() {
        Logger.logD(TAG,"stopingLog");
        SystemProperties.set(SYS_TLOG_ANDROID_ACTIVE, "0");
        return true;
    }

    /**
     * Check Android logger status.
     *
     * @return true if log is taking.
     */
    @Override
    public boolean isTakingLogging() {
        return true;
    }
}
