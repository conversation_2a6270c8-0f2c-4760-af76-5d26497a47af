/*
 * Copyright © 2020, The Thundercomm. All rights reserved.
 */

package com.tc.logclient.loggers;

import android.content.Context;
import android.os.SystemProperties;

import com.tc.logclient.R;
import com.tc.logclient.utils.Logger;
import com.tc.logclient.utils.TlogArgsUtils;

public class TcpdumpLoggers extends BaseLoggers {
    public static final String TAG = "TcpdumpLoggers";

    Context mContext;

    // This property is start/stop service tlog_tcpdump.
    private static final String SYS_TLOG_TCPDUMP_ACTIVE = "sys.tlog.tcpdump.active";
    // This property is set for tcpdump's parameter.
    private static final String SYS_TLOG_TCPDUMP_ARG = "sys.tlog.tcpdump.arg";
    // This is default value for tcpdump.
    private static final String TLOG_TCPDUMP_DEFAULT_ARG = "-i any -s 0 -U -w";

    public TcpdumpLoggers(Context context) {
        super(context);
        mContext = context;
    }

    /**
     * Starting tcpdump.
     */
    @Override
    protected boolean start() {
        Logger.logD(TAG, "startingLog");
        String tcpdumpArg = mSharePreferences.getString(mContext.getString(R.string.tcpdump_parameter_key),
                TLOG_TCPDUMP_DEFAULT_ARG);
        //SystemProperties.set(SYS_TLOG_TCPDUMP_ARG, tcpdumpArg);
        int result = TlogArgsUtils.setTlogArgs(getArgsFilePath(), tcpdumpArg);
        if (result < 0) {
            Logger.logW(TAG, "tcpdump save params error.");
        }
        SystemProperties.set(SYS_TLOG_TCPDUMP_ACTIVE, "1");
        return true;
    }

    @Override
    protected String getArgsFilePath() {
        return "/data/tlog/args/tcpdump.arg";
    }

    /**
     * Stopping tcpdump.
     */
    @Override
    protected boolean stop() {
        Logger.logD(TAG, "stopingLog");
        SystemProperties.set(SYS_TLOG_TCPDUMP_ACTIVE, "0");
        return true;
    }

    /**
     * Check tcpdump logger status.
     *
     * @return true if log is taking.
     */
    @Override
    public boolean isTakingLogging() {
        return true;
    }
}
