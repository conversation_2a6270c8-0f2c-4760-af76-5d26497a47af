package com.tc.logclient.utils;

import android.util.Log;

public final class Logger {
    private static String PREFIX_TAG = "TlogClient_";

    public static void logV(String tag, String msg) {
        Log.v(PREFIX_TAG + tag, msg);
    }

    public static void logV(String tag, String msg, Throwable ex) {
        Log.v(PREFIX_TAG + tag, msg, ex);
    }

    public static void logD(String tag, String msg) {
        Log.d(PREFIX_TAG + tag, msg);
    }

    public static void logD(String tag, String msg, Throwable ex) {
        Log.d(PREFIX_TAG + tag, msg, ex);
    }

    public static void logI(String tag, String msg) {
        Log.i(PREFIX_TAG + tag, msg);
    }

    public static void logI(String tag, String msg, Throwable ex) {
        Log.i(PREFIX_TAG + tag, msg, ex);
    }

    public static void logW(String tag, String msg) {
        Log.w(PREFIX_TAG + tag, msg);
    }

    public static void logW(String tag, String msg, Throwable ex) {
        Log.w(PREFIX_TAG + tag, msg, ex);
    }

    public static void logE(String tag, String msg) {
        Log.e(PREFIX_TAG + tag, msg);
    }

    public static void logE(String tag, String msg, Exception ex) {
        Log.e(PREFIX_TAG + tag, msg, ex);
    }

    public static void wtf(String tag, String msg) {
        Log.wtf(PREFIX_TAG + tag, msg);
    }

    public static void wtf(String tag, String msg, Throwable ex) {
        Log.wtf(PREFIX_TAG + tag, msg, ex);
    }

}