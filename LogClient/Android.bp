// Copyright © 2020, The Thundercomm. All rights reserved.

android_app {
    name: "LogClient",
    static_libs: [
        "androidx.appcompat_appcompat",
    ],
    manifest: "app/src/main/AndroidManifest.xml",
    asset_dirs: ["app/src/main/assets"],
    resource_dirs: [
        "app/src/main/res/",
    ],
    srcs: [
        "app/src/**/*.java",
    ],
    platform_apis: true,
    certificate: "platform",

    dex_preopt: {
        enabled: false,
    },
}

android_app {
    name: "LogClientUser",
    static_libs: [
        "androidx.appcompat_appcompat",
    ],
    manifest: "app/src/main/AndroidManifestUser.xml",
    asset_dirs: ["app/src/main/assets"],
    resource_dirs: [
        "app/src/main/res/",
    ],
    srcs: [
        "app/src/**/*.java",
    ],
    platform_apis: true,
    certificate: "platform",

    dex_preopt: {
        enabled: false,
    },
}
