#!/bin/bash

# LogClient日志控制测试脚本
# 使用方法: ./test_log_control.sh [start|stop|test_eventprovider]

echo "=== LogClient日志控制测试脚本 ==="

case "$1" in
    "start")
        echo "发送开始日志收集广播..."
        adb shell am broadcast -a "vendor.thundercomm.logclient.LOG_CONTROL" --ei log_operation 1 -n com.tc.logclient/.LogControlReceiver
        echo "开始广播已发送"
        ;;
    "stop")
        echo "发送停止日志收集广播..."
        adb shell am broadcast -a "vendor.thundercomm.logclient.LOG_CONTROL" --ei log_operation 0 -n com.tc.logclient/.LogControlReceiver
        echo "停止广播已发送"
        ;;
    "test_eventprovider")
        echo "测试通过EventProvider触发日志控制..."
        echo "发送开始操作..."
        adb shell am broadcast -a "yellowstone.system.SAVE_LOGS" --es operation "start"
        sleep 3
        echo "发送停止操作..."
        adb shell am broadcast -a "yellowstone.system.SAVE_LOGS" --es operation "stop"
        echo "EventProvider测试完成"
        ;;
    "check_logs")
        echo "检查日志输出..."
        adb logcat -s LogControlReceiver:* LogClientFragment:* SaveLogsController:* -d
        ;;
    "check_files")
        echo "检查SD卡Logs目录..."
        adb shell ls -la /sdcard/Logs/
        ;;
    *)
        echo "使用方法:"
        echo "  $0 start              - 直接发送开始日志收集广播"
        echo "  $0 stop               - 直接发送停止日志收集广播"
        echo "  $0 test_eventprovider - 通过EventProvider测试完整流程"
        echo "  $0 check_logs         - 查看相关日志输出"
        echo "  $0 check_files        - 查看SD卡Logs目录文件"
        echo ""
        echo "示例:"
        echo "  $0 start"
        echo "  $0 stop"
        echo "  $0 test_eventprovider"
        ;;
esac
