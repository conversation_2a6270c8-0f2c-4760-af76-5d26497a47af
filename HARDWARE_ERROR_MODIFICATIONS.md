# 硬件错误处理修改说明

## 📋 **修改概述**

根据提供的硬件错误规格表，对EventProvider进行了以下关键修改：

## 🔧 **主要修改内容**

### 1. **修正IMU传感器的HW ID映射**

**问题**: 原代码中G Sensor和Gyro Sensor共用同一个HW ID (3)
**解决**: 按照规格表分离为独立的HW ID

**修改文件**: `app/src/main/java/com/thundercomm/eventprovider/anno/EventType.java`

```java
// 修改前
public static final int IMU = 3; // Shared by G Sensor and Gyro Sensor

// 修改后
public static final int G_SENSOR = 3; // G Sensor (IMU)
public static final int GYRO_SENSOR = 4; // Gyro Sensor (IMU)
```

**影响的枚举定义**:
```java
// 修改前
G_SENSOR_UNAVAILABLE(HwId.IMU, ErrorCode.G_SENSOR_UNAVAILABLE, HwErrorCode.G_SENSOR_UNAVAILABLE),
GYRO_SENSOR_UNAVAILABLE(HwId.IMU, ErrorCode.GYRO_SENSOR_UNAVAILABLE, HwErrorCode.GYRO_SENSOR_UNAVAILABLE),

// 修改后
G_SENSOR_UNAVAILABLE(HwId.G_SENSOR, ErrorCode.G_SENSOR_UNAVAILABLE, HwErrorCode.G_SENSOR_UNAVAILABLE),
GYRO_SENSOR_UNAVAILABLE(HwId.GYRO_SENSOR, ErrorCode.GYRO_SENSOR_UNAVAILABLE, HwErrorCode.GYRO_SENSOR_UNAVAILABLE),
```

### 2. **实现ErrorInfoSender广播功能**

**新增文件**: `app/src/main/java/com/thundercomm/eventprovider/send/ErrorInfoSender.java`

**功能**: 发送错误信息广播，符合客户要求的格式

**广播格式**:
- **Action**: `"yellowstone.ssolapp.ERROR_INFO"`
- **参数**:
  - `KEY_ERROR_CODE`: HW Error Code (String)
  - `KEY_ERROR_TIME`: 发生时间 (YYYYMMDDhhmmss)
  - `KEY_ERROR_SUMMARY`: 错误摘要 (当前为空字符串)

**核心方法**:
```java
public void sendErrorInfo(int hwErrorCode, String errorSummary)
public void sendErrorInfo(String hwErrorCode, String errorSummary)
```

### 3. **更新RuntimeErrorListener**

**修改文件**: `app/src/main/java/com/thundercomm/eventprovider/listener/RuntimeErrorListener.java`

**主要变更**:

#### 3.1 更新事件类型数组
```java
// 修改前
EventType.HardwareRuntimeError.HwId.IMU,

// 修改后  
EventType.HardwareRuntimeError.HwId.G_SENSOR,
EventType.HardwareRuntimeError.HwId.GYRO_SENSOR,
```

#### 3.2 集成ErrorInfoSender
- 添加ErrorInfoSender成员变量
- 在onEventReceived方法中初始化并调用广播
- 所有硬件错误都会触发广播

#### 3.3 调整LED优先级映射
根据规格表重新分类错误优先级：

**高优先级 (DEVICE_ERROR)**:
- MCU_NO_RESPONSE (最关键的系统错误)

**中优先级 (DEVICE_WARNING)**:
- G_SENSOR_UNAVAILABLE, GYRO_SENSOR_UNAVAILABLE
- 所有摄像头错误 (FRONT/INCABIN/REAR/OPTION)
- GNSS, BT, WiFi, I/F Box Unit错误
- 电池和温度错误

**特殊处理**:
- LTE相关错误保持原有的上下文感知处理逻辑

## 🎯 **规格表对照**

| 硬件单元 | HW ID | Error Code | HW Error Code | LED优先级 | 广播 |
|---------|-------|------------|---------------|-----------|------|
| MCU | 01 | 3 | 013 | 高 (ERROR) | ✅ |
| LED | 02 | 1 | 021 | 高 (ERROR) | ✅ |
| G Sensor | 03 | 1 | 031 | 中 (WARNING) | ✅ |
| Gyro Sensor | 04 | 1 | 041 | 中 (WARNING) | ✅ |
| Front Camera | 05 | 1/2 | 051/052 | 中 (WARNING) | ✅ |
| Incabin Camera | 06 | 1/2 | 061/062 | 中 (WARNING) | ✅ |
| Rear Camera | 07 | 0/1/2 | 070/071/072 | 中 (WARNING) | ✅ |
| Option Camera | 08 | 0/1/2 | 080/081/082 | 中 (WARNING) | ✅ |
| SD Card | 09 | 0/1/2 | 090/091/092 | 高 (ERROR) | ✅ |
| LTE Module | 10 | 0/1/2 | 100/101/102 | 特殊处理 | ✅ |
| GNSS | 11 | 1/2 | 111/112 | 中 (WARNING) | ✅ |
| BT | 12 | 1 | 121 | 中 (WARNING) | ✅ |
| WiFi | 13 | 1 | 131 | 中 (WARNING) | ✅ |
| I/F Box Unit | 14 | 1 | 141 | 中 (WARNING) | ✅ |
| Temperature | 15 | 5 | 155 | 中 (WARNING) | ✅ |
| Voltage | 16 | 4 | 164 | 中 (WARNING) | ✅ |

## ⚠️ **暂未处理的项目**

1. **Battery high voltage** - 客户尚未提供具体的Error Code和HW Error Code
2. **LTE功能** - 按照现有逻辑处理，等待客户进一步需求

## ✅ **验证要点**

1. **HW ID映射正确性**: G Sensor (3), Gyro Sensor (4)
2. **广播功能**: 所有硬件错误都会发送广播
3. **LED优先级**: 摄像头错误从ERROR降级为WARNING
4. **向后兼容**: 现有功能保持不变

## 🔄 **后续建议**

1. **测试验证**: 建议编写单元测试验证广播功能
2. **集成测试**: 验证LED优先级调整的效果
3. **文档更新**: 更新相关API文档和用户手册
